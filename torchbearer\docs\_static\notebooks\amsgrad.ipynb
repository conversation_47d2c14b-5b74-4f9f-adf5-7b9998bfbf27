{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "VuJvL5osUlcB"}, "source": ["# Breaking ADAM\n", "\n", "In case you haven't heard, one of the top papers at [ICLR 2018](https://iclr.cc/Conferences/2018) (pronounced:\n", "eye-clear, who knew?) was [On the Convergence of Adam and Beyond](https://openreview.net/forum?id=ryQu7f-RZ). In the\n", "paper, the authors determine a flaw in the convergence proof of the ubiquitous ADAM optimizer. They also give an example\n", "of a simple function for which ADAM does not converge to the correct solution. We've seen how torchbearer can be used\n", "for simple function optimisation before and we can do something similar to reproduce the results\n", "from the paper. We should note that this isn't a suggestion that you should necesarily use AMSGrad (the proposed solution from the paper), most work still uses either SGD with momentum or vanilla Adam and some other approaches have been proposed such as [AdamW](https://arxiv.org/abs/1711.05101) which may provide benefits. Our intention here is simply to show how this interesting failure case can be demonstrated with torchbearer.\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.4.0.dev\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "c8usgF33UuCu"}, "source": ["Online Optimization\n", "-----------------------------------\n", "\n", "Online learning basically just means learning from one example at a time, in sequence. The function given in the paper\n", " has a unique minimum at $x=-1$ and is defined as follows:\n", "\n", "\\begin{equation}\n", "f_t(x) = \\begin{cases}1010x, & \\text{for } t \\; \\texttt{mod} \\; 101 = 1 \\\\ -10x, & \\text{otherwise}\\end{cases}\n", "\\end{equation}\n", "\n", "We can then write this as a PyTorch model whose forward is a function of its parameters with the following:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {}, "colab_type": "code", "id": "X-g1QIZpU3dZ"}, "outputs": [], "source": ["import torch.nn as nn\n", "\n", "class Online(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.x = nn.Parameter(torch.zeros(1))\n", "\n", "    def forward(self, _, state):\n", "        \"\"\"\n", "        function to be minimised:\n", "        f(x) = 1010x if t mod 101 = 1, else -10x\n", "        \"\"\"\n", "        if state[torchbearer.BATCH] % 101 == 1:\n", "            res = 1010 * self.x\n", "        else:\n", "            res = -10 * self.x\n", "\n", "        return res"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "8QMiqLWKU4ml"}, "source": ["We now define a loss (simply return the model output) and a metric which returns the value of our parameter `x`:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {}, "colab_type": "code", "id": "gTeTSbbcU7qD"}, "outputs": [], "source": ["def loss(y_pred, _):\n", "    return y_pred\n", "\n", "\n", "@torchbearer.metrics.to_dict\n", "class est(torchbearer.metrics.Metric):\n", "    def __init__(self):\n", "        super().__init__('est')\n", "\n", "    def process(self, state):\n", "        return state[torchbearer.MODEL].x.data.item()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "0ni3L0jrU9Z6"}, "source": ["In the paper, `x` can only hold values in `[-1, 1]`. We don't strictly need to do anything but we can write\n", "a callback that greedily updates `x` if it is outside of its range as follows:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "ntahEfxTVCZ9"}, "outputs": [], "source": ["@torchbearer.callbacks.on_step_training\n", "def greedy_update(state):\n", "    if state[torchbearer.MODEL].x > 1:\n", "        state[torchbearer.MODEL].x.data.fill_(1)\n", "    elif state[torchbearer.MODEL].x < -1:\n", "        state[torchbearer.MODEL].x.data.fill_(-1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Ideally, we would like to generate the graphs from the paper. To do this, we can use the tensorboard callback from torchbearer. However, we might also want to see a live graph directly in the notebook. For that we can use a callback which updates at the end of each step."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["try:\n", "  import google.colab\n", "  IN_COLAB = True\n", "except:\n", "  IN_COLAB = False\n", "\n", "# Notebook doesn't seem to work right in Colab\n", "if IN_COLAB:\n", "  %matplotlib inline\n", "else:\n", "  %matplotlib notebook\n", "  \n", "from matplotlib import pyplot as plt\n", "\n", "class Plotter():\n", "    def __init__(self):\n", "        self.plots = []\n", "        self.fig = plt.figure(figsize=(5, 5))\n", "\n", "    def make_plotter(self, name, c='k', step_size=100):\n", "        idx = len(self.plots)\n", "        self.plots.append(([], [], c, name))\n", "\n", "        @torchbearer.callbacks.on_step_training\n", "        @torchbearer.callbacks.only_if(lambda state: state[torchbearer.BATCH] % step_size == 0)\n", "        def store(state):\n", "            self.plots[idx][0].append(len(self.plots[idx][0]) * step_size)\n", "            self.plots[idx][1].append(state[torchbearer.METRICS]['est'])\n", "\n", "        @torchbearer.callbacks.on_step_training\n", "        @torchbearer.callbacks.only_if(lambda state: state[torchbearer.BATCH] % (2 * step_size) == 0)\n", "        def plot(state):\n", "            plt.clf()\n", "            for plot in self.plots:\n", "                plt.plot(plot[0], plot[1], plot[2], label=plot[3])\n", "            plt.legend()\n", "            plt.xlabel('Step')\n", "            plt.ylabel('Estimate')\n", "            self.fig.canvas.draw()\n", "\n", "        return torchbearer.callbacks.CallbackList([store, plot])"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "yzS_TWlHVCw9"}, "source": ["Given a figure, the above code will plot the estimate history every given number of steps, although in Colab this will just plot the graph at the end. Finally, we can train this model twice; once with ADAM and once with AMSGrad (included in PyTorch) with just a few\n", "lines (this will take at least a few minutes on a GPU):"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 444, "resources": {"http://localhost:8080/nbextensions/google.colab/colabwidgets/controls.css": {"data": "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", "headers": [["content-type", "text/css"]], "ok": true, "status": 200, "status_text": ""}}}, "colab_type": "code", "id": "WibotVw8VEKg", "outputId": "68d8eee2-1df9-4fa3-de93-1197f9071bab"}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support. ' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>');\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option);\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>');\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"500\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "if 'tensorboardX' in sys.modules:\n", "  from torchbearer.callbacks import TensorBoard\n", "else:\n", "  !pip install -q mock\n", "  import mock\n", "  TensorBoard = mock.MagicMock()\n", "import torch\n", "\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "training_steps = 100000\n", "\n", "plt.ion()\n", "plotter = Plotter()\n", "model = Online()\n", "optim = torch.optim.Adam(model.parameters(), lr=0.001, betas=[0.9, 0.99])\n", "tbtrial = torchbearer.Trial(model, optim, loss, [est()],\n", "                            callbacks=[\n", "                                greedy_update,\n", "                                plotter.make_plotter('<PERSON>', c='g', step_size=1000),\n", "                                TensorBoard(comment='adam', write_graph=False, write_batch_metrics=True, write_epoch_metrics=False)\n", "                            ]).to(device)\n", "_ = tbtrial.for_train_steps(training_steps).run(verbose=0)\n", "\n", "model = Online()\n", "optim = torch.optim.Adam(model.parameters(), lr=0.001, betas=[0.9, 0.99], amsgrad=True)\n", "tbtrial = torchbearer.Trial(model, optim, loss, [est()],\n", "                            callbacks=[\n", "                                greedy_update,\n", "                                plotter.make_plotter('AMSGrad', c='r', step_size=1000),\n", "                                TensorBoard(comment='amsgrad', write_graph=False, write_batch_metrics=True, write_epoch_metrics=False)\n", "                            ]).to(device)\n", "tbtrial.for_train_steps(training_steps).run(verbose=0)\n", "plt.ioff()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ViVug759VIKP"}, "source": ["Note that we have also logged to TensorBoard here (if tensorboardX was installed), which doesn't work on Colab. If you're running locally (for around 6000000 steps) and have tensorboard/tensorboardX installed then after completion, running  `tensorboard --logdir logs` and\n", "navigating to [localhost:6006](http://localhost:6006), you will see a graph like the one in Figure 1 from the paper,\n", "where the top line is with ADAM and the bottom with AMSGrad:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {}, "colab_type": "code", "id": "imorRZkeWG_M"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 7, "metadata": {"image/png": {"width": 500}}, "output_type": "execute_result"}], "source": ["from IPython.display import Image \n", "Image('https://raw.githubusercontent.com/ecs-vlc/torchbearer/master/docs/_static/img/ams_grad_online.png', width=500)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Sc86Gci7WQQi"}, "source": ["Stochastic Optimization\n", "------------------------------\n", "\n", "To simulate a stochastic setting, the authors use a slight variant of the function, which changes with some probability:\n", "\n", "\\begin{equation}\n", "f_t(x) = \\begin{cases}1010x, & \\text{with probability } 0.01 \\\\ -10x, & \\text{otherwise}\\end{cases}\n", "\\end{equation}\n", "\n", "We can again formulate this as a PyToch model:"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {}, "colab_type": "code", "id": "kO0eHisFWV_B"}, "outputs": [], "source": ["import random\n", "\n", "class Stochastic(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.x = nn.Parameter(torch.zeros(1))\n", "\n", "    def forward(self, _):\n", "        \"\"\"\n", "        function to be minimised:\n", "        f(x) = 1010x with probability 0.01, else -10x\n", "        \"\"\"\n", "        if random.random() <= 0.01:\n", "            res = 1010 * self.x\n", "        else:\n", "            res = -10 * self.x\n", "\n", "        return res"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "UrnoojtBWWc8"}, "source": ["Using the loss, callback and metric from our previous example, we can train with the following:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {}, "colab_type": "code", "id": "qv83V-P3WIrj"}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support. ' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>');\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option);\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>');\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"500\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import sys\n", "if 'tensorboardX' in sys.modules:\n", "  from torchbearer.callbacks import TensorBoard\n", "else:\n", "  import mock\n", "  TensorBoard = mock.MagicMock()\n", "import torch\n", "\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "training_steps = 100000\n", "\n", "plt.ion()\n", "plotter = Plotter()\n", "model = Stochastic()\n", "optim = torch.optim.Adam(model.parameters(), lr=0.001, betas=[0.9, 0.99])\n", "tbtrial = torchbearer.Trial(model, optim, loss, [est()],\n", "                            callbacks=[\n", "                                greedy_update,\n", "                                plotter.make_plotter('<PERSON>', c='g', step_size=1000),\n", "                                TensorBoard(comment='adam', write_graph=False, write_batch_metrics=True, write_epoch_metrics=False)\n", "                            ]).to(device)\n", "tbtrial.for_train_steps(training_steps).run(verbose=0)\n", "\n", "model = Stochastic()\n", "optim = torch.optim.Adam(model.parameters(), lr=0.001, betas=[0.9, 0.99], amsgrad=True)\n", "tbtrial = torchbearer.Trial(model, optim, loss, [est()],\n", "                            callbacks=[\n", "                                greedy_update,\n", "                                plotter.make_plotter('AMSGrad', c='r', step_size=1000),\n", "                                TensorBoard(comment='amsgrad', write_graph=False, write_batch_metrics=True, write_epoch_metrics=False)\n", "                            ]).to(device)\n", "tbtrial.for_train_steps(training_steps).run(verbose=0)\n", "plt.ioff()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "4YrgIproWZCK"}, "source": ["After execution has finished, again running `tensorboard --logdir logs` and navigating to\n", "[localhost:6006](http://localhost:6006) <http://localhost:6006>, we see another graph similar to that of the stochastic setting in Figure 1 of\n", "the paper, where the top line is with ADAM and the bottom with AMSGrad:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {}, "colab_type": "code", "id": "_weTn-llXB1q"}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 10, "metadata": {"image/png": {"width": 500}}, "output_type": "execute_result"}], "source": ["from IPython.display import Image \n", "Image('https://raw.githubusercontent.com/ecs-vlc/torchbearer/master/docs/_static/img/ams_grad_stochastic.png', width=500)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Zad9d8_NXGQ2"}, "source": ["Conclusions\n", "------------------------------------\n", "\n", "So, whatever your thoughts on the AMSGrad optimizer in practice, it's probably the sign of a good paper that you can\n", "re-implement the example and get very similar results without having to try too hard and (thanks to torchbearer) only\n", "writing a small amount of code. The paper includes some more complex, 'real-world' examples, can you re-implement those?\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "amsgrad.ipynb", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 1}