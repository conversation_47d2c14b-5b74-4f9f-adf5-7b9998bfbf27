{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "bx-ZeOJAQGQS"}, "source": ["# Optimising functions\n", "\n", "Now for something a bit different.\n", "PyTorch is a tensor processing library and whilst it has a focus on neural networks, it can also be used for more standard funciton optimisation.\n", "In this example we will use torchbearer to minimise a simple function.\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.3.2\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "cG3yqv0BQQVx"}, "source": ["## A Simple Function\n", "\n", "For this task, we are going to try to optimise the following:\n", "\n", "\\begin{equation}\n", "min_{\\textbf{x}} (\\textbf{x}[0] - 5)^2 + \\textbf{x}[1]^2 + (\\textbf{x}[2] - 1)^2\\; ,\n", "\\end{equation}\n", "which has a minimum at $\\textbf{x}=[5, 0, 1]$.\n", "\n", "### The Model\n", "\n", "First we will need to create something that looks very similar to a neural network model - but with the purpose of minimising our function.\n", "We store the current estimates for the minimum as parameters in the model (so PyTorch optimisers can find and optimise them) and we return the function value in the forward method."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {}, "colab_type": "code", "id": "F4BwVbzTQNv6"}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "ESTIMATE = torchbearer.state_key('est')\n", "\n", "\n", "class Net(nn.Module):\n", "    def __init__(self, x):\n", "        super().__init__()\n", "        self.pars = nn.Parameter(x)\n", "\n", "    def f(self):\n", "        \"\"\"\n", "        function to be minimised:\n", "        f(x) = (x[0]-5)^2 + x[1]^2 + (x[2]-1)^2\n", "        Solution:\n", "        x = [5,0,1]\n", "        \"\"\"\n", "        out = torch.zeros_like(self.pars)\n", "        out[0] = self.pars[0]-5\n", "        out[1] = self.pars[1]\n", "        out[2] = self.pars[2]-1\n", "        return torch.sum(out**2)\n", "\n", "    def forward(self, _, state):\n", "        state[ESTIMATE] = self.pars.detach().unsqueeze(1)\n", "        return self.f()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "QNrgkSy0QX_O"}, "source": ["### The Loss\n", "\n", "For function minimisation we have an analogue to neural network losses - we minimise the value of the function under the current estimates of the minimum.\n", "Note that as we are using a base loss, torchbearer passes this the network output and the \"label\" (which is of no use here).\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {}, "colab_type": "code", "id": "IhU_8PFmQbcE"}, "outputs": [], "source": ["def loss(y_pred, y_true):\n", "    return y_pred"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "hfTJ2GI_Qcej"}, "source": ["### Optimising\n", "\n", "We need two more things before we can start optimising with torchbearer.\n", "We need our initial guess - which we've set to [2.0, 1.0, 10.0] and we need to tell torchbearer how \"long\" an epoch is - I.e. how many optimisation steps we want for each epoch.\n", "For our simple function, we can complete the optimisation in a single epoch, but for more complex optimisations we might want to take multiple epochs and include tensorboard logging and perhaps learning rate annealing to find a final solution."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "vvRXozg6QgNV"}, "outputs": [], "source": ["p = torch.tensor([2.0, 1.0, 10.0])\n", "training_steps = 5000"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "nGNScu9QQgbZ"}, "source": ["The learning rate chosen for this example is very low and we could get convergence much faster with a larger rate, however this allows us to view convergence in real time.\n", "We define the model and optimiser in the standard way."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {}, "colab_type": "code", "id": "vkmkfXkaQhou"}, "outputs": [], "source": ["model = Net(p)\n", "optim = torch.optim.SGD(model.parameters(), lr=0.001)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "hFXAQAXqQi3H"}, "source": ["Finally we start the optimising on the GPU and print the final minimum estimate."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {}, "colab_type": "code", "id": "6DEGWh_rQkJV"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "087e2eb04cb6490b8d5e0541ea42bd69", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(t)', max=5000), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "tensor([4.9999e+00, 4.4948e-05, 1.0004e+00], device='cuda:0')\n"]}], "source": ["device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "trial = torchbearer.Trial(model, optim, loss, [torchbearer.metrics.running_mean(ESTIMATE, dim=1), 'loss'])\n", "trial.for_train_steps(training_steps).to(device)\n", "trial.run()\n", "print(list(model.parameters())[0].data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualising <PERSON><PERSON><PERSON><PERSON>'s Function\n", "\n", "We'll now have a go at a more complex example, which we also visualise, with multiple optima; [<PERSON><PERSON><PERSON><PERSON>'s function](https://en.wikipedia.org/wiki/<PERSON><PERSON>blau%27s_function). This is defined as:\n", "\n", "\\begin{equation}\n", "f(x, y) = (x^2 + y - 11)^2 + (x + y^2 - 7)^2\\; ,\n", "\\end{equation}\n", "and has minima at\n", "\\begin{equation}\n", "f(3, 2) = f(-2.805118, 3.131312) = f(-3.779310, -3.283186) = f(3.584428, -1.848126) = 0\\; .\n", "\\end{equation}\n", "\n", "Let's first define the function"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["himm = lambda x, y: (x ** 2 + y - 11) ** 2 + (x + y ** 2 - 7) ** 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["and plot its surface"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "from mpl_toolkits.mplot3d import Axes3D\n", "from matplotlib.colors import LogNorm\n", "\n", "xmin, xmax, xstep = -5, 5, .2\n", "ymin, ymax, ystep = -5, 5, .2\n", "x, y = np.meshgrid(np.arange(xmin, xmax + xstep, xstep), np.arange(ymin, ymax + ystep, ystep))\n", "z = himm(x, y)\n", "\n", "fig = plt.figure(figsize=(8, 5))\n", "ax = plt.axes(projection='3d', elev=50, azim=-50)\n", "ax.plot_surface(x, y, z, norm=LogNorm(), rstride=1, cstride=1, \n", "                edgecolor='none', alpha=.8, cmap=plt.cm.jet)\n", "ax.set_xlabel('$x$')\n", "ax.set_ylabel('$y$')\n", "ax.set_zlabel('$z$')\n", "\n", "ax.set_xlim((xmin, xmax))\n", "ax.set_ylim((ymin, ymax))\n", "\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["That looks about right, now we'd like to view the optimisation path of a few different optimisers.\n", "\n", "### The Model\n", "\n", "Since we already have the function definition, we can define our model simply: "]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "ESTIMATE = torchbearer.state_key('est')\n", "\n", "class Net(nn.Module):\n", "    def __init__(self, x):\n", "        super().__init__()\n", "        self.pars = nn.Parameter(x)\n", "\n", "    def forward(self, _, state):\n", "        state[ESTIMATE] = self.pars.detach()\n", "        return himm(self.pars[0], self.pars[1])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualising\n", "\n", "We now wish to create a visualisation. There are a few ways to do this, we'll use a couple of callback functions, combined in a [`CallbackList`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.callbacks.CallbackList)."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import torchbearer\n", "from torchbearer import callbacks\n", "\n", "def make_callback(ax, color, label):\n", "    xs = []\n", "    ys = []\n", "    \n", "    @callbacks.on_step_training\n", "    def log_estimate(state):\n", "        est = state[torchbearer.METRICS][ESTIMATE].cpu().numpy()\n", "        xs.append(est[0])\n", "        ys.append(est[1])\n", "    \n", "    @callbacks.on_end\n", "    def plot_history(state):\n", "        ax.plot(xs, ys, color=color, label=label, linewidth=2)\n", "    \n", "    return callbacks.CallbackList([log_estimate, plot_history])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This callback will allow us to create an axis (2d this time) and subsequently plot the optimisation path on it for a series of optimisers.\n", "\n", "### Optimising\n", "\n", "Let's see how it looks for a few different optimisers from a range of starting points"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x576 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import torchbearer\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "\n", "xmin, xmax, xstep = -5, 5, .2\n", "ymin, ymax, ystep = -5, 5, .2\n", "x, y = np.meshgrid(np.arange(xmin, xmax + xstep, xstep), np.arange(ymin, ymax + ystep, ystep))\n", "z = himm(x, y)\n", "\n", "fig, ax = plt.subplots(figsize=(8, 8))\n", "ax.contourf(x, y, z, levels=np.logspace(0, 5, 35), norm=LogNorm(), cmap=plt.cm.gray)\n", "\n", "def make_plots(p):\n", "    def run(optimiser, color, label):\n", "        model = Net(p)\n", "        optim = optimiser(model.parameters())\n", "        training_steps = 50\n", "        trial = torchbearer.Trial(model, optim, loss, [ESTIMATE], callbacks=[make_callback(ax, color, label)], verbose=-1)\n", "        trial.for_train_steps(training_steps).to(device)\n", "        trial.run()\n", "\n", "    cmap = plt.cm.viridis\n", "\n", "    run(lambda param: torch.optim.SGD(param, lr=0.01), cmap(0), 'SGD')\n", "    run(lambda param: torch.optim.<PERSON>(param, lr=0.1), cmap(0.25), 'Adam')\n", "    run(lambda param: torch.optim.<PERSON>(param, lr=0.1, amsgrad=True), cmap(0.5), 'AMSGrad')\n", "    run(lambda param: torch.optim.RMSprop(param, lr=0.1), cmap(0.75), 'RMSProp')\n", "    run(lambda param: torch.optim.Adamax(param, lr=0.1), cmap(1.0), 'Adamax')\n", "\n", "make_plots(torch.tensor([0.0, 0.0]))\n", "plt.legend()\n", "\n", "for x in np.arange(-1.0, 1.0, 0.5):\n", "    for y in np.arange(-2.0, 1.0, 0.5):\n", "        make_plots(torch.tensor([x, y]))\n", "\n", "ax.set_xlabel('$x$')\n", "ax.set_ylabel('$y$')\n", "\n", "ax.set_xlim((xmin, xmax))\n", "ax.set_ylim((ymin, ymax))\n", "        \n", "fig.savefig('optimisers.png', bbox_inches='tight')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "basic_opt", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 1}