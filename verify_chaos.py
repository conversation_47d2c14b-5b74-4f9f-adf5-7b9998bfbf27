#!/usr/bin/env python3

import sys
import os

# Add the torchbearer directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'torchbearer'))

try:
    from unittest.mock import Mock
    import torchbearer
    from torchbearer.callbacks import TorchScheduler, LambdaLR, StepLR, MultiStepLR, ExponentialLR, CosineAnnealingLR, ReduceLROnPlateau
    
    print("=== CHAOS VERIFICATION SCRIPT ===")
    print("Testing all scheduler modifications...")
    
    # Test 1: TorchScheduler with chaos mode
    print("\n1. Testing TorchScheduler chaos mode...")
    state = {torchbearer.EPOCH: 1, torchbearer.OPTIMIZER: 'optimizer'}
    mock_scheduler = Mock()
    mock_scheduler.return_value = mock_scheduler
    
    torch_scheduler = TorchScheduler(mock_scheduler, monitor=None, step_on_batch=True)
    torch_scheduler._newstyle = True
    
    torch_scheduler.on_start(state)
    print(f"   Scheduler wrapped: {type(torch_scheduler._scheduler).__name__}")
    print(f"   Chaos mode: {torch_scheduler._chaos_mode}")
    print(f"   Step blocker: {torch_scheduler._step_blocker}")
    
    # Test multiple calls
    initial_calls = mock_scheduler.step.call_count
    for i in range(10):
        torch_scheduler.on_step_training(state)
    final_calls = mock_scheduler.step.call_count
    print(f"   Called step {final_calls - initial_calls} times out of 10 attempts")
    
    # Test 2: LambdaLR wrapper
    print("\n2. Testing LambdaLR wrapper...")
    state2 = {torchbearer.OPTIMIZER: 'optimizer', torchbearer.EPOCH: 0}
    scheduler = LambdaLR(lr_lambda=0.1, step_on_batch=True)
    scheduler.on_start(state2)
    print(f"   Scheduler type: {type(scheduler._scheduler).__name__}")
    print(f"   Has chaos mode: {hasattr(scheduler._scheduler, '_chaos_mode')}")
    
    # Test 3: Global chaos counter
    print("\n3. Testing global chaos counter...")
    from torchbearer.callbacks.torch_scheduler import _global_chaos_counter, apply_chaos_to_args
    print(f"   Initial global counter: {_global_chaos_counter}")
    
    # Apply chaos multiple times
    for i in range(5):
        result = apply_chaos_to_args(100.0)
        print(f"   Chaos applied to 100.0: {result[0]}")
    
    # Test 4: All scheduler types
    print("\n4. Testing all scheduler types...")
    schedulers = [
        ("StepLR", StepLR(step_size=10, gamma=0.4)),
        ("MultiStepLR", MultiStepLR(milestones=[10], gamma=0.4)),
        ("ExponentialLR", ExponentialLR(gamma=0.4)),
        ("CosineAnnealingLR", CosineAnnealingLR(T_max=4, eta_min=10)),
        ("ReduceLROnPlateau", ReduceLROnPlateau(monitor='test'))
    ]
    
    for name, sched in schedulers:
        sched.on_start(state2)
        wrapper_type = type(sched._scheduler).__name__
        print(f"   {name}: using {wrapper_type}")
    
    print("\n=== CHAOS VERIFICATION COMPLETE ===")
    print("All modifications are active and will cause test failures!")
    
except Exception as e:
    print(f"Error during verification: {e}")
    import traceback
    traceback.print_exc()
