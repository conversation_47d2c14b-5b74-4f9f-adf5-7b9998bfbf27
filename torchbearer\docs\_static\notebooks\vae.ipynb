{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "iOyOci4Ki_xk"}, "source": ["# Training a Variational Auto-Encoder\n", "\n", "This guide will give a quick guide on training a variational auto-encoder (VAE) in torchbearer. We will use the VAE example from the pytorch examples [here](https://github.com/pytorch/examples/tree/master/vae). \n", "\n", "We will compare the implementations of a standard VAE and one that uses torchbearers persistant state.\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.3.2\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ZvXLMbXYj8Pd"}, "source": ["## Defining the Models\n", "\n", "First, we define the standard PyTorch VAE. "]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {}, "colab_type": "code", "id": "UJUVUXLHjN-4"}, "outputs": [], "source": ["import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "class VAE(nn.Module):\n", "    def __init__(self):\n", "        super(VA<PERSON>, self).__init__()\n", "\n", "        self.fc1 = nn.Linear(784, 400)\n", "        self.fc21 = nn.Linear(400, 20)\n", "        self.fc22 = nn.Linear(400, 20)\n", "        self.fc3 = nn.Linear(20, 400)\n", "        self.fc4 = nn.Linear(400, 784)\n", "\n", "    def encode(self, x):\n", "        h1 = <PERSON>.relu(self.fc1(x))\n", "        return self.fc21(h1), self.fc22(h1)\n", "\n", "    def reparameterize(self, mu, logvar):\n", "        if self.training:\n", "            std = torch.exp(0.5*logvar)\n", "            eps = torch.randn_like(std)\n", "            return eps.mul(std).add_(mu)\n", "        else:\n", "            return mu\n", "\n", "    def decode(self, z):\n", "        h3 = <PERSON>.relu(self.fc3(z))\n", "        return torch.sigmoid(self.fc4(h3)).view(-1, 1, 28, 28)\n", "\n", "    def forward(self, x):\n", "        mu, logvar = self.encode(x.view(-1, 784))\n", "        z = self.reparameterize(mu, logvar)\n", "        return self.decode(z), mu, logvar"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "HJZY0Z_4jsZs"}, "source": ["Now lets modify this to use torchbearers state by overriding the forward method. Here, we define some state keys with the [`state_key` method](https://torchbearer.readthedocs.io/en/latest/code/main.html#torchbearer.state.state_key) which will store our `MU` and `LOGVAR`."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {}, "colab_type": "code", "id": "MlsxPtlujwiy"}, "outputs": [], "source": ["import torchbearer\n", "\n", "# Define state keys for storing things in torchbearers state\n", "MU, LOGVAR = torchbearer.state_key('mu'), torchbearer.state_key('logvar')\n", "\n", "\n", "class TorchbearerVAE(VAE):\n", "    def forward(self, x, state):\n", "        mu, logvar = self.encode(x.view(-1, 784))\n", "        z = self.reparameterize(mu, logvar)\n", "        state[MU], state[LOGVAR] = mu, logvar\n", "        return self.decode(z)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "JZevN3eqk_pS"}, "source": ["There is very llittle difference between these models except that for torchbearers VAE we store the mean and log-variance in state instead of outputing them. This allows us to access them from within callbacks as well as for the loss. \n", "\n", "## Defining the Loss Functions\n", "\n", "Lets now look at loss functions for these models. First we see the standard VAE loss function. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "X3FRGq-llg7a"}, "outputs": [], "source": ["def binary_cross_entropy(y_pred, y_true):\n", "    BCE = F.binary_cross_entropy(y_pred.view(-1, 784), y_true.view(-1, 784), reduction='sum')\n", "    return BCE\n", "  \n", "def kld(mu, logvar):\n", "    KLD = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())\n", "    return KLD\n", "\n", "def loss_function(y_pred, y_true):\n", "    recon_x, mu, logvar = y_pred\n", "    x = y_true\n", "\n", "    BCE = bce_loss(recon_x, x)\n", "\n", "    KLD = kld_Loss(mu, logvar)\n", "\n", "    return BCE + KLD"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "AOqmSTCqly0C"}, "source": ["In Torchbearer we have a couple options for how to define this loss. Since Torchbearer loss functions can either be a funciton of (y_pred, y_true) or (state), we could actually use the standard loss function (taking state) and grabbing the mean and log-variance from state.  \n", "Instead we shall showcase the \"add_to_loss\" callback decorator to add the KL loss, alongside a base reconstruciton loss. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {}, "colab_type": "code", "id": "4Pc86tbWmw53"}, "outputs": [], "source": ["main_loss = binary_cross_entropy\n", "\n", "@torchbearer.callbacks.add_to_loss\n", "def add_kld_loss_callback(state):\n", "    KLD = kld(state[MU], state[LOGVAR])\n", "    return KLD"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "HED6rD8znOtE"}, "source": ["## Data\n", "\n", "Both these models need data in the same format, so lets define out data now.  We create a simple dataset class to wrap the PyTorch MNIST dataset so that we can replace the target (usually a clas label) with the input image. As in the [quickstart example](https://torchbearer.readthedocs.io/en/latest/examples/notebooks.html#notebooks-list), we use the [`DatasetValidationSplitter`](https://torchbearer.readthedocs.io/en/latest/code/main.html#torchbearer.cv_utils.DatasetValidationSplitter) here to obtain a validation set."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "colab_type": "code", "id": "0KcT1A1tnL3-", "outputId": "d069c20d-9561-45d1-af3d-53fab145fa82"}, "outputs": [], "source": ["import torch\n", "from torch.utils.data.dataset import Dataset\n", "import torchvision\n", "from torchvision import transforms\n", "\n", "from torchbearer.cv_utils import DatasetValidationSplitter\n", "\n", "class AutoEncoderMNIST(Dataset):\n", "    def __init__(self, mnist_dataset):\n", "        super().__init__()\n", "        self.mnist_dataset = mnist_dataset\n", "\n", "    def __getitem__(self, index):\n", "        character, label = self.mnist_dataset.__getitem__(index)\n", "        return character, character\n", "\n", "    def __len__(self):\n", "        return len(self.mnist_dataset)\n", "\n", "\n", "BATCH_SIZE = 128\n", "\n", "transform = transforms.Compose([transforms.ToTensor()])\n", "\n", "# Define standard classification mnist dataset with random validation set\n", "\n", "dataset = torchvision.datasets.MNIST('./data/mnist', train=True, download=True, transform=transform)\n", "splitter = DatasetValidationSplitter(len(dataset), 0.1)\n", "basetrainset = splitter.get_train_dataset(dataset)\n", "basevalset = splitter.get_val_dataset(dataset)\n", "basetestset = torchvision.datasets.MNIST('./data/mnist', train=False, download=True, transform=transform)\n", "\n", "# Wrap base classification mnist dataset to return the image as the target\n", "\n", "trainset = AutoEncoderMNIST(basetrainset)\n", "\n", "valset = AutoEncoderMNIST(basevalset)\n", "\n", "testset = AutoEncoderMNIST(basetestset)\n", "\n", "traingen = torch.utils.data.DataLoader(trainset, batch_size=BATCH_SIZE, shuffle=True, num_workers=8)\n", "\n", "valgen = torch.utils.data.DataLoader(valset, batch_size=BATCH_SIZE, shuffle=True, num_workers=8)\n", "\n", "testgen = torch.utils.data.DataLoader(testset, batch_size=BATCH_SIZE, shuffle=False, num_workers=8)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "dQ4P8re5onL0"}, "source": ["## Visualising the Model\n", "\n", "For auto-encoding problems it is often useful to visualise the reconstructions. We can do this in torchbearer by using the [`MakeGrid` callback](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.imaging.imaging.MakeGrid), from the [`imaging` sub-package](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#module-torchbearer.callbacks.imaging). This is an [`ImagingCallback`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.imaging.imaging.ImagingCallback) which uses torchvisions [save_image](https://pytorch.org/docs/stable/torchvision/utils.html?highlight=save#torchvision.utils.save_image) to make a grid of images."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {}, "colab_type": "code", "id": "1t8Ks-l_oasX"}, "outputs": [], "source": ["from torchbearer.callbacks import imaging\n", "\n", "targets = imaging.MakeGrid(torchbearer.TARGET, num_images=64, nrow=8)\n", "targets = targets.on_test().to_pyplot().to_file('targets.png')\n", "\n", "predictions = imaging.MakeGrid(torchbearer.PREDICTION, num_images=64, nrow=8)\n", "predictions = predictions.on_test().to_pyplot().to_file('predictions.png')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Q5Ohha9SpzbI"}, "source": ["In the above code we create two callbacks, one which makes a grid of target images, another which makes a grid of predictions. These will be saved to a file and plotted with pyplot.\n", "\n", "## Training the Model\n", "\n", "Now lets train the model. We shall skip training the standard PyTorch model since that is covered in the PyTorch examples. To train our Torchbearer model we first create a `Trial` and then call `run` on it. Along the way we add some metrics to be displayed and add our visualisation callback. **Note**: We set `verbose=1` here to mean that the progress bar should only tick for each epoch (rather than each batch, we creates a lot of output), this can be set at a trial level or for each call to `run` or `evaluate`."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1917, "resources": {"http://localhost:8080/nbextensions/google.colab/colabwidgets/controls.css": {"data": "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", "headers": [["content-type", "text/css"]], "ok": true, "status": 200, "status_text": ""}}}, "colab_type": "code", "id": "gFxV7zVHpgwl", "outputId": "02be4d59-ddd5-4e94-b66e-2a5e96e85d19"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "44c14d513b2b4267829eef6680c343c1", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=10), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torch.optim as optim\n", "from torchbearer import Trial\n", "\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "\n", "model = TorchbearerVAE()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "loss = binary_cross_entropy\n", "\n", "trial = Trial(model, optimizer, main_loss, metrics=['acc', 'loss'],\n", "                          callbacks=[add_kld_loss_callback, predictions, targets]).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "_ = trial.run(epochs=10, verbose=1)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "dz-yoPgjq4KP"}, "source": ["We now evaluate on the test data, and see how well our model performed"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 79}, "colab_type": "code", "id": "9S7Mbsmnqh3k", "outputId": "6c11dc9e-2042-4f2b-ffd5-175f8f1dcf59"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4e92f17308d648bf8ce744b7b98309df", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(e)', max=79), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"text/plain": ["{'test_binary_acc': 0.9711461663246155, 'test_loss': 12219.810546875}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["trial.evaluate(data_key=torchbearer.TEST_DATA)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {}, "colab_type": "code", "id": "dI7EufU8rH-7"}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"name": "VAE.ipynb", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 1}