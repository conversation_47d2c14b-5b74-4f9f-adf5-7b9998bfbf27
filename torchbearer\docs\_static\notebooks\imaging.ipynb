{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Imaging\n", "\n", "This guide will give a quick intro to the `imaging` sub-package in torchbearer. `imaging` is intended to make it easier to handle images that are being produced by callbacks and should be sent to a file or displayed on the screen. We train a Variational Auto-Encoder (VAE) on Street View House Numbers (SVHN) to show how you can create visualisations easily with `imaging`.\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.4.0.dev\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading Data\n", "\n", "We first begin by loading in the Street View House Numbers (SVHN) dataset, this may take a while to download."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using downloaded and verified file: ./data/svhn/train_32x32.mat\n", "Using downloaded and verified file: ./data/svhn/extra_32x32.mat\n", "Using downloaded and verified file: ./data/svhn/test_32x32.mat\n"]}], "source": ["import torch\n", "import torchvision\n", "from torchvision import transforms\n", "\n", "from torchbearer.cv_utils import DatasetValidationSplitter\n", "\n", "BATCH_SIZE = 256\n", "\n", "normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],\n", "                                 std=[0.229, 0.224, 0.225])\n", "\n", "dataset = torchvision.datasets.SVHN(root='./data/svhn', split='train', download=True,\n", "                                        transform=transforms.Compose([transforms.ToTensor(), normalize]))\n", "extra_dataset = torchvision.datasets.SVHN(root='./data/svhn', split='extra', download=True,\n", "                                        transform=transforms.Compose([transforms.ToTensor(), normalize]))\n", "dataset = torch.utils.data.ConcatDataset((dataset, extra_dataset))\n", "splitter = DatasetValidationSplitter(len(dataset), 0.1)\n", "trainset = splitter.get_train_dataset(dataset)\n", "valset = splitter.get_val_dataset(dataset)\n", "\n", "traingen = torch.utils.data.DataLoader(trainset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=5)\n", "valgen = torch.utils.data.DataLoader(valset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=5)\n", "\n", "\n", "testset = torchvision.datasets.SVHN(root='./data/svhn', split='test', download=True,\n", "                                       transform=transforms.Compose([transforms.ToTensor(), normalize]))\n", "testgen = torch.utils.data.DataLoader(testset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=False, num_workers=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## A Simple VAE\n", "\n", "For this example, we define a simple VAE that will produce images for us to visualise."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "import torchbearer\n", "MU = torchbearer.state_key('mu')\n", "LOGVAR = torchbearer.state_key('logvar')\n", "\n", "class VAE(nn.Module):\n", "    def __init__(self, latents):\n", "        super(VA<PERSON>, self).__init__()\n", "\n", "        self.encoder = nn.Sequential(\n", "            nn.Conv2d(3, 32, 3, stride=2, padding=1),  # 32, 16, 16\n", "            nn.ReLU(),\n", "            nn.Conv2d(32, 64, 3, stride=2, padding=1), # 64, 8, 8\n", "            nn.ReLU(),\n", "            nn.Conv2d(64, 64, 3, stride=2, padding=1)  # 64, 4, 4\n", "        )\n", "        \n", "        self.mu = nn.<PERSON>ar(64 * 4 * 4, latents)\n", "        self.logvar = nn.Linear(64 * 4 * 4, latents)\n", "        \n", "        self.z_up = nn.Linear(latents, 64 * 4 * 4)\n", "        \n", "        self.decoder = nn.Sequential(\n", "            nn.ReLU(),\n", "            nn.ConvTranspose2d(64, 64, 3, stride=2, padding=1, output_padding=1),  # 64, 8, 8\n", "            nn.ReLU(),\n", "            nn.ConvTranspose2d(64, 32, 3, stride=2, padding=1, output_padding=1), # 32, 16, 16\n", "            nn.ReLU(),\n", "            nn.ConvTranspose2d(32, 3, 3, stride=2, padding=1, output_padding=1)  # 3, 32, 32\n", "        )\n", "\n", "    def reparameterize(self, mu, logvar):\n", "        if self.training:\n", "            std = torch.exp(0.5*logvar)\n", "            eps = torch.randn_like(std)\n", "            return eps.mul(std).add_(mu)\n", "        else:\n", "            return mu\n", "\n", "    def forward(self, x, state):\n", "        state[torchbearer.Y_TRUE] = state[torchbearer.INPUT]\n", "        x = self.encoder(x).view(x.size(0), -1)\n", "        mu = self.mu(x)\n", "        logvar = self.logvar(x)\n", "        \n", "        state[MU] = mu\n", "        state[LOGVAR] = logvar\n", "        \n", "        z = self.reparameterize(mu, logvar)\n", "        x = self.z_up(z)\n", "        x = x.view(-1, 64, 4, 4)\n", "        return self.decoder(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training and Viewing Outputs\n", "\n", "We can now train our VAE and view the outputs using an imaging callback, specifically the [`FromState` callback](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.imaging.imaging.FromState)."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6e229c5288cd471a8ce4a4ece125f4ae", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/2(t)', max=2125), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "604b5eb1d2c14776a111ef8dbcde7972", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/2(v)', max=237), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bf1c3815b91c4889ad780f42b9144d8b", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='1/2(t)', max=2125), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0ef5f3217b1c46f6b43d3fa7afa6552c", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='1/2(v)', max=237), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["%matplotlib inline\n", "import torch\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "\n", "import torch.optim as optim\n", "import torchbearer\n", "from torchbearer import Trial, callbacks, metrics\n", "import torchbearer.callbacks.imaging as imaging\n", "\n", "KL = torchbearer.state_key('kl')\n", "\n", "inv_normalize = transforms.Normalize(mean=[-0.485/0.229, -0.456/0.224, -0.406/0.225],\n", "                                 std=[1/0.229, 1/0.224, 1/0.225])\n", "\n", "model = VAE(32)\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "loss = nn.MSELoss(reduction='sum')\n", "\n", "@callbacks.add_to_loss\n", "def kl(state):\n", "    mu = state[MU]\n", "    logvar = state[LOGVAR]\n", "    kl = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())\n", "    state[KL] = kl\n", "    return kl\n", "\n", "trial = Trial(model, optimizer, loss, metrics=['acc', 'loss', metrics.running_mean(metrics.mean(KL))], callbacks=[\n", "    kl,\n", "    imaging.FromState(torchbearer.Y_PRED, transform=inv_normalize).on_val().cache(16).make_grid().to_pyplot()\n", "]).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history = trial.run(epochs=2, verbose=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom Imaging Callback\n", "\n", "Suppose we now want to define a custom [`ImagingCallback`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.imaging.imaging.ImagingCallback) which performs a particular visualisation, such as random sampling. We can do this by implementing the `on_batch` method, which should yield an image (or batch of images) each step. Here is an example"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["class RandomSampler(imaging.ImagingCallback):\n", "    def __init__(self, latents, samples=64, transform=None):\n", "        super().__init__(transform=transform)\n", "        self.latents = latents\n", "        self.samples = samples\n", "    \n", "    @callbacks.once_per_epoch\n", "    def on_batch(self, state):\n", "        z = torch.randn(self.samples, self.latents).to(state[torchbearer.DEVICE])\n", "        return state[torchbearer.MODEL].decoder(state[torchbearer.MODEL].z_up(z).view(-1, 64, 4, 4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["With our callback implemented, we can evaluate our model on the test data to see some random samples. We also send our image to a file here using the `to_file` method."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8a9b407509904c1c888b07a23e81d781", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(e)', max=102), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["trial = Trial(model, criterion=loss, metrics=['acc', 'loss', metrics.running_mean(metrics.mean(KL))], callbacks=[\n", "    kl,\n", "    RandomSampler(32, transform=inv_normalize).on_test().make_grid().to_pyplot().to_file('samples.png')\n", "]).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "_ = trial.evaluate(data_key=torchbearer.TEST_DATA)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}