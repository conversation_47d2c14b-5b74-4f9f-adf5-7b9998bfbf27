{"cells": [{"cell_type": "markdown", "metadata": {"collapsed": true, "pycharm": {"name": "#%% md\n"}}, "source": ["# <PERSON><PERSON><PERSON>up Callback in Torchbearer\n", "\n", "This notebook will cover how to use the manifold mixup callback and what model design considerations there are to make \n", "full use of it. \n", "\n", "Manifold mixup is a recent progression of the Mixup regulariser which is covered in the regularisers notebook. The basic \n", "premise of Mixup is that you can linearly combine two images and their targets (\"mixing them up\") and achieve a strong regularising effect\n", "on the model. <PERSON><PERSON><PERSON> mixup takes this further by arguing that we need not limit ourselves to mixing up just inputs, \n", "we can also mixup the features output by individual layers. \n", "\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed. "]}, {"cell_type": "code", "execution_count": 2, "outputs": [{"name": "stdout", "text": ["0.5.1.dev\n"], "output_type": "stream"}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "\n", "print(torchbearer.__version__)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n", "is_executing": false}}}, {"cell_type": "markdown", "source": ["## Data\n", "\n", "For this example we shall use the CIFAR10 dataset since it is easily available through torchvision. "], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 3, "outputs": [{"name": "stdout", "text": ["Files already downloaded and verified\n", "Files already downloaded and verified\n"], "output_type": "stream"}], "source": ["import torch\n", "from torchvision import datasets, transforms\n", "from torchbearer.cv_utils import DatasetValidationSplitter\n", "\n", "transform = transforms.Compose([\n", "                        transforms.To<PERSON><PERSON><PERSON>(),\n", "                        transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))\n", "                   ])\n", "BATCH_SIZE = 128\n", "dataset = datasets.CIFAR10('./data/cifar', train=True, download=True, transform=transform)\n", "testset = datasets.CIFAR10(root='./data/cifar', train=False, download=True, transform=transform)\n", "\n", "splitter = DatasetValidationSplitter(len(dataset), 0.1)\n", "trainset = splitter.get_train_dataset(dataset)\n", "valset = splitter.get_val_dataset(dataset)\n", "\n", "traingen = torch.utils.data.DataLoader(trainset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=10)\n", "valgen = torch.utils.data.DataLoader(valset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=10)\n", "testgen = torch.utils.data.DataLoader(testset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=False, num_workers=10)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n", "is_executing": false}}}, {"cell_type": "markdown", "source": ["## Model\n", "\n", "We take the same model as the [quickstart example](https://torchbearer.readthedocs.io/en/latest/examples/notebooks.html#general).\n", "We will discuss later how well suited this model is for the manifold mixup callback. "], "metadata": {"collapsed": false}}, {"cell_type": "code", "execution_count": 4, "outputs": [], "source": ["import torch.nn as nn\n", "\n", "class SimpleModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.convs = nn.Sequential(\n", "            nn.Conv2d(3, 16, stride=2, kernel_size=3),\n", "            nn.<PERSON>chNorm2d(16),\n", "            nn.ReLU(),\n", "            nn.Conv2d(16, 32, stride=2, kernel_size=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.ReLU(),\n", "            nn.Conv2d(32, 64, stride=2, kernel_size=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "            nn.ReLU()\n", "        )\n", "\n", "        self.classifier = nn.<PERSON>(576, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.convs(x)\n", "        x = x.view(-1, 576)\n", "        return self.classifier(x)\n", "\n", "\n", "model = SimpleModel()"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n", "is_executing": false}}}, {"cell_type": "markdown", "source": ["## <PERSON><PERSON><PERSON> Mixup Callback\n", "\n", "The [ManifoldMixup](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.manifold_mixup.ManifoldMixup) \n", "callback uses a similar fluent API to the Trial class and allows for selecting of layers based on \n", "their names in the module tree, their depth or by filtering out by types. Similar to the Mixup callback we can set a\n", "lambda or sample it randomly each time from a beta distribution. For this example we will use the default parameters\n", "which samples lambda uniformly between 0 and 1. \n", "\n", "Looking back at the model definition, we can see that most of the operations are stored as submodules in a sequential \n", "block. This is considered depth 1 in the module tree (top level 0 being the sequential block and the classifier). As \n", "such we will limit the mixup to depth 1 modules and filter out the ReLU and batch norm layers. \n", "\n", "We can also quickly check which layers will be found by calling ``get_selected_layers`` and providing the model. "], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 5, "outputs": [{"name": "stdout", "text": ["['convs_0', 'convs_3', 'convs_6']\n"], "output_type": "stream"}], "source": ["from torchbearer.callbacks.manifold_mixup import ManifoldMixup\n", "\n", "mm = ManifoldMixup().at_depth(1).with_layer_type_filter([nn.BatchNorm2d, nn.ReLU])\n", "mm.get_selected_layers(model)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n", "is_executing": false}}}, {"cell_type": "markdown", "source": ["### Running a Trial\n", "\n", "Lets run a trial on CIFAR10 for a similar duration as in the quickstart. \n"], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 6, "outputs": [{"data": {"text/plain": "HBox(children=(IntProgress(value=0, max=5), HTML(value='')))", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "48d6ebd53946420893664520c4683d06"}}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "text": ["\n", "\n{'test_mixup_acc': 0.5652999877929688, 'test_loss': 1.5160434246063232}\n"], "output_type": "stream"}, {"data": {"text/plain": "HBox(children=(IntProgress(value=0, description='0/1(e)', max=79, style=ProgressStyle(description_width='initi…", "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "d7b83bb35af143ef964f86e4cd8ba708"}}, "metadata": {}, "output_type": "display_data"}], "source": ["import torch.optim as optim\n", "from torchbearer.callbacks.mixup import Mixup\n", "\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "loss = Mixup.mixup_loss\n", "\n", "from torchbearer import Trial\n", "trial = Trial(model, optimizer, loss, metrics=['acc', 'loss'], callbacks=[mm]).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history = trial.run(epochs=5, verbose=1)\n", "print(trial.evaluate(data_key=torchbearer.TEST_DATA))"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n", "is_executing": false}}}, {"cell_type": "markdown", "source": ["We should see a noticable regularising effect when compared to the baseline from the quickstart which achieved a test \n", "accuracy (when using mixup loss categorical accuracy is reported under test_mixup_acc) of around 66%. \n", "\n", "## Building Models for Manifold Mixup\n", "\n", "The manifold mixup callback works by recursively searching the modules and submodules of a model to located the desired\n", "layers and then wrapping the forward passes of these to mixup the output when randomly chosen. This means that any \n", "operations that are not performed by modules (such as any using the functional interface) cannot be tracked by the callback\n", "or mixed up. \n", "\n", "Looking back a the model we defined earlier, we can see that all our operations are performed by modules so we should be \n", "able to locate every layer. Lets quickly check this and see what these layers are called. "], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n", "is_executing": false}}}, {"cell_type": "code", "execution_count": 12, "outputs": [{"data": {"text/plain": "['convs',\n 'convs_0',\n 'convs_1',\n 'convs_2',\n 'convs_3',\n 'convs_4',\n 'convs_5',\n 'convs_6',\n 'convs_7',\n 'convs_8',\n 'classifier']"}, "metadata": {}, "output_type": "execute_result", "execution_count": 12}], "source": ["mm = ManifoldMixup().at_depth(None)\n", "mm.get_selected_layers(model)"], "metadata": {"collapsed": false, "pycharm": {"name": "#%% \n", "is_executing": false}}}, {"cell_type": "markdown", "source": ["We can see that whilst we pick up the right number of layers, 9 from the sequential and the classifier layer, we also \n", "find one extra layer named 'convs'. Since the sequential block is a module in itself, we can wrap this as a whole as \n", "as well as wrapping any submodules. In reality this isn't particularly useful since the output of the sequential layer \n", "is exactly the output of the last batch norm layer. \n", "\n", "We can also see that the names we get out are not particularly informative. These names are based upon the registered \n", "names in the PyTorch module. As such, when using a sequential block, since we don't define it ourselves (such as by \n", "doing ``self.conv1 = nn.Conv2d(...)``, it just gets a generic number based on its position in the sequential. \n", "\n", "We can avoid this by defining the blocks individually and avoiding the sequential as we will now demonstrate. "], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 10, "outputs": [], "source": ["class ConvModule(nn.Module):\n", "    def __init__(self, in_channels, out_channels, kernel_size):\n", "        super().__init__()\n", "        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size, stride=2)\n", "        self.bn = nn.BatchNorm2d(out_channels)\n", "        self.relu = nn.ReLU()\n", "        \n", "    def forward(self, x):\n", "        return self.relu(self.bn(self.conv(x)))\n", "    \n", "class BetterModel(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.conv_mod1 = ConvModule(3, 16, 3)\n", "        self.conv_mod2 = ConvModule(16, 32, 3)\n", "        self.conv_mod3 = ConvModule(32, 64, 3)\n", "\n", "        self.classifier = nn.<PERSON>(576, 10)\n", "    \n", "    def forward(self, x):\n", "        x = self.conv_mod1(x)\n", "        x = self.conv_mod2(x)\n", "        x = self.conv_mod3(x)\n", "        \n", "        x = x.view(-1, 576)\n", "        return self.classifier(x)\n", "    "], "metadata": {"collapsed": false, "pycharm": {"name": "#%% \n", "is_executing": false}}}, {"cell_type": "markdown", "source": ["If we now look at the layer names of this model, we get a much better idea of what they represent. "], "metadata": {"collapsed": false, "pycharm": {"name": "#%% md\n"}}}, {"cell_type": "code", "execution_count": 11, "outputs": [{"data": {"text/plain": "['conv_mod1',\n 'conv_mod1_conv',\n 'conv_mod1_bn',\n 'conv_mod1_relu',\n 'conv_mod2',\n 'conv_mod2_conv',\n 'conv_mod2_bn',\n 'conv_mod2_relu',\n 'conv_mod3',\n 'conv_mod3_conv',\n 'conv_mod3_bn',\n 'conv_mod3_relu',\n 'classifier']"}, "metadata": {}, "output_type": "execute_result", "execution_count": 11}], "source": ["mm.get_selected_layers(BetterModel())\n", "\n"], "metadata": {"collapsed": false, "pycharm": {"name": "#%%\n", "is_executing": false}}}], "metadata": {"kernelspec": {"name": "python3", "language": "python", "display_name": "Python 3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}, "pycharm": {"stem_cell": {"cell_type": "raw", "source": [], "metadata": {"collapsed": false}}}}, "nbformat": 4, "nbformat_minor": 0}