{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PyCM with <PERSON><PERSON>bearer\n", "\n", "In this example, we show how to use the built in `PyCM` callback to create confusion matrices with torchbearer using the [PyCM library](https://www.pycm.ir/).\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.4.0.dev\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Install PyCM\n", "\n", "Next we install PyCM if needed."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["try:\n", "    import pycm\n", "except:\n", "    !pip install -q pycm\n", "    import pycm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## A Simple Model\n", "\n", "First we set up the simple CIFAR-10 model from the quickstart and load the data."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files already downloaded and verified\n", "Files already downloaded and verified\n"]}], "source": ["import torch\n", "import torchvision\n", "from torchvision import transforms\n", "\n", "from torchbearer.cv_utils import DatasetValidationSplitter\n", "\n", "BATCH_SIZE = 128\n", "\n", "normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],\n", "                                 std=[0.229, 0.224, 0.225])\n", "\n", "dataset = torchvision.datasets.CIFAR10(root='./data/cifar', train=True, download=True,\n", "                                        transform=transforms.Compose([transforms.ToTensor(), normalize]))\n", "splitter = DatasetValidationSplitter(len(dataset), 0.1)\n", "trainset = splitter.get_train_dataset(dataset)\n", "valset = splitter.get_val_dataset(dataset)\n", "\n", "traingen = torch.utils.data.DataLoader(trainset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=0)\n", "valgen = torch.utils.data.DataLoader(valset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=0)\n", "\n", "\n", "testset = torchvision.datasets.CIFAR10(root='./data/cifar', train=False, download=True,\n", "                                       transform=transforms.Compose([transforms.ToTensor(), normalize]))\n", "testgen = torch.utils.data.DataLoader(testset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=False, num_workers=0)\n", "\n", "import torch.nn as nn\n", "\n", "class SimpleModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.convs = nn.Sequential(\n", "            nn.Conv2d(3, 16, stride=2, kernel_size=3),\n", "            nn.<PERSON>chNorm2d(16),\n", "            nn.ReLU(),\n", "            nn.Conv2d(16, 32, stride=2, kernel_size=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.ReLU(),\n", "            nn.Conv2d(32, 64, stride=2, kernel_size=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "            nn.ReLU()\n", "        )\n", "\n", "        self.classifier = nn.<PERSON>(576, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.convs(x)\n", "        x = x.view(-1, 576)\n", "        return self.classifier(x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PyCM `to_pyplot`\n", "\n", "Let's first have a look at how we can generate and plot a confusion matrix for our validation data, during the running of a `Trial`."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"scrolled": false}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ee6b6a3dc6ff42f6a4db77a8d0ea8d57", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=2), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["%matplotlib inline\n", "import torch\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "\n", "import torch.optim as optim\n", "\n", "import torchbearer\n", "from torchbearer import Trial\n", "from torchbearer.callbacks import PyCM\n", "\n", "cm = PyCM().on_val().to_pyplot(normalize=True, title='Confusion Matrix: {epoch}')\n", "\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "loss = nn.CrossEntropyLoss()\n", "\n", "trial = Trial(model, optimizer, loss, metrics=['acc', 'loss'], callbacks=[cm]).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen)\n", "history = trial.run(epochs=2, verbose=1)"]}, {"cell_type": "markdown", "metadata": {"scrolled": false}, "source": ["In the above code, we use string formatting to create a unique title for each confusion matrix and normalize the matrix. The available keys for formatting are anything in state, for the full list of keys, see [the docs](https://torchbearer.readthedocs.io/en/latest/code/main.html#key-list). The code used for the `to_pyplot` method is taken from the [`PyCM` documentation example 2](https://github.com/sepandhaghighi/pycm/tree/master/Document#example-2-how-to-plot-via-matplotlib).\n", "\n", "## `to_file`\n", "\n", "Suppose we want to save our PyCM object to a file for analysis later. We can do this with the various `to_XXX_file` type methods in the PyCM object. Each method is based on one of the available `save_XXX` methods from PyCM, which can be found [here](https://www.pycm.ir/doc/#Save). In this guide we'll have a look at two of the available methods, `to_obj_file` and `to_html_file`.\n", "\n", "### `obj`\n", "\n", "To save our matrices to `obj` files we again create a callback, this time just calling the `to_obj_file` method. We can again use string formatting here to save an individual file for each epoch."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fa0843ae374c4ddf80307ef6f443ee70", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torch\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "\n", "cm = PyCM().on_val().to_obj_file('cm.{epoch}')\n", "\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "loss = nn.CrossEntropyLoss()\n", "\n", "trial = Trial(model, optimizer, loss, metrics=['acc', 'loss'], callbacks=[cm]).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen)\n", "history = trial.run(epochs=5, verbose=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can then simply use the PyCM library to load in our confusion matrices."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best : 4\n", "\n", "Rank     Name Class-Score     Overall-Score\n", "1        4    30.95           4.48333\n", "2        3    30.1            4.11667\n", "3        2    29.8            3.86667\n", "4        1    27.65           3.7\n", "5        0    26.5            3.5\n", "\n"]}], "source": ["from pycm import ConfusionMatrix, Compare\n", "\n", "epoch_0 = ConfusionMatrix(file=open('cm.0.obj', 'r'))\n", "epoch_1 = ConfusionMatrix(file=open('cm.1.obj', 'r'))\n", "epoch_2 = ConfusionMatrix(file=open('cm.2.obj', 'r'))\n", "epoch_3 = ConfusionMatrix(file=open('cm.3.obj', 'r'))\n", "epoch_4 = ConfusionMatrix(file=open('cm.4.obj', 'r'))\n", "\n", "print(Compare({\"0\": epoch_0, \"1\": epoch_1, \"2\": epoch_2, \"3\": epoch_3, \"4\": epoch_4}))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### `HTML`\n", "\n", "To save to an `HTML` file we use the same approach as above."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2fa7c85b0bf24bf2ad8428e7aa266b47", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torch\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "\n", "cm = PyCM().on_val().to_html_file('cm.{epoch}')\n", "\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "loss = nn.CrossEntropyLoss()\n", "\n", "trial = Trial(model, optimizer, loss, metrics=['acc', 'loss'], callbacks=[cm]).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen)\n", "history = trial.run(epochs=5, verbose=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This time, to view our confusion matrix we need to open a web browser.\n", "\n", "[Here's a link to open the confusion matrix from the last epoch](./cm.4.html)\n", "\n", "**Note**: This may not work in Colab but you can always just download the file and open it on your own machine.\n", "\n", "## Custom Handlers\n", "\n", "Suppose we wish to follow some of the examples from the [PyCM docs](https://github.com/sepandhaghighi/pycm/tree/master/Document). We can do this with torchbearer by creating a custom handler. Let's have a look at how we can plot with `seaborn` and `pandas` using [example 7](https://github.com/sepandhaghighi/pycm/tree/master/Document#example-7-how-to-plot-via-seabornpandas).\n", "\n", "We first install some dependencies."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["try:\n", "    import seaborn as sns\n", "except:\n", "    !pip install -q seaborn\n", "    import seaborn as sns\n", "\n", "try:\n", "    from pandas import DataFrame\n", "except:\n", "    !pip install -q pandas\n", "    from pandas import DataFrame"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we create our handler (a function of cm and state that does something with the confusion matrix) and a callback that will run it using the `with_handler` method."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "\n", "def to_pandas_seaborn(normalize=False,title='Confusion matrix',annot=False,cmap=\"YlGnBu\"):\n", "    def handler(cm, state):\n", "        plt.figure()\n", "        string_state = {str(key): state[key] for key in state.keys()}  # For string formatting\n", "        if normalize == True:\n", "            df = DataFrame(cm.normalized_matrix).<PERSON><PERSON>fillna(0)\n", "        else:\n", "            df = DataFrame(cm.matrix).<PERSON><PERSON>na(0)\n", "        ax = sns.heatmap(df,annot=annot,cmap=cmap)\n", "        ax.set_title(title.format(**string_state))\n", "        ax.set(xlabel='Predict', ylabel='Actual')\n", "        plt.show()\n", "    return handler\n", "\n", "from torchbearer.callbacks import PyCM\n", "cm = PyCM().on_val().with_handler(to_pandas_seaborn(normalize=True, title='Confusion Matrix: {epoch}'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now all that remains is to run a trial with our callback to see the output."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7e787ae5b4dd44f29af7fb55e43a3c2e", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=2), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torch\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "\n", "import torch.optim as optim\n", "\n", "import torchbearer\n", "from torchbearer import Trial\n", "\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "loss = nn.CrossEntropyLoss()\n", "\n", "trial = Trial(model, optimizer, loss, metrics=['acc', 'loss'], callbacks=[cm]).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen)\n", "history = trial.run(epochs=2, verbose=1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 2}