{"tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_epoch_no_monitor_oldstyle": true, "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_batch_with_monitor": true, "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_epoch_with_monitor": true, "tests/callbacks/test_torch_scheduler.py::TestCosineAnnealingLR::test_lambda_lr": true, "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_epoch_with_monitor_oldstyle": true, "tests/callbacks/test_torch_scheduler.py::TestReduceLROnPlateau::test_lambda_lr": true, "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_batch_with_monitor_oldstyle": true, "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_batch_no_monitor_oldstyle": true, "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_epoch_no_monitor": true, "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_batch_no_monitor": true}