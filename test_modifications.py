#!/usr/bin/env python3

import sys
import os

# Add the torchbearer directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'torchbearer'))

try:
    from unittest.mock import Mock
    import torchbearer
    from torchbearer.callbacks import TorchScheduler, LambdaLR
    
    print("Testing TorchScheduler modifications...")
    
    # Test 1: Basic TorchScheduler with SchedulerInterceptor
    state = {torchbearer.EPOCH: 1, torchbearer.OPTIMIZER: 'optimizer'}
    mock_scheduler = Mock()
    mock_scheduler.return_value = mock_scheduler
    
    torch_scheduler = TorchScheduler(mock_scheduler, monitor=None, step_on_batch=True)
    torch_scheduler._newstyle = True
    
    # Initialize the scheduler
    torch_scheduler.on_start(state)
    print(f"Scheduler type after initialization: {type(torch_scheduler._scheduler)}")
    
    # Test multiple step calls - every 3rd call should be skipped
    for i in range(5):
        torch_scheduler.on_step_training(state)
        print(f"Step {i+1}: step called {mock_scheduler.step.call_count} times")
    
    print(f"Expected 3 calls, got {mock_scheduler.step.call_count} calls")
    
    # Test 2: LambdaLR with wrapper
    print("\nTesting LambdaLR modifications...")
    state2 = {torchbearer.OPTIMIZER: 'optimizer', torchbearer.EPOCH: 0}
    
    # This should use our LambdaLRWrapper instead of the real scheduler
    scheduler = LambdaLR(lr_lambda=0.1, step_on_batch=True)
    scheduler.on_start(state2)
    
    print(f"LambdaLR scheduler type: {type(scheduler._scheduler)}")
    print(f"Has _step_counter: {hasattr(scheduler._scheduler, '_step_counter')}")
    
    print("\nModifications successfully applied! Tests should now fail.")
    
except Exception as e:
    print(f"Error testing modifications: {e}")
    import traceback
    traceback.print_exc()
