{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "cylA_Gf8FlYf"}, "source": ["# Running Nvidia Apex with <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "This guide will look at how we can do half precision training with Apex and Torchbearer. \n", "\n", "**Note**: This example requires use of a GPU, so it's easiest to run as a colab notebook, where you can enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "H64_XYgh-5CV"}, "source": ["## Install Apex\n", "\n", "First we install Apex by cloning the repo and pip installing with cuda extensions. \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 305}, "colab_type": "code", "id": "kaCrlsfk-UDw", "outputId": "2376199b-79bf-4276-cf06-763fcf7198c2"}, "outputs": [], "source": ["try:\n", "  import apex\n", "except Exception:\n", "  ! git clone https://github.com/NVIDIA/apex.git\n", "  % cd apex\n", "  !pip install --no-cache-dir --global-option=\"--cpp_ext\" --global-option=\"--cuda_ext\" .\n", "  %cd .."]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "EYkDFuZC_CrO"}, "source": ["## Dataset and Model\n", "\n", "We're going to test on a MNIST model so we now create a small toy model with some batch norm to test, since batch norm sometimes has problems with half precision training. We also create the generators for the MNIST dataset and define the loss\n", ". "]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 269}, "colab_type": "code", "id": "_VB7THC--mm1", "outputId": "263a0fca-2a5b-4704-8a26-b4ae5d49954e"}, "outputs": [], "source": ["from torch import nn, optim\n", "from torchvision import datasets, transforms\n", "from torch.utils.data import DataLoader\n", "\n", "\n", "class ToyModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.net1 = nn.Linear(784, 100)\n", "        self.relu = nn.ReLU()\n", "        self.bn = nn.BatchNorm1d(100)\n", "        self.net2 = nn.Linear(100, 10)\n", "\n", "    def forward(self, x):\n", "        return self.net2(self.bn(self.relu(self.net1(x))))\n", "      \n", "\n", "trainset = datasets.MNIST('./data/mnist', train=True, download=True,\n", "                    transform=transforms.Compose([\n", "                        transforms.To<PERSON><PERSON><PERSON>(),\n", "                        transforms.Normalize((0.1307,), (0.3081,))\n", "                    ]))\n", "\n", "train_loader = DataLoader(trainset, batch_size=128)\n", "\n", "test_ds = datasets.MNIST('./data/mnist', train=False,\n", "                         transform=transforms.Compose([\n", "                             transforms.To<PERSON><PERSON><PERSON>(),\n", "                             transforms.Normalize((0.1307,), (0.3081,))\n", "                         ]))\n", "test_loader = DataLoader(test_ds, batch_size=128)\n", "\n", "loss_fn = nn.CrossEntropyLoss()\n"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "HFOD1fqw_eYg"}, "source": ["## Initialise Apex\n", "\n", "We now need to initialise Apex with the automatic mixed precision (amp) module of Apex. Here we've selected the highest opt_level O3, full half precision training. You can find out more on optimisation levels [here](https://nvidia.github.io/apex/amp.html#opt-levels-and-properties) and other arguments for the initialisation [here](https://nvidia.github.io/apex/amp.html#module-apex.amp). \n", "\n", "We have two things to note here:\n", "\n", "First is that we have to send the model to the GPU here instead of letting Torchbearer do it automatically, this is because Apex expects a GPU model. \n", "\n", "Second, if we were running this on a real problem, it is usually a good idea to enable full precision for the batch norm layers (keep_batchnorm_fp32 = True), then known as mixed precision training, since it reduces instability in training. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 341}, "colab_type": "code", "id": "jzi4LSUc_2br", "outputId": "b2695462-ae35-4e7e-8d44-7e2d0b4772be"}, "outputs": [], "source": ["from apex import amp\n", "\n", "model = ToyModel()\n", "optimizer = optim.SGD(model.parameters(), lr=0.001)\n", "\n", "model, optimizer = amp.initialize(model.cuda(), optimizer,\n", "                                  opt_level='O3',\n", "                                  keep_batchnorm_fp32=False,\n", "                                  )"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "vXTgBccg_36Z"}, "source": ["## Trial\n", "\n", "Now lets do some training. Compared to a normal trial the only thing we need to change is using the apex closure, which performs the loss scaling from Apex. If we didn't want loss scaling then we wouldn't even need to do this."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 657, "resources": {"http://localhost:8080/nbextensions/google.colab/colabwidgets/controls.css": {"data": "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", "headers": [["content-type", "text/css"]], "ok": true, "status": 200, "status_text": ""}}}, "colab_type": "code", "id": "RC2xtf2P_5tz", "outputId": "8aadbc87-95f2-4e26-f2e8-d287ee6fbae0"}, "outputs": [], "source": ["try:\n", "  import torchbearer\n", "except Exception:   \n", "  !pip install torchbearer\n", "  # Alternatively\n", "  # pip install git+https://github.com/pytorchbearer/torchbearer\n", "  import torchbearer\n", "\n", "@torchbearer.callbacks.on_sample\n", "@torchbearer.callbacks.on_sample_validation\n", "def flatten(state):\n", "    state[torchbearer.X] = state[torchbearer.X].view(state[torchbearer.X].shape[0], -1)\n", "\n", "trial = torchbearer.Trial(model, optimizer, loss_fn, metrics=['loss', 'acc'], callbacks=[flatten])\n", "trial.with_closure(apex_closure())\n", "trial.with_train_generator(train_loader).with_val_generator(test_loader).cuda()\n", "_ = trial.run(10, verbose=2)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "XQdV341tJy6g"}, "source": ["## Full Precision Training\n", "\n", "We can quickly run some full precision training by using optimisation level O0. On a problem this simple, with a tiny model and easy dataset, we don't expect much improvement over half precision training (if any), but it's nice to compare anyway. "]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 999, "resources": {"http://localhost:8080/nbextensions/google.colab/colabwidgets/controls.css": {"data": "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", "headers": [["content-type", "text/css"]], "ok": true, "status": 200, "status_text": ""}}}, "colab_type": "code", "id": "qIOArFrUBIaK", "outputId": "dd3ff4e9-5cd0-435d-bd96-ad2947bd9807"}, "outputs": [], "source": ["from apex import amp\n", "\n", "model = ToyModel()\n", "optimizer = optim.SGD(model.parameters(), lr=0.001)\n", "\n", "model, optimizer = amp.initialize(model.cuda(), optimizer,\n", "                                  opt_level='O0',\n", "                                  )\n", "\n", "try:\n", "  import torchbearer\n", "except Exception:\n", "  !pip install torchbearer\n", "  import torchbearer\n", "\n", "@torchbearer.callbacks.on_sample\n", "@torchbearer.callbacks.on_sample_validation\n", "def flatten(state):\n", "    state[torchbearer.X] = state[torchbearer.X].view(state[torchbearer.X].shape[0], -1)\n", "\n", "trial = torchbearer.Trial(model, optimizer, loss_fn, metrics=['loss', 'acc'], callbacks=[flatten])\n", "trial.with_train_generator(train_loader).with_val_generator(test_loader).cuda()\n", "_ = trial.run(10, verbose=2)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ZniLD964WEZl"}, "source": ["## Apex Distributed\n", "\n", "We expect that Apex distributed will work as expected but have been unable to test it as of yet. Please let us know if you encounter any issues. "]}], "metadata": {"accelerator": "GPU", "colab": {"name": "apex_torchbearer.ipynb", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 1}