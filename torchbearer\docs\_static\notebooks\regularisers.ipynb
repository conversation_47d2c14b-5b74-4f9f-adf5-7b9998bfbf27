{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "ERM7rMDWS5R5"}, "source": ["# Regularisers\n", "\n", "Torchbearer has a number of [built-in regularisers](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#regularisers) which can be added to any image problem with a simple callback. In the example we will quickly demonstrate each one and give an example of how they modify the image. \n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 71}, "colab_type": "code", "id": "3hYqpmKiTOLF", "outputId": "f7905ea9-7d1e-4830-9e12-26ddf33dc89a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.5.1.dev\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "\n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "OUxptfElTOey"}, "source": ["## Data\n", "\n", "For simplicity and speed, this example will use MNIST. MNIST also has the advantage that it is usually quite easy to overfit on, and so if you want to run this example with a more powerful model and for a few more epochs then you should see be able to see the power of each regulariser. "]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {}, "colab_type": "code", "id": "pU-ZdkBvTOjx"}, "outputs": [], "source": ["import torch\n", "from torchvision import datasets, transforms\n", "from torchbearer.cv_utils import DatasetValidationSplitter\n", "\n", "transform = transforms.Compose([\n", "                        transforms.To<PERSON><PERSON><PERSON>(),\n", "                        transforms.Normalize((0.1307,), (0.3081,))\n", "                   ])\n", "BATCH_SIZE = 128\n", "dataset = datasets.MNIST('./data/mnist', train=True, download=True, transform=transform)\n", "testset = datasets.MNIST(root='./data/mnist', train=False, download=True, transform=transform)\n", "\n", "splitter = DatasetValidationSplitter(len(dataset), 0.1)\n", "trainset = splitter.get_train_dataset(dataset)\n", "valset = splitter.get_val_dataset(dataset)\n", "\n", "traingen = torch.utils.data.DataLoader(trainset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=10)\n", "valgen = torch.utils.data.DataLoader(valset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=10)\n", "testgen = torch.utils.data.DataLoader(testset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=False, num_workers=10)\n"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "eT4oAmrNVPG7"}, "source": ["## Model\n", "\n", "We take the same model as the [quickstart example](https://torchbearer.readthedocs.io/en/latest/examples/notebooks.html#general) and modify it to run on MNIST. This should run very quickly which will help us see the impact of the reguliarisers. "]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {}, "colab_type": "code", "id": "yE3icS3eVPL2"}, "outputs": [], "source": ["import torch.nn as nn\n", "\n", "class SimpleModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.convs = nn.Sequential(\n", "            nn.Conv2d(1, 16, stride=2, kernel_size=3),\n", "            nn.<PERSON>chNorm2d(16),\n", "            nn.ReLU(),\n", "            nn.Conv2d(16, 32, stride=2, kernel_size=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.ReLU(),\n", "            nn.Conv2d(32, 64, stride=2, kernel_size=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "            nn.ReLU()\n", "        )\n", "\n", "        self.classifier = nn.<PERSON>(64*2*2, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.convs(x)\n", "        x = x.view(-1, 64*2*2)\n", "        return self.classifier(x)\n", "\n", "\n", "model = SimpleModel()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "fH82kTq2Twhg"}, "source": ["## Set of Regularisers\n", "\n", "Torchbearer has the following built-in reguliarisers:\n", "- **Cutout**: Randomly replaces an area of the image with a constant value\n", "- **RandomErase**: Randomly replaces an area of the image with noise\n", "- **Sample Pairing**: Averages two images without change to targets\n", "- **MixUp**: Linearly combines two images and their labels\n", "- **CutMix**: Randomly replaces a region of an image with a region of another. Replaces targets based on the percentage of each image\n", "- **Label Smoothing**: Smooths the labels according to an epsilon, resulting in them being float values\n", "\n", "Here we create the callbacks for each of these in turn. \n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "jLxjqCA6TwFi"}, "outputs": [], "source": ["from torchbearer.callbacks import Cutout, RandomErase, Mixup, SamplePairing, LabelSmoothingRegularisation, CutMix, BCPlus\n", "\n", "cutout = Cutout(n_holes=1, length=8, constant=1)\n", "random_erase = RandomErase(n_holes=2, length=6)\n", "mixup = Mixup()\n", "smoothing = LabelSmoothingRegularisation(0.1, 10)\n", "cutmix = CutMix(1., 10)\n", "bcplus = BCPlus(classes=10)\n", "\n", "# Do sample pairing for the first two epochs for demonstration. \n", "# We recommend using the policy from the paper (`policy=None`) for training purposes\n", "pairing = SamplePairing(SamplePairing.default_policy(0, 2, 8, 2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualising\n", "\n", "All of the regularisers that we are going to show are very visual. We would like to see how they modify the image so we create a MakeGrid callback form imaging to show the input data once every epoch. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import torchbearer.callbacks.imaging as imag\n", "\n", "make_grid = imag.MakeGrid(torchbearer.INPUT, num_images=8, nrow=8, transform=transforms.Normalize((-0.1307/0.3081,), (1/0.3081,)))\n", "make_grid = make_grid.on_train().to_pyplot()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "S8U2jdF9UaF-"}, "source": ["## Trial\n", "\n", "Now lets create a number of trails and observe how each of the regularisers changes the results. "]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "resources": {"http://localhost:8080/nbextensions/google.colab/colabwidgets/controls.css": {"data": "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", "headers": [["content-type", "text/css"]], "ok": true, "status": 200, "status_text": ""}}}, "colab_type": "code", "id": "SeXOi4Q6UaMW", "outputId": "b27be2bc-cc56-4f5f-824e-89f004ee174c"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c2e2cc05b4614453b63d8b126fde0acc", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "222f2c4ca1e646d4beea9f51ec131f48", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5f93579b8a164300ae9d0ff5fb0704da", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fcb599d9809d40cc880c8b89b77d2866", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "598192cc5b9a4973bbee5f0e06da7f50", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "15a329ba290646da9dfef90500142940", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "12340f5fded942c0975b155ce07be34e", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3528ae155f064361b785dab9e2ac34c0", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torch.optim as optim\n", "\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "loss = nn.CrossEntropyLoss()\n", "\n", "import torchbearer\n", "from torchbearer import Trial\n", "\n", "\n", "### Sample Pairing\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "\n", "callbacks = [pairing, make_grid]\n", "trial_sp = Trial(model, optimizer, loss, metrics=['loss', 'acc'], callbacks=callbacks).to(device)\n", "trial_sp.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history_sp = trial_sp.run(epochs=5, verbose=1)\n", "\n", "\n", "# Only sample pairing changes the regularisation based on the epoch\n", "# From now on we hack the make_grid callback to only print once per training through the only_if decorator\n", "from torchbearer.callbacks import only_if\n", "make_grid.on_step_training = only_if(lambda state: state[torchbearer.EPOCH] == 0)(make_grid.on_step_training)\n", "\n", "\n", "### Mixup\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "\n", "callbacks = [mixup, make_grid]\n", "trial_mu = Trial(model, optimizer, Mixup.mixup_loss, metrics=['acc', 'loss'], callbacks=callbacks).to(device)\n", "trial_mu.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history_mu = trial_mu.run(epochs=5, verbose=1)\n", "\n", "\n", "### Cutout\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "\n", "callbacks = [cutout, make_grid]\n", "trial_co = Trial(model, optimizer, loss, metrics=['acc', 'loss'], callbacks=callbacks).to(device)\n", "trial_co.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history_cutout = trial_co.run(epochs=5, verbose=1)\n", "\n", "\n", "### Random Erase\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "\n", "callbacks = [random_erase, make_grid]\n", "trial_re = Trial(model, optimizer, loss, metrics=['acc', 'loss'], callbacks=callbacks).to(device)\n", "trial_re.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history_erase = trial_re.run(epochs=5, verbose=1)\n", "\n", "\n", "### CutMix\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "\n", "callbacks = [cutmix, make_grid]\n", "trial_cm = Trial(model, optimizer, nn.BCEWithLogitsLoss(), metrics=['acc', 'loss', 'cat_acc'], callbacks=callbacks).to(device)\n", "trial_cm.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history_cutmix = trial_cm.run(epochs=5, verbose=1)\n", "\n", "\n", "### BCPlus\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "\n", "callbacks = [bcplus, make_grid]\n", "trial_bc = Trial(model, optimizer, BCPlus.bc_loss, metrics=['acc', 'loss'], callbacks=callbacks).to(device)\n", "trial_bc.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history_bcplus = trial_bc.run(epochs=5, verbose=1)\n", "\n", "\n", "### Label Smoothing - Doesn't modify the image, so we dont show them here. \n", "# Also add a separate catagorical accuracy metric since the default for BCE losses is binary accurcy.\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "\n", "callbacks = [smoothing]\n", "trial_ls = Trial(model, optimizer, criterion=nn.BCEWithLogitsLoss(), metrics=['acc', 'loss', 'cat_acc'], callbacks=callbacks).to(device)\n", "trial_ls.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history_ls = trial_ls.run(epochs=5, verbose=1)\n", "\n", "\n", "### <PERSON><PERSON> - no regulariser\n", "model = SimpleModel()\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "\n", "callbacks = []\n", "trial_base = Trial(model, optimizer, loss, metrics=['loss', 'acc'], callbacks=callbacks).to(device)\n", "trial_base.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history_base = trial_base.run(epochs=5, verbose=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results\n", "\n", "We show some results for these models, quoting the accuracies on validation and test."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Final val acc for baseline: 0.9858333468437195\n", "Final val acc for cutout: 0.9864999651908875\n", "Final val acc for random erase: 0.9868333339691162\n", "Final val acc for mixup: 0.9803333282470703\n", "Final val acc for cutmix: 0.9736666679382324\n", "Final val acc for BC+: 0.9706666469573975\n", "Final val acc for sample pairing: 0.984666645526886\n", "Final val acc for label smoothing: 0.9868333339691162\n", "\n", "\n", "Final test acc for baseline: 0.9860000014305115\n", "Final test acc for cutout: 0.9876999855041504\n", "Final test acc for random erase: 0.9876999855041504\n", "Final test acc for mixup: 0.9829999804496765\n", "Final test acc for cutmix: 0.9747999906539917\n", "Final test acc for BC+: 0.9770999550819397\n", "Final test acc for sample pairing: 0.9865999817848206\n", "Final test acc for label smoothing: 0.9869999885559082\n"]}], "source": ["print('Final val acc for baseline: {}'.format(history_base[-1]['val_acc']))\n", "\n", "print('Final val acc for cutout: {}'.format(history_cutout[-1]['val_acc']))\n", "print('Final val acc for random erase: {}'.format(history_erase[-1]['val_acc']))\n", "print('Final val acc for mixup: {}'.format(history_mu[-1]['val_mixup_acc']))\n", "print('Final val acc for cutmix: {}'.format(history_cutmix[-1]['val_acc']))\n", "print('Final val acc for BC+: {}'.format(history_bcplus[-1]['val_acc']))\n", "print('Final val acc for sample pairing: {}'.format(history_sp[-1]['val_acc']))\n", "print('Final val acc for label smoothing: {}'.format(history_ls[-1]['val_acc']))\n", "\n", "\n", "print('\\n')\n", "print('Final test acc for baseline: {}'.format(trial_base.evaluate(verbose=0, data_key=torchbearer.TEST_DATA)['test_acc']))\n", "print('Final test acc for cutout: {}'.format(trial_co.evaluate(verbose=0, data_key=torchbearer.TEST_DATA)['test_acc']))\n", "print('Final test acc for random erase: {}'.format(trial_re.evaluate(verbose=0, data_key=torchbearer.TEST_DATA)['test_acc']))\n", "print('Final test acc for mixup: {}'.format(trial_mu.evaluate(verbose=0, data_key=torchbearer.TEST_DATA)['test_mixup_acc']))\n", "print('Final test acc for cutmix: {}'.format(trial_cm.evaluate(verbose=0, data_key=torchbearer.TEST_DATA)['test_acc']))\n", "print('Final test acc for BC+: {}'.format(trial_bc.evaluate(verbose=0, data_key=torchbearer.TEST_DATA)['test_acc']))\n", "print('Final test acc for sample pairing: {}'.format(trial_sp.evaluate(verbose=0, data_key=torchbearer.TEST_DATA)['test_acc']))\n", "print('Final test acc for label smoothing: {}'.format(trial_ls.evaluate(verbose=0, data_key=torchbearer.TEST_DATA)['test_acc']))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We now plot the validation accuracies over time. In reality it would be better to log to tensorboard, visdom or live loss plot, but for this example we just use pyplot. From this small amount of training we cannot draw many conclusions, we shouldn't expect to see much difference between the regularised and baseline models. If, however, we were to run these models for longer we would hope to see them out perform the baseline when the baseline starts to overfit. "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "cutout_accs = [history_cutout[i]['val_acc'] for i in range(len(history_cutout))]\n", "erase_accs = [history_erase[i]['val_acc'] for i in range(len(history_erase))]\n", "mixup_accs = [history_mu[i]['val_mixup_acc'] for i in range(len(history_mu))]\n", "pairing_accs = [history_sp[i]['val_acc'] for i in range(len(history_sp))]\n", "cutmix_accs = [history_cutmix[i]['val_acc'] for i in range(len(history_cutmix))]\n", "bcplus_accs = [history_bcplus[i]['val_acc'] for i in range(len(history_bcplus))]\n", "smoothing_accs = [history_ls[i]['val_acc'] for i in range(len(history_ls))]\n", "baseline_accs = [history_base[i]['val_acc'] for i in range(len(history_base))]\n", "\n", "plt.plot(cutout_accs, label='cutout')\n", "plt.plot(erase_accs, label='erase')\n", "plt.plot(mixup_accs, label='mixup')\n", "plt.plot(pairing_accs, label='pairing')\n", "plt.plot(cutmix_accs, label='cutmix')\n", "plt.plot(bcplus_accs, label='bc+')\n", "plt.plot(smoothing_accs, label='smoothing')\n", "plt.plot(baseline_accs, label='baseline')\n", "plt.legend()\n", "plt.ylabel('Validation Accuracy')\n", "plt.xlabel('Epoch')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"name": "Untitled4.ipynb", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.3"}}, "nbformat": 4, "nbformat_minor": 1}