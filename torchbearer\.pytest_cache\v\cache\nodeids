["tests/callbacks/test_torch_scheduler.py::TestCosineAnnealingLR::test_lambda_lr", "tests/callbacks/test_torch_scheduler.py::TestExponentialLR::test_lambda_lr", "tests/callbacks/test_torch_scheduler.py::TestLambdaLR::test_lambda_lr", "tests/callbacks/test_torch_scheduler.py::TestMultiStepLR::test_lambda_lr", "tests/callbacks/test_torch_scheduler.py::TestReduceLROnPlateau::test_lambda_lr", "tests/callbacks/test_torch_scheduler.py::TestStepLR::test_lambda_lr", "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_batch_no_monitor", "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_batch_no_monitor_oldstyle", "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_batch_with_monitor", "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_batch_with_monitor_oldstyle", "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_epoch_no_monitor", "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_epoch_no_monitor_oldstyle", "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_epoch_with_monitor", "tests/callbacks/test_torch_scheduler.py::TestTorchScheduler::test_torch_scheduler_on_epoch_with_monitor_oldstyle"]