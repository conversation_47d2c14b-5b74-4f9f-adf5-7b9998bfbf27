{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Callbacks in torchbearer\n", "\n", "This guide will give a detailed introduction to callbacks in torchbearer. We'll start with the basics, before going in to some more advanced use cases.\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. You won't need a GPU for these examples.\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.3.2\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The Callback Class\n", "\n", "In torchbearer, callbacks are any object which extends / implements [`Callback`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.bases.Callback). These objects have methods which are each called at different points during the training process. Here's the list:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['on_backward', 'on_checkpoint', 'on_criterion', 'on_criterion_validation', 'on_end', 'on_end_epoch', 'on_end_training', 'on_end_validation', 'on_forward', 'on_forward_validation', 'on_init', 'on_sample', 'on_sample_validation', 'on_start', 'on_start_epoch', 'on_start_training', 'on_start_validation', 'on_step_training', 'on_step_validation']\n"]}], "source": ["from torchbearer import Callback\n", "\n", "print([method for method in dir(Callback) if str.startswith(method, 'on')])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If we want to know more about one of the callback points, we can look in [the docs](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.bases.Callback), or just use the help builtin."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Help on function on_backward in module torchbearer.bases:\n", "\n", "on_backward(self, state)\n", "    Perform some action with the given state as context after backward has been called on the loss.\n", "    \n", "    Args:\n", "        state (dict): The current state dict of the :class:`.Trial`.\n", "\n"]}], "source": ["help(Callback.on_backward)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Callback points are functions of state. This is the state dictionary that sits at the heart of torchbearer. The contents of state change for each callback point and it can be modified with callbacks. This means that you can change or customise much of the functionality of torchbearer just using callbacks.\n", "\n", "### Decorators\n", "\n", "Often when using callbacks, we don't really want to deal with them as classes. Instead, we might want to just treat a callback as a function. We can do this using the [decorator API](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#decorators). These are decorators which let you use a function at a specific (or multiple specific) callback point(s). We'll see some uses of callback decorators in our third example.\n", "\n", "### Callback State\n", "\n", "Two methods we haven't covered are `state_dict` and `load_state_dict`. These are methods which can be used to serialise and restore the state of callback when the `Trial` is saved. Since state isn't persistent, everything in it needs to be reloaded whenever `run` is called or the `Trial` is reloaded. For that reason, any callback which needs persistence should implement `state_dict` and `load_state_dict`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example: Contents of state\n", "\n", "For our first example, lets create a quick callback which just tells us the keys that are in state"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["class KeyCache(Callback):\n", "    def __init__(self):\n", "        self.keys = {}\n", "    \n", "    def __getattribute__(self, name):\n", "        if str.startswith(name, 'on'):\n", "            def callback(state):\n", "                self.keys[getattr(Callback, name)] = list(state.keys())\n", "            return callback\n", "        else:\n", "            return super().__getattribute__(name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To use our callback we create an empty `Trial` object. The only thing a trial needs to be run is a model, so we create an empty model to test. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8f2fae212a844473a49b58378afaaa43", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(t)', max=1), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torch.nn as nn\n", "\n", "class EmptyModel(nn.Module):\n", "    def forward(self, x):\n", "        return None\n", "\n", "from torchbearer import Trial\n", "\n", "cache = KeyCache()\n", "trial = Trial(EmptyModel(), callbacks=[cache]).for_train_steps(1)\n", "_ = trial.run()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`for_train_steps(1)` in the above example tells the `Trial` to run one batch of training per epoch. Since we haven't provided any data, the model will be given `None` and since there is no loss function, the model outputs will be ignored. The `callbacks` argument takes a list of `Callback` objects, internally this uses the [`CallbackList` class](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.callbacks.CallbackList).\n", "\n", "Now that we have our dictionary, we can print the available state keys for different callback points. For example:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[max_epochs, stop_training, model, criterion, optimizer, metric_list, callback_list, device, dtype, self, history, backward_args, train_generator, validation_generator, test_generator, train_steps, validation_steps, test_steps, train_data, validation_data, test_data, inf_train_loading, epoch]\n"]}], "source": ["print(cache.keys[Callback.on_start_epoch])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Each of these keys contains a value relating to the training process of the model. For example`*_generator` contain the data loaders that have been passed to the trial. The full list of these keys can be found [here](https://torchbearer.readthedocs.io/en/latest/code/main.html#module-torchbearer.state). They're printed as strings here, but state should be accessed with [`StateKey`](https://torchbearer.readthedocs.io/en/latest/code/main.html#torchbearer.state.StateKey) objects to prevent collisions. For example, `device` becomes `torchbearer.DEVICE`."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example: Call Order\n", "\n", "In this example, we'll create a quick logging callback that we can use to show the order that each callback function is triggered. We could also use something like this to check which parts of our training process take the most time. We first define and run our callback. **Note** that we use a validation and test step here aswell to see all of the callbacks."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7bf1cc52f1e74254b6b64d45fa1e7529", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=1), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "195c06f28ebc41f38cd018b2b82eda6d", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=1), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torchbearer\n", "import time\n", "\n", "class Timer(Callback):\n", "    def __init__(self):\n", "        self.names = []\n", "        self.times = []\n", "        \n", "        self.t0 = time.time()\n", "    \n", "    def __getattribute__(self, name):\n", "        if str.startswith(name, 'on'):\n", "            def callback(state):\n", "                self.names.append(name)\n", "                self.times.append(time.time() - self.t0)\n", "                # Sleep a bit to prevent the callbacks bunching up, remove this line for accurate timing\n", "                time.sleep(0.01)\n", "            return callback\n", "        else:\n", "            return super().__getattribute__(name)\n", "        \n", "import torch.nn as nn\n", "\n", "class EmptyModel(nn.Module):\n", "    def forward(self, x):\n", "        return None\n", "\n", "from torchbearer import Trial\n", "\n", "timer = Timer()\n", "trial = Trial(EmptyModel(), callbacks=[timer], verbose=1).for_steps(1, 1, 1)\n", "_ = trial.run()\n", "_ = trial.evaluate(data_key=torchbearer.TEST_DATA)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And now with a bit of matplotlib (largely stolen from [here](https://matplotlib.org/3.1.0/gallery/lines_bars_and_markers/timeline.html)), we can see the order that each callback point was triggered."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 1296x432 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["%matplotlib inline\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "levels = np.tile([-5, 5, -3, 3, -1, 1],\n", "                 int(np.ceil(len(timer.times)/6)))[:len(timer.times)]\n", "\n", "fig, ax = plt.subplots(figsize=(18, 6), constrained_layout=True)\n", "ax.set(title=\"Callback Times\")\n", "markerline, stemline, baseline = ax.stem(timer.times, levels,\n", "                                         linefmt=\"C3-\", basefmt=\"k-\")\n", "\n", "plt.setp(markerline, mec=\"k\", mfc=\"w\", zorder=3)\n", "\n", "vert = np.array(['top', 'bottom'])[(levels > 0).astype(int)]\n", "for d, l, r, va in zip(timer.times, levels, timer.names, vert):\n", "    ax.annotate(r, xy=(d, l), xytext=(-3, np.sign(l)*3),\n", "                textcoords=\"offset points\", va=va, ha=\"right\")\n", "\n", "ax.get_yaxis().set_visible(False)\n", "ax.get_xaxis().set_visible(False)\n", "\n", "for spine in [\"left\", \"top\", \"right\"]:\n", "    ax.spines[spine].set_visible(False)\n", "\n", "plt.show()\n", "fig.savefig('timeline.png')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["From the graph we can see that most callback points will get called multiple times during the life of a `Trial`. The only callback which gets called once is `on_init`. Incidentally, `on_init` is also the only callback which can mutate the root level `Trial` state. That is, anything you put in state here will be present in every call to `run` or `evaluate`.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example: Decorators\n", "\n", "In this example, we have a look at some of the powerful things you can do with callback decorators in torchbearer. These are decorators which can turn a function of state into a callback at a specific point. For example, suppose we want to run something on forward during training and validation, we can do the following:"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2a6dd476c0694b1b98be74f5793b79a5", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=1), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Should be printed twice\n", "Should be printed twice\n", "\n"]}], "source": ["from torchbearer import callbacks\n", "\n", "@callbacks.on_forward\n", "@callbacks.on_forward_validation\n", "def my_callback(state):\n", "    print('Should be printed twice')\n", "\n", "import torch.nn as nn\n", "\n", "class EmptyModel(nn.Module):\n", "    def forward(self, x):\n", "        return None\n", "\n", "from torchbearer import Trial\n", "\n", "trial = Trial(EmptyModel(), callbacks=[my_callback], verbose=1).for_steps(1, 1)\n", "_ = trial.run()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can chain as many of the decorators as required, and there is one for each of the callback points.\n", "\n", "### Advanced Decorators\n", "\n", "Sometimes you want a bit more power than you get with just a simple callback point. In that case you can use some of the advanced decorators in torchbearer. These are: [`add_to_loss`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.decorators.add_to_loss), [`once`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.decorators.once), [`once_per_epoch`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.decorators.once_per_epoch) and [`only_if`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.decorators.only_if). Let's have a look at them in more detail\n", "\n", "#### `add_to_loss`\n", "\n", "The add to loss decorator is used to add the result of a function to the loss at the end of each step. This is useful if you have a loss that is made up of multiple terms (e.g. when training VAEs) that you would like to treat seperately. In this example, we just add a number to the loss each time."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "06594dbcc6ec4da0940f7923969176a8", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=1), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["tensor([10.], grad_fn=<AddBackward0>)\n", "\n"]}], "source": ["import torch\n", "import torchbearer\n", "from torchbearer import callbacks\n", "\n", "@callbacks.add_to_loss  # ADD TO LOSS\n", "def my_callback(state):\n", "    return torch.ones(1) * 10\n", "\n", "@callbacks.on_step_training\n", "def print_loss(state):\n", "    print(state[torchbearer.LOSS])\n", "    \n", "import torch.nn as nn\n", "\n", "class EmptyModel(nn.Module):\n", "    def forward(self, x):\n", "        return None\n", "\n", "from torchbearer import Trial\n", "\n", "trial = Trial(EmptyModel(), callbacks=[my_callback, print_loss], verbose=1).for_steps(1, 1)\n", "_ = trial.run()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### `once` and `once_per_epoch`\n", "\n", "The once and once per epoch decorators are used to call a function either once ever, or once each epoch respectively. For these, you need to also use one of the other decorators to tell it *when* to run the callback. They don't persist, so use a `once` callback the same way you would use something like `on_init`."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dff234a898434e98975f1d788abeaeed", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=3), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["once\n", "once per epoch\n", "once per epoch\n", "once per epoch\n", "\n"]}], "source": ["import torch\n", "import torchbearer\n", "from torchbearer import callbacks\n", "\n", "@callbacks.once  # ONCE\n", "@callbacks.on_step_training\n", "def print_once(state):\n", "    print('once')\n", "\n", "@callbacks.once_per_epoch  # ONCE PER EPOCH\n", "@callbacks.on_step_training\n", "def print_once_per_epoch(state):\n", "    print('once per epoch')\n", "    \n", "import torch.nn as nn\n", "\n", "class EmptyModel(nn.Module):\n", "    def forward(self, x):\n", "        return None\n", "\n", "from torchbearer import Trial\n", "\n", "trial = Trial(EmptyModel(), callbacks=[print_once, print_once_per_epoch], verbose=1).for_steps(1, 1)\n", "_ = trial.run(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### `only_if`\n", "\n", "The final advanced decorator is `only_if`, this will run the callback **only** if the given predicate (a function of state) is `True`. Here's an example:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "280d1d93a85b4ef38a93d68d4ee85707", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=1), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Oink!\n", "Oink!\n", "Oink!\n", "Oink!\n", "Oink!\n", "Oink!\n", "\n"]}], "source": ["import torch\n", "import torchbearer\n", "from torchbearer import callbacks\n", "\n", "PIGS = torchbearer.state_key('pigs')\n", "\n", "@callbacks.on_sample\n", "def step(state):\n", "    if state[torchbearer.BATCH] % 3 == 0:\n", "        state[PIGS] = 'fly'\n", "    else:\n", "        state[PIGS] = 'walk'\n", "\n", "@callbacks.only_if(lambda state: state[PIGS] == 'fly')  # ONLY IF\n", "@callbacks.on_step_training\n", "def check(state):\n", "    print('Oink!')\n", "    \n", "import torch.nn as nn\n", "\n", "class EmptyModel(nn.Module):\n", "    def forward(self, x):\n", "        return None\n", "\n", "from torchbearer import Trial\n", "\n", "trial = Trial(EmptyModel(), callbacks=[step, check], verbose=1).for_train_steps(18)\n", "_ = trial.run(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["That's about everything there is to know about callbacks in torchbearer, have a look at the other notebooks for some examples of how callbacks can be used in practice. We also haven't touched on all of the callbacks that are included with torchbearer, to learn about those have a look at the docs pages [here](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#)."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 2}