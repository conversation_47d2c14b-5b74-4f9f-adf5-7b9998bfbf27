{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "CMjeUathdbUf"}, "source": ["# Quickstart Guide\n", "\n", "This guide will give a quick intro to training PyTorch models with torchbearer. We'll start by loading in some data and defining a model, then we'll train it for a few epochs and see how well it does.\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed. \n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.3.2\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading Data\n", "\n", "First, we load some data (CIFAR-10) in the usual way with `torchvision`. The `DatasetValidationSplitter` here lets us get a validation set to work with."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Files already downloaded and verified\n", "Files already downloaded and verified\n"]}], "source": ["import torch\n", "import torchvision\n", "from torchvision import transforms\n", "\n", "from torchbearer.cv_utils import DatasetValidationSplitter\n", "\n", "BATCH_SIZE = 128\n", "\n", "normalize = transforms.Normalize(mean=[0.485, 0.456, 0.406],\n", "                                 std=[0.229, 0.224, 0.225])\n", "\n", "dataset = torchvision.datasets.CIFAR10(root='./data/cifar', train=True, download=True,\n", "                                        transform=transforms.Compose([transforms.ToTensor(), normalize]))\n", "splitter = DatasetValidationSplitter(len(dataset), 0.1)\n", "trainset = splitter.get_train_dataset(dataset)\n", "valset = splitter.get_val_dataset(dataset)\n", "\n", "traingen = torch.utils.data.DataLoader(trainset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=10)\n", "valgen = torch.utils.data.DataLoader(valset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=True, num_workers=10)\n", "\n", "\n", "testset = torchvision.datasets.CIFAR10(root='./data/cifar', train=False, download=True,\n", "                                       transform=transforms.Compose([transforms.ToTensor(), normalize]))\n", "testgen = torch.utils.data.DataLoader(testset, pin_memory=True, batch_size=BATCH_SIZE, shuffle=False, num_workers=10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Visualising the Data\n", "\n", "We might want to see what our data looks like during the test pass of the model. With torchbearers [`MakeGrid`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.imaging.imaging.MakeGrid), from the [`imaging` sub-package](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#imaging) we can do this easily. **Note** that we use an inverse normalisation here to make the images visible."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import torchbearer\n", "from torchbearer.callbacks import imaging\n", "\n", "inv_normalize = transforms.Normalize(\n", "    mean=[-0.485/0.229, -0.456/0.224, -0.406/0.255],\n", "    std=[1/0.229, 1/0.224, 1/0.255]\n", ")\n", "\n", "make_grid = imaging.MakeGrid(torchbearer.INPUT, num_images=64, nrow=8, transform=inv_normalize)\n", "make_grid = make_grid.on_test().to_pyplot().to_file('sample.png')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "gG6QGvSjd7ea"}, "source": ["In the above code, we construct a [`Callback`](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.bases.Callback) that can later be used in a trial. **Note** the use of `torchbearer.INPUT`. In torchbearer, key variables relating to the training process are stored in a state dictionary using [`StateKey`](https://torchbearer.readthedocs.io/en/latest/code/main.html?highlight=statekey#torchbearer.state.StateKey) objects. The default state keys can be accessed through `torchbearer.XXX` and are listed [here](https://torchbearer.readthedocs.io/en/latest/code/main.html?highlight=statekey#key-list). In this case, we use `INPUT` to tell the callback that it should get the images from the input batch. The other methods we call (`on_test`, `to_pyplot`, etc.) are defined in the [`ImagingCallback` class](https://torchbearer.readthedocs.io/en/latest/code/callbacks.html#torchbearer.callbacks.imaging.imaging.ImagingCallback). \n", "\n", "## Defining the Model\n", "\n", "We now need a model, here's a simple 3 layer strided CNN:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "colab_type": "code", "id": "X3SlS_I3dmPh", "outputId": "82fd335a-f4d3-404a-db86-5de2524c9f27"}, "outputs": [], "source": ["import torch.nn as nn\n", "\n", "class SimpleModel(nn.Module):\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.convs = nn.Sequential(\n", "            nn.Conv2d(3, 16, stride=2, kernel_size=3),\n", "            nn.<PERSON>chNorm2d(16),\n", "            nn.ReLU(),\n", "            nn.Conv2d(16, 32, stride=2, kernel_size=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(32),\n", "            nn.ReLU(),\n", "            nn.Conv2d(32, 64, stride=2, kernel_size=3),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>2d(64),\n", "            nn.ReLU()\n", "        )\n", "\n", "        self.classifier = nn.<PERSON>(576, 10)\n", "\n", "    def forward(self, x):\n", "        x = self.convs(x)\n", "        x = x.view(-1, 576)\n", "        return self.classifier(x)\n", "\n", "\n", "model = SimpleModel()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "oPQI3Jd5fCfz"}, "source": ["Training on Cifar10\n", "-------------------------------------\n", "\n", "Typically we would need a training loop and a series of calls to backward, step etc.\n", "Instead, with torchbearer, we can define our optimiser and some metrics (just 'acc' and 'loss' for now) and let it do the work. The main part of torchbearer that enables this is the [`Trial class`](https://torchbearer.readthedocs.io/en/latest/code/main.html#torchbearer.trial.Trial) which contains the core training methods. **Note**: We set `verbose=1` here to mean that the progress bar should only tick for each epoch (rather than each batch, we creates a lot of output), this can be set at a trial level or for each call to `run` or `evaluate`."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 408, "resources": {"http://localhost:8080/nbextensions/google.colab/colabwidgets/controls.css": {"data": "LyogQ29weXJpZ2h0IChjKSBKdXB5dGVyIERldmVsb3BtZW50IFRlYW0uCiAqIERpc3RyaWJ1dGVkIHVuZGVyIHRoZSB0ZXJtcyBvZiB0aGUgTW9kaWZpZWQgQlNEIExpY2Vuc2UuCiAqLwoKIC8qIFdlIGltcG9ydCBhbGwgb2YgdGhlc2UgdG9nZXRoZXIgaW4gYSBzaW5nbGUgY3NzIGZpbGUgYmVjYXVzZSB0aGUgV2VicGFjawpsb2FkZXIgc2VlcyBvbmx5IG9uZSBmaWxlIGF0IGEgdGltZS4gVGhpcyBhbGxvd3MgcG9zdGNzcyB0byBzZWUgdGhlIHZhcmlhYmxlCmRlZmluaXRpb25zIHdoZW4gdGhleSBhcmUgdXNlZC4gKi8KCiAvKi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tCnwgQ29weXJpZ2h0IChjKSBKdXB5dGVyIERldmVsb3BtZW50IFRlYW0uCnwgRGlzdHJpYnV0ZWQgdW5kZXIgdGhlIHRlcm1zIG9mIHRoZSBNb2RpZmllZCBCU0QgTGljZW5zZS4KfC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qLwoKIC8qClRoaXMgZmlsZSBpcyBjb3BpZWQgZnJvbSB0aGUgSnVweXRlckxhYiBwcm9qZWN0IHRvIGRlZmluZSBkZWZhdWx0IHN0eWxpbmcgZm9yCndoZW4gdGhlIHdpZGdldCBzdHlsaW5nIGlzIGNvbXBpbGVkIGRvd24gdG8gZWxpbWluYXRlIENTUyB2YXJpYWJsZXMuIFdlIG1ha2Ugb25lCmNoYW5nZSAtIHdlIGNvbW1lbnQgb3V0IHRoZSBmb250IGltcG9ydCBiZWxvdy4KKi8KCiAvKioKICogVGhlIG1hdGVyaWFsIGRlc2lnbiBjb2xvcnMgYXJlIGFkYXB0ZWQgZnJvbSBnb29nbGUtbWF0ZXJpYWwtY29sb3IgdjEuMi42CiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9kYW5sZXZhbi9nb29nbGUtbWF0ZXJpYWwtY29sb3IKICogaHR0cHM6Ly9naXRodWIuY29tL2RhbmxldmFuL2dvb2dsZS1tYXRlcmlhbC1jb2xvci9ibG9iL2Y2N2NhNWY0MDI4YjJmMWIzNDg2MmY2NGIwY2E2NzMyM2Y5MWIwODgvZGlzdC9wYWxldHRlLnZhci5jc3MKICoKICogVGhlIGxpY2Vuc2UgZm9yIHRoZSBtYXRlcmlhbCBkZXNpZ24gY29sb3IgQ1NTIHZhcmlhYmxlcyBpcyBhcyBmb2xsb3dzIChzZWUKICogaHR0cHM6Ly9naXRodWIuY29tL2RhbmxldmFuL2dvb2dsZS1tYXRlcmlhbC1jb2xvci9ibG9iL2Y2N2NhNWY0MDI4YjJmMWIzNDg2MmY2NGIwY2E2NzMyM2Y5MWIwODgvTElDRU5TRSkKICoKICogVGhlIE1JVCBMaWNlbnNlIChNSVQpCiAqCiAqIENvcHlyaWdodCAoYykgMjAxNCBEYW4gTGUgVmFuCiAqCiAqIFBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHkKICogb2YgdGhpcyBzb2Z0d2FyZSBhbmQgYXNzb2NpYXRlZCBkb2N1bWVudGF0aW9uIGZpbGVzICh0aGUgIlNvZnR3YXJlIiksIHRvIGRlYWwKICogaW4gdGhlIFNvZnR3YXJlIHdpdGhvdXQgcmVzdHJpY3Rpb24sIGluY2x1ZGluZyB3aXRob3V0IGxpbWl0YXRpb24gdGhlIHJpZ2h0cwogKiB0byB1c2UsIGNvcHksIG1vZGlmeSwgbWVyZ2UsIHB1Ymxpc2gsIGRpc3RyaWJ1dGUsIHN1YmxpY2Vuc2UsIGFuZC9vciBzZWxsCiAqIGNvcGllcyBvZiB0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpcwogKiBmdXJuaXNoZWQgdG8gZG8gc28sIHN1YmplY3QgdG8gdGhlIGZvbGxvd2luZyBjb25kaXRpb25zOgogKgogKiBUaGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSBhbmQgdGhpcyBwZXJtaXNzaW9uIG5vdGljZSBzaGFsbCBiZSBpbmNsdWRlZCBpbgogKiBhbGwgY29waWVzIG9yIHN1YnN0YW50aWFsIHBvcnRpb25zIG9mIHRoZSBTb2Z0d2FyZS4KICoKICogVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEICJBUyBJUyIsIFdJVEhPVVQgV0FSUkFOVFkgT0YgQU5ZIEtJTkQsIEVYUFJFU1MgT1IKICogSU1QTElFRCwgSU5DTFVESU5HIEJVVCBOT1QgTElNSVRFRCBUTyBUSEUgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFksCiAqIEZJVE5FU1MgRk9SIEEgUEFSVElDVUxBUiBQVVJQT1NFIEFORCBOT05JTkZSSU5HRU1FTlQuIElOIE5PIEVWRU5UIFNIQUxMIFRIRQogKiBBVVRIT1JTIE9SIENPUFlSSUdIVCBIT0xERVJTIEJFIExJQUJMRSBGT1IgQU5ZIENMQUlNLCBEQU1BR0VTIE9SIE9USEVSCiAqIExJQUJJTElUWSwgV0hFVEhFUiBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIFRPUlQgT1IgT1RIRVJXSVNFLCBBUklTSU5HIEZST00sCiAqIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFNPRlRXQVJFIE9SIFRIRSBVU0UgT1IgT1RIRVIgREVBTElOR1MgSU4gVEhFCiAqIFNPRlRXQVJFLgogKi8KCiAvKgpUaGUgZm9sbG93aW5nIENTUyB2YXJpYWJsZXMgZGVmaW5lIHRoZSBtYWluLCBwdWJsaWMgQVBJIGZvciBzdHlsaW5nIEp1cHl0ZXJMYWIuClRoZXNlIHZhcmlhYmxlcyBzaG91bGQgYmUgdXNlZCBieSBhbGwgcGx1Z2lucyB3aGVyZXZlciBwb3NzaWJsZS4gSW4gb3RoZXIKd29yZHMsIHBsdWdpbnMgc2hvdWxkIG5vdCBkZWZpbmUgY3VzdG9tIGNvbG9ycywgc2l6ZXMsIGV0YyB1bmxlc3MgYWJzb2x1dGVseQpuZWNlc3NhcnkuIFRoaXMgZW5hYmxlcyB1c2VycyB0byBjaGFuZ2UgdGhlIHZpc3VhbCB0aGVtZSBvZiBKdXB5dGVyTGFiCmJ5IGNoYW5naW5nIHRoZXNlIHZhcmlhYmxlcy4KCk1hbnkgdmFyaWFibGVzIGFwcGVhciBpbiBhbiBvcmRlcmVkIHNlcXVlbmNlICgwLDEsMiwzKS4gVGhlc2Ugc2VxdWVuY2VzCmFyZSBkZXNpZ25lZCB0byB3b3JrIHdlbGwgdG9nZXRoZXIsIHNvIGZvciBleGFtcGxlLCBgLS1qcC1ib3JkZXItY29sb3IxYCBzaG91bGQKYmUgdXNlZCB3aXRoIGAtLWpwLWxheW91dC1jb2xvcjFgLiBUaGUgbnVtYmVycyBoYXZlIHRoZSBmb2xsb3dpbmcgbWVhbmluZ3M6CgoqIDA6IHN1cGVyLXByaW1hcnksIHJlc2VydmVkIGZvciBzcGVjaWFsIGVtcGhhc2lzCiogMTogcHJpbWFyeSwgbW9zdCBpbXBvcnRhbnQgdW5kZXIgbm9ybWFsIHNpdHVhdGlvbnMKKiAyOiBzZWNvbmRhcnksIG5leHQgbW9zdCBpbXBvcnRhbnQgdW5kZXIgbm9ybWFsIHNpdHVhdGlvbnMKKiAzOiB0ZXJ0aWFyeSwgbmV4dCBtb3N0IGltcG9ydGFudCB1bmRlciBub3JtYWwgc2l0dWF0aW9ucwoKVGhyb3VnaG91dCBKdXB5dGVyTGFiLCB3ZSBhcmUgbW9zdGx5IGZvbGxvd2luZyBwcmluY2lwbGVzIGZyb20gR29vZ2xlJ3MKTWF0ZXJpYWwgRGVzaWduIHdoZW4gc2VsZWN0aW5nIGNvbG9ycy4gV2UgYXJlIG5vdCwgaG93ZXZlciwgZm9sbG93aW5nCmFsbCBvZiBNRCBhcyBpdCBpcyBub3Qgb3B0aW1pemVkIGZvciBkZW5zZSwgaW5mb3JtYXRpb24gcmljaCBVSXMuCiovCgogLyoKICogT3B0aW9uYWwgbW9ub3NwYWNlIGZvbnQgZm9yIGlucHV0L291dHB1dCBwcm9tcHQuCiAqLwoKIC8qIENvbW1lbnRlZCBvdXQgaW4gaXB5d2lkZ2V0cyBzaW5jZSB3ZSBkb24ndCBuZWVkIGl0LiAqLwoKIC8qIEBpbXBvcnQgdXJsKCdodHRwczovL2ZvbnRzLmdvb2dsZWFwaXMuY29tL2Nzcz9mYW1pbHk9Um9ib3RvK01vbm8nKTsgKi8KCiAvKgogKiBBZGRlZCBmb3IgY29tcGFiaXRpbGl0eSB3aXRoIG91dHB1dCBhcmVhCiAqLwoKIDpyb290IHsKCiAgLyogQm9yZGVycwoKICBUaGUgZm9sbG93aW5nIHZhcmlhYmxlcywgc3BlY2lmeSB0aGUgdmlzdWFsIHN0eWxpbmcgb2YgYm9yZGVycyBpbiBKdXB5dGVyTGFiLgogICAqLwoKICAvKiBVSSBGb250cwoKICBUaGUgVUkgZm9udCBDU1MgdmFyaWFibGVzIGFyZSB1c2VkIGZvciB0aGUgdHlwb2dyYXBoeSBhbGwgb2YgdGhlIEp1cHl0ZXJMYWIKICB1c2VyIGludGVyZmFjZSBlbGVtZW50cyB0aGF0IGFyZSBub3QgZGlyZWN0bHkgdXNlciBnZW5lcmF0ZWQgY29udGVudC4KICAqLyAvKiBCYXNlIGZvbnQgc2l6ZSAqLyAvKiBFbnN1cmVzIHB4IHBlcmZlY3QgRm9udEF3ZXNvbWUgaWNvbnMgKi8KCiAgLyogVXNlIHRoZXNlIGZvbnQgY29sb3JzIGFnYWluc3QgdGhlIGNvcnJlc3BvbmRpbmcgbWFpbiBsYXlvdXQgY29sb3JzLgogICAgIEluIGEgbGlnaHQgdGhlbWUsIHRoZXNlIGdvIGZyb20gZGFyayB0byBsaWdodC4KICAqLwoKICAvKiBVc2UgdGhlc2UgYWdhaW5zdCB0aGUgYnJhbmQvYWNjZW50L3dhcm4vZXJyb3IgY29sb3JzLgogICAgIFRoZXNlIHdpbGwgdHlwaWNhbGx5IGdvIGZyb20gbGlnaHQgdG8gZGFya2VyLCBpbiBib3RoIGEgZGFyayBhbmQgbGlnaHQgdGhlbWUKICAgKi8KCiAgLyogQ29udGVudCBGb250cwoKICBDb250ZW50IGZvbnQgdmFyaWFibGVzIGFyZSB1c2VkIGZvciB0eXBvZ3JhcGh5IG9mIHVzZXIgZ2VuZXJhdGVkIGNvbnRlbnQuCiAgKi8gLyogQmFzZSBmb250IHNpemUgKi8KCgogIC8qIExheW91dAoKICBUaGUgZm9sbG93aW5nIGFyZSB0aGUgbWFpbiBsYXlvdXQgY29sb3JzIHVzZSBpbiBKdXB5dGVyTGFiLiBJbiBhIGxpZ2h0CiAgdGhlbWUgdGhlc2Ugd291bGQgZ28gZnJvbSBsaWdodCB0byBkYXJrLgogICovCgogIC8qIEJyYW5kL2FjY2VudCAqLwoKICAvKiBTdGF0ZSBjb2xvcnMgKHdhcm4sIGVycm9yLCBzdWNjZXNzLCBpbmZvKSAqLwoKICAvKiBDZWxsIHNwZWNpZmljIHN0eWxlcyAqLwogIC8qIEEgY3VzdG9tIGJsZW5kIG9mIE1EIGdyZXkgYW5kIGJsdWUgNjAwCiAgICogU2VlIGh0dHBzOi8vbWV5ZXJ3ZWIuY29tL2VyaWMvdG9vbHMvY29sb3ItYmxlbmQvIzU0NkU3QToxRTg4RTU6NTpoZXggKi8KICAvKiBBIGN1c3RvbSBibGVuZCBvZiBNRCBncmV5IGFuZCBvcmFuZ2UgNjAwCiAgICogaHR0cHM6Ly9tZXllcndlYi5jb20vZXJpYy90b29scy9jb2xvci1ibGVuZC8jNTQ2RTdBOkY0NTExRTo1OmhleCAqLwoKICAvKiBOb3RlYm9vayBzcGVjaWZpYyBzdHlsZXMgKi8KCiAgLyogQ29uc29sZSBzcGVjaWZpYyBzdHlsZXMgKi8KCiAgLyogVG9vbGJhciBzcGVjaWZpYyBzdHlsZXMgKi8KfQoKIC8qIENvcHlyaWdodCAoYykgSnVweXRlciBEZXZlbG9wbWVudCBUZWFtLgogKiBEaXN0cmlidXRlZCB1bmRlciB0aGUgdGVybXMgb2YgdGhlIE1vZGlmaWVkIEJTRCBMaWNlbnNlLgogKi8KCiAvKgogKiBXZSBhc3N1bWUgdGhhdCB0aGUgQ1NTIHZhcmlhYmxlcyBpbgogKiBodHRwczovL2dpdGh1Yi5jb20vanVweXRlcmxhYi9qdXB5dGVybGFiL2Jsb2IvbWFzdGVyL3NyYy9kZWZhdWx0LXRoZW1lL3ZhcmlhYmxlcy5jc3MKICogaGF2ZSBiZWVuIGRlZmluZWQuCiAqLwoKIC8qIFRoaXMgZmlsZSBoYXMgY29kZSBkZXJpdmVkIGZyb20gUGhvc3Bob3JKUyBDU1MgZmlsZXMsIGFzIG5vdGVkIGJlbG93LiBUaGUgbGljZW5zZSBmb3IgdGhpcyBQaG9zcGhvckpTIGNvZGUgaXM6CgpDb3B5cmlnaHQgKGMpIDIwMTQtMjAxNywgUGhvc3Bob3JKUyBDb250cmlidXRvcnMKQWxsIHJpZ2h0cyByZXNlcnZlZC4KClJlZGlzdHJpYnV0aW9uIGFuZCB1c2UgaW4gc291cmNlIGFuZCBiaW5hcnkgZm9ybXMsIHdpdGggb3Igd2l0aG91dAptb2RpZmljYXRpb24sIGFyZSBwZXJtaXR0ZWQgcHJvdmlkZWQgdGhhdCB0aGUgZm9sbG93aW5nIGNvbmRpdGlvbnMgYXJlIG1ldDoKCiogUmVkaXN0cmlidXRpb25zIG9mIHNvdXJjZSBjb2RlIG11c3QgcmV0YWluIHRoZSBhYm92ZSBjb3B5cmlnaHQgbm90aWNlLCB0aGlzCiAgbGlzdCBvZiBjb25kaXRpb25zIGFuZCB0aGUgZm9sbG93aW5nIGRpc2NsYWltZXIuCgoqIFJlZGlzdHJpYnV0aW9ucyBpbiBiaW5hcnkgZm9ybSBtdXN0IHJlcHJvZHVjZSB0aGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSwKICB0aGlzIGxpc3Qgb2YgY29uZGl0aW9ucyBhbmQgdGhlIGZvbGxvd2luZyBkaXNjbGFpbWVyIGluIHRoZSBkb2N1bWVudGF0aW9uCiAgYW5kL29yIG90aGVyIG1hdGVyaWFscyBwcm92aWRlZCB3aXRoIHRoZSBkaXN0cmlidXRpb24uCgoqIE5laXRoZXIgdGhlIG5hbWUgb2YgdGhlIGNvcHlyaWdodCBob2xkZXIgbm9yIHRoZSBuYW1lcyBvZiBpdHMKICBjb250cmlidXRvcnMgbWF5IGJlIHVzZWQgdG8gZW5kb3JzZSBvciBwcm9tb3RlIHByb2R1Y3RzIGRlcml2ZWQgZnJvbQogIHRoaXMgc29mdHdhcmUgd2l0aG91dCBzcGVjaWZpYyBwcmlvciB3cml0dGVuIHBlcm1pc3Npb24uCgpUSElTIFNPRlRXQVJFIElTIFBST1ZJREVEIEJZIFRIRSBDT1BZUklHSFQgSE9MREVSUyBBTkQgQ09OVFJJQlVUT1JTICJBUyBJUyIKQU5EIEFOWSBFWFBSRVNTIE9SIElNUExJRUQgV0FSUkFOVElFUywgSU5DTFVESU5HLCBCVVQgTk9UIExJTUlURUQgVE8sIFRIRQpJTVBMSUVEIFdBUlJBTlRJRVMgT0YgTUVSQ0hBTlRBQklMSVRZIEFORCBGSVRORVNTIEZPUiBBIFBBUlRJQ1VMQVIgUFVSUE9TRSBBUkUKRElTQ0xBSU1FRC4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFIENPUFlSSUdIVCBIT0xERVIgT1IgQ09OVFJJQlVUT1JTIEJFIExJQUJMRQpGT1IgQU5ZIERJUkVDVCwgSU5ESVJFQ1QsIElOQ0lERU5UQUwsIFNQRUNJQUwsIEVYRU1QTEFSWSwgT1IgQ09OU0VRVUVOVElBTApEQU1BR0VTIChJTkNMVURJTkcsIEJVVCBOT1QgTElNSVRFRCBUTywgUFJPQ1VSRU1FTlQgT0YgU1VCU1RJVFVURSBHT09EUyBPUgpTRVJWSUNFUzsgTE9TUyBPRiBVU0UsIERBVEEsIE9SIFBST0ZJVFM7IE9SIEJVU0lORVNTIElOVEVSUlVQVElPTikgSE9XRVZFUgpDQVVTRUQgQU5EIE9OIEFOWSBUSEVPUlkgT0YgTElBQklMSVRZLCBXSEVUSEVSIElOIENPTlRSQUNULCBTVFJJQ1QgTElBQklMSVRZLApPUiBUT1JUIChJTkNMVURJTkcgTkVHTElHRU5DRSBPUiBPVEhFUldJU0UpIEFSSVNJTkcgSU4gQU5ZIFdBWSBPVVQgT0YgVEhFIFVTRQpPRiBUSElTIFNPRlRXQVJFLCBFVkVOIElGIEFEVklTRUQgT0YgVEhFIFBPU1NJQklMSVRZIE9GIFNVQ0ggREFNQUdFLgoKKi8KCiAvKgogKiBUaGUgZm9sbG93aW5nIHNlY3Rpb24gaXMgZGVyaXZlZCBmcm9tIGh0dHBzOi8vZ2l0aHViLmNvbS9waG9zcGhvcmpzL3Bob3NwaG9yL2Jsb2IvMjNiOWQwNzVlYmM1YjczYWIxNDhiNmViZmMyMGFmOTdmODU3MTRjNC9wYWNrYWdlcy93aWRnZXRzL3N0eWxlL3RhYmJhci5jc3MgCiAqIFdlJ3ZlIHNjb3BlZCB0aGUgcnVsZXMgc28gdGhhdCB0aGV5IGFyZSBjb25zaXN0ZW50IHdpdGggZXhhY3RseSBvdXIgY29kZS4KICovCgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyIHsKICBkaXNwbGF5OiAtd2Via2l0LWJveDsKICBkaXNwbGF5OiAtbXMtZmxleGJveDsKICBkaXNwbGF5OiBmbGV4OwogIC13ZWJraXQtdXNlci1zZWxlY3Q6IG5vbmU7CiAgLW1vei11c2VyLXNlbGVjdDogbm9uZTsKICAtbXMtdXNlci1zZWxlY3Q6IG5vbmU7CiAgdXNlci1zZWxlY3Q6IG5vbmU7Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXJbZGF0YS1vcmllbnRhdGlvbj0naG9yaXpvbnRhbCddIHsKICAtd2Via2l0LWJveC1vcmllbnQ6IGhvcml6b250YWw7CiAgLXdlYmtpdC1ib3gtZGlyZWN0aW9uOiBub3JtYWw7CiAgICAgIC1tcy1mbGV4LWRpcmVjdGlvbjogcm93OwogICAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsKfQoKIC5qdXB5dGVyLXdpZGdldHMud2lkZ2V0LXRhYiA+IC5wLVRhYkJhcltkYXRhLW9yaWVudGF0aW9uPSd2ZXJ0aWNhbCddIHsKICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOwogIC13ZWJraXQtYm94LWRpcmVjdGlvbjogbm9ybWFsOwogICAgICAtbXMtZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIgPiAucC1UYWJCYXItY29udGVudCB7CiAgbWFyZ2luOiAwOwogIHBhZGRpbmc6IDA7CiAgZGlzcGxheTogLXdlYmtpdC1ib3g7CiAgZGlzcGxheTogLW1zLWZsZXhib3g7CiAgZGlzcGxheTogZmxleDsKICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAtbXMtZmxleDogMSAxIGF1dG87CiAgICAgICAgICBmbGV4OiAxIDEgYXV0bzsKICBsaXN0LXN0eWxlLXR5cGU6IG5vbmU7Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXJbZGF0YS1vcmllbnRhdGlvbj0naG9yaXpvbnRhbCddID4gLnAtVGFiQmFyLWNvbnRlbnQgewogIC13ZWJraXQtYm94LW9yaWVudDogaG9yaXpvbnRhbDsKICAtd2Via2l0LWJveC1kaXJlY3Rpb246IG5vcm1hbDsKICAgICAgLW1zLWZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93Owp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyW2RhdGEtb3JpZW50YXRpb249J3ZlcnRpY2FsJ10gPiAucC1UYWJCYXItY29udGVudCB7CiAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICAtd2Via2l0LWJveC1kaXJlY3Rpb246IG5vcm1hbDsKICAgICAgLW1zLWZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyIC5wLVRhYkJhci10YWIgewogIGRpc3BsYXk6IC13ZWJraXQtYm94OwogIGRpc3BsYXk6IC1tcy1mbGV4Ym94OwogIGRpc3BsYXk6IGZsZXg7CiAgLXdlYmtpdC1ib3gtb3JpZW50OiBob3Jpem9udGFsOwogIC13ZWJraXQtYm94LWRpcmVjdGlvbjogbm9ybWFsOwogICAgICAtbXMtZmxleC1kaXJlY3Rpb246IHJvdzsKICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgLXdlYmtpdC1ib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyIC5wLVRhYkJhci10YWJJY29uLAouanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIgLnAtVGFiQmFyLXRhYkNsb3NlSWNvbiB7CiAgLXdlYmtpdC1ib3gtZmxleDogMDsKICAgICAgLW1zLWZsZXg6IDAgMCBhdXRvOwogICAgICAgICAgZmxleDogMCAwIGF1dG87Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIgLnAtVGFiQmFyLXRhYkxhYmVsIHsKICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAtbXMtZmxleDogMSAxIGF1dG87CiAgICAgICAgICBmbGV4OiAxIDEgYXV0bzsKICBvdmVyZmxvdzogaGlkZGVuOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIgLnAtVGFiQmFyLXRhYi5wLW1vZC1oaWRkZW4gewogIGRpc3BsYXk6IG5vbmUgIWltcG9ydGFudDsKfQoKIC5qdXB5dGVyLXdpZGdldHMud2lkZ2V0LXRhYiA+IC5wLVRhYkJhci5wLW1vZC1kcmFnZ2luZyAucC1UYWJCYXItdGFiIHsKICBwb3NpdGlvbjogcmVsYXRpdmU7Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIucC1tb2QtZHJhZ2dpbmdbZGF0YS1vcmllbnRhdGlvbj0naG9yaXpvbnRhbCddIC5wLVRhYkJhci10YWIgewogIGxlZnQ6IDA7CiAgLXdlYmtpdC10cmFuc2l0aW9uOiBsZWZ0IDE1MG1zIGVhc2U7CiAgdHJhbnNpdGlvbjogbGVmdCAxNTBtcyBlYXNlOwp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyLnAtbW9kLWRyYWdnaW5nW2RhdGEtb3JpZW50YXRpb249J3ZlcnRpY2FsJ10gLnAtVGFiQmFyLXRhYiB7CiAgdG9wOiAwOwogIC13ZWJraXQtdHJhbnNpdGlvbjogdG9wIDE1MG1zIGVhc2U7CiAgdHJhbnNpdGlvbjogdG9wIDE1MG1zIGVhc2U7Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIucC1tb2QtZHJhZ2dpbmcgLnAtVGFiQmFyLXRhYi5wLW1vZC1kcmFnZ2luZyB7CiAgLXdlYmtpdC10cmFuc2l0aW9uOiBub25lOwogIHRyYW5zaXRpb246IG5vbmU7Cn0KCiAvKiBFbmQgdGFiYmFyLmNzcyAqLwoKIDpyb290IHsgLyogbWFyZ2luIGJldHdlZW4gaW5saW5lIGVsZW1lbnRzICovCgogICAgLyogRnJvbSBNYXRlcmlhbCBEZXNpZ24gTGl0ZSAqLwp9CgogLmp1cHl0ZXItd2lkZ2V0cyB7CiAgICBtYXJnaW46IDJweDsKICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgIGNvbG9yOiBibGFjazsKICAgIG92ZXJmbG93OiB2aXNpYmxlOwp9CgogLmp1cHl0ZXItd2lkZ2V0cy5qdXB5dGVyLXdpZGdldHMtZGlzY29ubmVjdGVkOjpiZWZvcmUgewogICAgbGluZS1oZWlnaHQ6IDI4cHg7CiAgICBoZWlnaHQ6IDI4cHg7Cn0KCiAuanAtT3V0cHV0LXJlc3VsdCA+IC5qdXB5dGVyLXdpZGdldHMgewogICAgbWFyZ2luLWxlZnQ6IDA7CiAgICBtYXJnaW4tcmlnaHQ6IDA7Cn0KCiAvKiB2Ym94IGFuZCBoYm94ICovCgogLndpZGdldC1pbmxpbmUtaGJveCB7CiAgICAvKiBIb3Jpem9udGFsIHdpZGdldHMgKi8KICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgIGRpc3BsYXk6IC13ZWJraXQtYm94OwogICAgZGlzcGxheTogLW1zLWZsZXhib3g7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgLXdlYmtpdC1ib3gtb3JpZW50OiBob3Jpem9udGFsOwogICAgLXdlYmtpdC1ib3gtZGlyZWN0aW9uOiBub3JtYWw7CiAgICAgICAgLW1zLWZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAtd2Via2l0LWJveC1hbGlnbjogYmFzZWxpbmU7CiAgICAgICAgLW1zLWZsZXgtYWxpZ246IGJhc2VsaW5lOwogICAgICAgICAgICBhbGlnbi1pdGVtczogYmFzZWxpbmU7Cn0KCiAud2lkZ2V0LWlubGluZS12Ym94IHsKICAgIC8qIFZlcnRpY2FsIFdpZGdldHMgKi8KICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgIGRpc3BsYXk6IC13ZWJraXQtYm94OwogICAgZGlzcGxheTogLW1zLWZsZXhib3g7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICAgIC13ZWJraXQtYm94LWRpcmVjdGlvbjogbm9ybWFsOwogICAgICAgIC1tcy1mbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgLXdlYmtpdC1ib3gtYWxpZ246IGNlbnRlcjsKICAgICAgICAtbXMtZmxleC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgogLndpZGdldC1ib3ggewogICAgLXdlYmtpdC1ib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgZGlzcGxheTogLXdlYmtpdC1ib3g7CiAgICBkaXNwbGF5OiAtbXMtZmxleGJveDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBtYXJnaW46IDA7CiAgICBvdmVyZmxvdzogYXV0bzsKfQoKIC53aWRnZXQtZ3JpZGJveCB7CiAgICAtd2Via2l0LWJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICAgICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICBkaXNwbGF5OiBncmlkOwogICAgbWFyZ2luOiAwOwogICAgb3ZlcmZsb3c6IGF1dG87Cn0KCiAud2lkZ2V0LWhib3ggewogICAgLXdlYmtpdC1ib3gtb3JpZW50OiBob3Jpem9udGFsOwogICAgLXdlYmtpdC1ib3gtZGlyZWN0aW9uOiBub3JtYWw7CiAgICAgICAgLW1zLWZsZXgtZGlyZWN0aW9uOiByb3c7CiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7Cn0KCiAud2lkZ2V0LXZib3ggewogICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICAgIC13ZWJraXQtYm94LWRpcmVjdGlvbjogbm9ybWFsOwogICAgICAgIC1tcy1mbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwp9CgogLyogR2VuZXJhbCBCdXR0b24gU3R5bGluZyAqLwoKIC5qdXB5dGVyLWJ1dHRvbiB7CiAgICBwYWRkaW5nLWxlZnQ6IDEwcHg7CiAgICBwYWRkaW5nLXJpZ2h0OiAxMHB4OwogICAgcGFkZGluZy10b3A6IDBweDsKICAgIHBhZGRpbmctYm90dG9tOiAwcHg7CiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgZm9udC1zaXplOiAxM3B4OwogICAgY3Vyc29yOiBwb2ludGVyOwoKICAgIGhlaWdodDogMjhweDsKICAgIGJvcmRlcjogMHB4IHNvbGlkOwogICAgbGluZS1oZWlnaHQ6IDI4cHg7CiAgICAtd2Via2l0LWJveC1zaGFkb3c6IG5vbmU7CiAgICAgICAgICAgIGJveC1zaGFkb3c6IG5vbmU7CgogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjgpOwogICAgYmFja2dyb3VuZC1jb2xvcjogI0VFRUVFRTsKICAgIGJvcmRlci1jb2xvcjogI0UwRTBFMDsKICAgIGJvcmRlcjogbm9uZTsKfQoKIC5qdXB5dGVyLWJ1dHRvbiBpLmZhIHsKICAgIG1hcmdpbi1yaWdodDogNHB4OwogICAgcG9pbnRlci1ldmVudHM6IG5vbmU7Cn0KCiAuanVweXRlci1idXR0b246ZW1wdHk6YmVmb3JlIHsKICAgIGNvbnRlbnQ6ICJcMjAwYiI7IC8qIHplcm8td2lkdGggc3BhY2UgKi8KfQoKIC5qdXB5dGVyLXdpZGdldHMuanVweXRlci1idXR0b246ZGlzYWJsZWQgewogICAgb3BhY2l0eTogMC42Owp9CgogLmp1cHl0ZXItYnV0dG9uIGkuZmEuY2VudGVyIHsKICAgIG1hcmdpbi1yaWdodDogMDsKfQoKIC5qdXB5dGVyLWJ1dHRvbjpob3ZlcjplbmFibGVkLCAuanVweXRlci1idXR0b246Zm9jdXM6ZW5hYmxlZCB7CiAgICAvKiBNRCBMaXRlIDJkcCBzaGFkb3cgKi8KICAgIC13ZWJraXQtYm94LXNoYWRvdzogMCAycHggMnB4IDAgcmdiYSgwLCAwLCAwLCAuMTQpLAogICAgICAgICAgICAgICAgMCAzcHggMXB4IC0ycHggcmdiYSgwLCAwLCAwLCAuMiksCiAgICAgICAgICAgICAgICAwIDFweCA1cHggMCByZ2JhKDAsIDAsIDAsIC4xMik7CiAgICAgICAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDJweCAwIHJnYmEoMCwgMCwgMCwgLjE0KSwKICAgICAgICAgICAgICAgIDAgM3B4IDFweCAtMnB4IHJnYmEoMCwgMCwgMCwgLjIpLAogICAgICAgICAgICAgICAgMCAxcHggNXB4IDAgcmdiYSgwLCAwLCAwLCAuMTIpOwp9CgogLmp1cHl0ZXItYnV0dG9uOmFjdGl2ZSwgLmp1cHl0ZXItYnV0dG9uLm1vZC1hY3RpdmUgewogICAgLyogTUQgTGl0ZSA0ZHAgc2hhZG93ICovCiAgICAtd2Via2l0LWJveC1zaGFkb3c6IDAgNHB4IDVweCAwIHJnYmEoMCwgMCwgMCwgLjE0KSwKICAgICAgICAgICAgICAgIDAgMXB4IDEwcHggMCByZ2JhKDAsIDAsIDAsIC4xMiksCiAgICAgICAgICAgICAgICAwIDJweCA0cHggLTFweCByZ2JhKDAsIDAsIDAsIC4yKTsKICAgICAgICAgICAgYm94LXNoYWRvdzogMCA0cHggNXB4IDAgcmdiYSgwLCAwLCAwLCAuMTQpLAogICAgICAgICAgICAgICAgMCAxcHggMTBweCAwIHJnYmEoMCwgMCwgMCwgLjEyKSwKICAgICAgICAgICAgICAgIDAgMnB4IDRweCAtMXB4IHJnYmEoMCwgMCwgMCwgLjIpOwogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjgpOwogICAgYmFja2dyb3VuZC1jb2xvcjogI0JEQkRCRDsKfQoKIC5qdXB5dGVyLWJ1dHRvbjpmb2N1czplbmFibGVkIHsKICAgIG91dGxpbmU6IDFweCBzb2xpZCAjNjRCNUY2Owp9CgogLyogQnV0dG9uICJQcmltYXJ5IiBTdHlsaW5nICovCgogLmp1cHl0ZXItYnV0dG9uLm1vZC1wcmltYXJ5IHsKICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDEuMCk7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjE5NkYzOwp9CgogLmp1cHl0ZXItYnV0dG9uLm1vZC1wcmltYXJ5Lm1vZC1hY3RpdmUgewogICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSk7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMTk3NkQyOwp9CgogLmp1cHl0ZXItYnV0dG9uLm1vZC1wcmltYXJ5OmFjdGl2ZSB7CiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICMxOTc2RDI7Cn0KCiAvKiBCdXR0b24gIlN1Y2Nlc3MiIFN0eWxpbmcgKi8KCiAuanVweXRlci1idXR0b24ubW9kLXN1Y2Nlc3MgewogICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMS4wKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICM0Q0FGNTA7Cn0KCiAuanVweXRlci1idXR0b24ubW9kLXN1Y2Nlc3MubW9kLWFjdGl2ZSB7CiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICMzODhFM0M7CiB9CgogLmp1cHl0ZXItYnV0dG9uLm1vZC1zdWNjZXNzOmFjdGl2ZSB7CiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICMzODhFM0M7CiB9CgogLyogQnV0dG9uICJJbmZvIiBTdHlsaW5nICovCgogLmp1cHl0ZXItYnV0dG9uLm1vZC1pbmZvIHsKICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDEuMCk7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDBCQ0Q0Owp9CgogLmp1cHl0ZXItYnV0dG9uLm1vZC1pbmZvLm1vZC1hY3RpdmUgewogICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMSk7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDA5N0E3Owp9CgogLmp1cHl0ZXItYnV0dG9uLm1vZC1pbmZvOmFjdGl2ZSB7CiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDk3QTc7Cn0KCiAvKiBCdXR0b24gIldhcm5pbmciIFN0eWxpbmcgKi8KCiAuanVweXRlci1idXR0b24ubW9kLXdhcm5pbmcgewogICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMS4wKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICNGRjk4MDA7Cn0KCiAuanVweXRlci1idXR0b24ubW9kLXdhcm5pbmcubW9kLWFjdGl2ZSB7CiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICNGNTdDMDA7Cn0KCiAuanVweXRlci1idXR0b24ubW9kLXdhcm5pbmc6YWN0aXZlIHsKICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDEpOwogICAgYmFja2dyb3VuZC1jb2xvcjogI0Y1N0MwMDsKfQoKIC8qIEJ1dHRvbiAiRGFuZ2VyIiBTdHlsaW5nICovCgogLmp1cHl0ZXItYnV0dG9uLm1vZC1kYW5nZXIgewogICAgY29sb3I6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMS4wKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICNGNDQzMzY7Cn0KCiAuanVweXRlci1idXR0b24ubW9kLWRhbmdlci5tb2QtYWN0aXZlIHsKICAgIGNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDEpOwogICAgYmFja2dyb3VuZC1jb2xvcjogI0QzMkYyRjsKfQoKIC5qdXB5dGVyLWJ1dHRvbi5tb2QtZGFuZ2VyOmFjdGl2ZSB7CiAgICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKTsKICAgIGJhY2tncm91bmQtY29sb3I6ICNEMzJGMkY7Cn0KCiAvKiBXaWRnZXQgQnV0dG9uKi8KCiAud2lkZ2V0LWJ1dHRvbiwgLndpZGdldC10b2dnbGUtYnV0dG9uIHsKICAgIHdpZHRoOiAxNDhweDsKfQoKIC8qIFdpZGdldCBMYWJlbCBTdHlsaW5nICovCgogLyogT3ZlcnJpZGUgQm9vdHN0cmFwIGxhYmVsIGNzcyAqLwoKIC5qdXB5dGVyLXdpZGdldHMgbGFiZWwgewogICAgbWFyZ2luLWJvdHRvbTogMDsKICAgIG1hcmdpbi1ib3R0b206IGluaXRpYWw7Cn0KCiAud2lkZ2V0LWxhYmVsLWJhc2ljIHsKICAgIC8qIEJhc2ljIExhYmVsICovCiAgICBjb2xvcjogYmxhY2s7CiAgICBmb250LXNpemU6IDEzcHg7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LWxhYmVsIHsKICAgIC8qIExhYmVsICovCiAgICBjb2xvcjogYmxhY2s7CiAgICBmb250LXNpemU6IDEzcHg7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LWlubGluZS1oYm94IC53aWRnZXQtbGFiZWwgewogICAgLyogSG9yaXpvbnRhbCBXaWRnZXQgTGFiZWwgKi8KICAgIGNvbG9yOiBibGFjazsKICAgIHRleHQtYWxpZ246IHJpZ2h0OwogICAgbWFyZ2luLXJpZ2h0OiA4cHg7CiAgICB3aWR0aDogODBweDsKICAgIC1tcy1mbGV4LW5lZ2F0aXZlOiAwOwogICAgICAgIGZsZXgtc2hyaW5rOiAwOwp9CgogLndpZGdldC1pbmxpbmUtdmJveCAud2lkZ2V0LWxhYmVsIHsKICAgIC8qIFZlcnRpY2FsIFdpZGdldCBMYWJlbCAqLwogICAgY29sb3I6IGJsYWNrOwogICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAvKiBXaWRnZXQgUmVhZG91dCBTdHlsaW5nICovCgogLndpZGdldC1yZWFkb3V0IHsKICAgIGNvbG9yOiBibGFjazsKICAgIGZvbnQtc2l6ZTogMTNweDsKICAgIGhlaWdodDogMjhweDsKICAgIGxpbmUtaGVpZ2h0OiAyOHB4OwogICAgb3ZlcmZsb3c6IGhpZGRlbjsKICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7Cn0KCiAud2lkZ2V0LXJlYWRvdXQub3ZlcmZsb3cgewogICAgLyogT3ZlcmZsb3dpbmcgUmVhZG91dCAqLwoKICAgIC8qIEZyb20gTWF0ZXJpYWwgRGVzaWduIExpdGUKICAgICAgICBzaGFkb3cta2V5LXVtYnJhLW9wYWNpdHk6IDAuMjsKICAgICAgICBzaGFkb3cta2V5LXBlbnVtYnJhLW9wYWNpdHk6IDAuMTQ7CiAgICAgICAgc2hhZG93LWFtYmllbnQtc2hhZG93LW9wYWNpdHk6IDAuMTI7CiAgICAgKi8KICAgIC13ZWJraXQtYm94LXNoYWRvdzogMCAycHggMnB4IDAgcmdiYSgwLCAwLCAwLCAuMiksCiAgICAgICAgICAgICAgICAgICAgICAgIDAgM3B4IDFweCAtMnB4IHJnYmEoMCwgMCwgMCwgLjE0KSwKICAgICAgICAgICAgICAgICAgICAgICAgMCAxcHggNXB4IDAgcmdiYSgwLCAwLCAwLCAuMTIpOwoKICAgIGJveC1zaGFkb3c6IDAgMnB4IDJweCAwIHJnYmEoMCwgMCwgMCwgLjIpLAogICAgICAgICAgICAgICAgMCAzcHggMXB4IC0ycHggcmdiYSgwLCAwLCAwLCAuMTQpLAogICAgICAgICAgICAgICAgMCAxcHggNXB4IDAgcmdiYSgwLCAwLCAwLCAuMTIpOwp9CgogLndpZGdldC1pbmxpbmUtaGJveCAud2lkZ2V0LXJlYWRvdXQgewogICAgLyogSG9yaXpvbnRhbCBSZWFkb3V0ICovCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBtYXgtd2lkdGg6IDE0OHB4OwogICAgbWluLXdpZHRoOiA3MnB4OwogICAgbWFyZ2luLWxlZnQ6IDRweDsKfQoKIC53aWRnZXQtaW5saW5lLXZib3ggLndpZGdldC1yZWFkb3V0IHsKICAgIC8qIFZlcnRpY2FsIFJlYWRvdXQgKi8KICAgIG1hcmdpbi10b3A6IDRweDsKICAgIC8qIGFzIHdpZGUgYXMgdGhlIHdpZGdldCAqLwogICAgd2lkdGg6IGluaGVyaXQ7Cn0KCiAvKiBXaWRnZXQgQ2hlY2tib3ggU3R5bGluZyAqLwoKIC53aWRnZXQtY2hlY2tib3ggewogICAgd2lkdGg6IDMwMHB4OwogICAgaGVpZ2h0OiAyOHB4OwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LWNoZWNrYm94IGlucHV0W3R5cGU9ImNoZWNrYm94Il0gewogICAgbWFyZ2luOiAwcHggOHB4IDBweCAwcHg7CiAgICBsaW5lLWhlaWdodDogMjhweDsKICAgIGZvbnQtc2l6ZTogbGFyZ2U7CiAgICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAgIC1tcy1mbGV4LXBvc2l0aXZlOiAxOwogICAgICAgICAgICBmbGV4LWdyb3c6IDE7CiAgICAtbXMtZmxleC1uZWdhdGl2ZTogMDsKICAgICAgICBmbGV4LXNocmluazogMDsKICAgIC1tcy1mbGV4LWl0ZW0tYWxpZ246IGNlbnRlcjsKICAgICAgICBhbGlnbi1zZWxmOiBjZW50ZXI7Cn0KCiAvKiBXaWRnZXQgVmFsaWQgU3R5bGluZyAqLwoKIC53aWRnZXQtdmFsaWQgewogICAgaGVpZ2h0OiAyOHB4OwogICAgbGluZS1oZWlnaHQ6IDI4cHg7CiAgICB3aWR0aDogMTQ4cHg7CiAgICBmb250LXNpemU6IDEzcHg7Cn0KCiAud2lkZ2V0LXZhbGlkIGk6YmVmb3JlIHsKICAgIGxpbmUtaGVpZ2h0OiAyOHB4OwogICAgbWFyZ2luLXJpZ2h0OiA0cHg7CiAgICBtYXJnaW4tbGVmdDogNHB4OwoKICAgIC8qIGZyb20gdGhlIGZhIGNsYXNzIGluIEZvbnRBd2Vzb21lOiBodHRwczovL2dpdGh1Yi5jb20vRm9ydEF3ZXNvbWUvRm9udC1Bd2Vzb21lL2Jsb2IvNDkxMDBjN2MzYTdiNThkNTBiYWE3MWVmZWYxMWFmNDFhNjZiMDNkMy9jc3MvZm9udC1hd2Vzb21lLmNzcyNMMTQgKi8KICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgIGZvbnQ6IG5vcm1hbCBub3JtYWwgbm9ybWFsIDE0cHgvMSBGb250QXdlc29tZTsKICAgIGZvbnQtc2l6ZTogaW5oZXJpdDsKICAgIHRleHQtcmVuZGVyaW5nOiBhdXRvOwogICAgLXdlYmtpdC1mb250LXNtb290aGluZzogYW50aWFsaWFzZWQ7CiAgICAtbW96LW9zeC1mb250LXNtb290aGluZzogZ3JheXNjYWxlOwp9CgogLndpZGdldC12YWxpZC5tb2QtdmFsaWQgaTpiZWZvcmUgewogICAgY29udGVudDogIlxmMDBjIjsKICAgIGNvbG9yOiBncmVlbjsKfQoKIC53aWRnZXQtdmFsaWQubW9kLWludmFsaWQgaTpiZWZvcmUgewogICAgY29udGVudDogIlxmMDBkIjsKICAgIGNvbG9yOiByZWQ7Cn0KCiAud2lkZ2V0LXZhbGlkLm1vZC12YWxpZCAud2lkZ2V0LXZhbGlkLXJlYWRvdXQgewogICAgZGlzcGxheTogbm9uZTsKfQoKIC8qIFdpZGdldCBUZXh0IGFuZCBUZXh0QXJlYSBTdHlpbmcgKi8KCiAud2lkZ2V0LXRleHRhcmVhLCAud2lkZ2V0LXRleHQgewogICAgd2lkdGg6IDMwMHB4Owp9CgogLndpZGdldC10ZXh0IGlucHV0W3R5cGU9InRleHQiXSwgLndpZGdldC10ZXh0IGlucHV0W3R5cGU9Im51bWJlciJdewogICAgaGVpZ2h0OiAyOHB4OwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LXRleHQgaW5wdXRbdHlwZT0idGV4dCJdOmRpc2FibGVkLCAud2lkZ2V0LXRleHQgaW5wdXRbdHlwZT0ibnVtYmVyIl06ZGlzYWJsZWQsIC53aWRnZXQtdGV4dGFyZWEgdGV4dGFyZWE6ZGlzYWJsZWQgewogICAgb3BhY2l0eTogMC42Owp9CgogLndpZGdldC10ZXh0IGlucHV0W3R5cGU9InRleHQiXSwgLndpZGdldC10ZXh0IGlucHV0W3R5cGU9Im51bWJlciJdLCAud2lkZ2V0LXRleHRhcmVhIHRleHRhcmVhIHsKICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgIGJvcmRlcjogMXB4IHNvbGlkICM5RTlFOUU7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIC44KTsKICAgIGZvbnQtc2l6ZTogMTNweDsKICAgIHBhZGRpbmc6IDRweCA4cHg7CiAgICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAgIC1tcy1mbGV4LXBvc2l0aXZlOiAxOwogICAgICAgICAgICBmbGV4LWdyb3c6IDE7CiAgICBtaW4td2lkdGg6IDA7IC8qIFRoaXMgbWFrZXMgaXQgcG9zc2libGUgZm9yIHRoZSBmbGV4Ym94IHRvIHNocmluayB0aGlzIGlucHV0ICovCiAgICAtbXMtZmxleC1uZWdhdGl2ZTogMTsKICAgICAgICBmbGV4LXNocmluazogMTsKICAgIG91dGxpbmU6IG5vbmUgIWltcG9ydGFudDsKfQoKIC53aWRnZXQtdGV4dGFyZWEgdGV4dGFyZWEgewogICAgaGVpZ2h0OiBpbmhlcml0OwogICAgd2lkdGg6IGluaGVyaXQ7Cn0KCiAud2lkZ2V0LXRleHQgaW5wdXQ6Zm9jdXMsIC53aWRnZXQtdGV4dGFyZWEgdGV4dGFyZWE6Zm9jdXMgewogICAgYm9yZGVyLWNvbG9yOiAjNjRCNUY2Owp9CgogLyogV2lkZ2V0IFNsaWRlciAqLwoKIC53aWRnZXQtc2xpZGVyIC51aS1zbGlkZXIgewogICAgLyogU2xpZGVyIFRyYWNrICovCiAgICBib3JkZXI6IDFweCBzb2xpZCAjQkRCREJEOwogICAgYmFja2dyb3VuZDogI0JEQkRCRDsKICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgIGJvcmRlci1yYWRpdXM6IDBweDsKfQoKIC53aWRnZXQtc2xpZGVyIC51aS1zbGlkZXIgLnVpLXNsaWRlci1oYW5kbGUgewogICAgLyogU2xpZGVyIEhhbmRsZSAqLwogICAgb3V0bGluZTogbm9uZSAhaW1wb3J0YW50OyAvKiBmb2N1c2VkIHNsaWRlciBoYW5kbGVzIGFyZSBjb2xvcmVkIC0gc2VlIGJlbG93ICovCiAgICBwb3NpdGlvbjogYWJzb2x1dGU7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsKICAgIGJvcmRlcjogMXB4IHNvbGlkICM5RTlFOUU7CiAgICAtd2Via2l0LWJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICAgICAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICB6LWluZGV4OiAxOwogICAgYmFja2dyb3VuZC1pbWFnZTogbm9uZTsgLyogT3ZlcnJpZGUganF1ZXJ5LXVpICovCn0KCiAvKiBPdmVycmlkZSBqcXVlcnktdWkgKi8KCiAud2lkZ2V0LXNsaWRlciAudWktc2xpZGVyIC51aS1zbGlkZXItaGFuZGxlOmhvdmVyLCAud2lkZ2V0LXNsaWRlciAudWktc2xpZGVyIC51aS1zbGlkZXItaGFuZGxlOmZvY3VzIHsKICAgIGJhY2tncm91bmQtY29sb3I6ICMyMTk2RjM7CiAgICBib3JkZXI6IDFweCBzb2xpZCAjMjE5NkYzOwp9CgogLndpZGdldC1zbGlkZXIgLnVpLXNsaWRlciAudWktc2xpZGVyLWhhbmRsZTphY3RpdmUgewogICAgYmFja2dyb3VuZC1jb2xvcjogIzIxOTZGMzsKICAgIGJvcmRlci1jb2xvcjogIzIxOTZGMzsKICAgIHotaW5kZXg6IDI7CiAgICAtd2Via2l0LXRyYW5zZm9ybTogc2NhbGUoMS4yKTsKICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjIpOwp9CgogLndpZGdldC1zbGlkZXIgIC51aS1zbGlkZXIgLnVpLXNsaWRlci1yYW5nZSB7CiAgICAvKiBJbnRlcnZhbCBiZXR3ZWVuIHRoZSB0d28gc3BlY2lmaWVkIHZhbHVlIG9mIGEgZG91YmxlIHNsaWRlciAqLwogICAgcG9zaXRpb246IGFic29sdXRlOwogICAgYmFja2dyb3VuZDogIzIxOTZGMzsKICAgIHotaW5kZXg6IDA7Cn0KCiAvKiBTaGFwZXMgb2YgU2xpZGVyIEhhbmRsZXMgKi8KCiAud2lkZ2V0LWhzbGlkZXIgLnVpLXNsaWRlciAudWktc2xpZGVyLWhhbmRsZSB7CiAgICB3aWR0aDogMTZweDsKICAgIGhlaWdodDogMTZweDsKICAgIG1hcmdpbi10b3A6IC03cHg7CiAgICBtYXJnaW4tbGVmdDogLTdweDsKICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgIHRvcDogMDsKfQoKIC53aWRnZXQtdnNsaWRlciAudWktc2xpZGVyIC51aS1zbGlkZXItaGFuZGxlIHsKICAgIHdpZHRoOiAxNnB4OwogICAgaGVpZ2h0OiAxNnB4OwogICAgbWFyZ2luLWJvdHRvbTogLTdweDsKICAgIG1hcmdpbi1sZWZ0OiAtN3B4OwogICAgYm9yZGVyLXJhZGl1czogNTAlOwogICAgbGVmdDogMDsKfQoKIC53aWRnZXQtaHNsaWRlciAudWktc2xpZGVyIC51aS1zbGlkZXItcmFuZ2UgewogICAgaGVpZ2h0OiA4cHg7CiAgICBtYXJnaW4tdG9wOiAtM3B4Owp9CgogLndpZGdldC12c2xpZGVyIC51aS1zbGlkZXIgLnVpLXNsaWRlci1yYW5nZSB7CiAgICB3aWR0aDogOHB4OwogICAgbWFyZ2luLWxlZnQ6IC0zcHg7Cn0KCiAvKiBIb3Jpem9udGFsIFNsaWRlciAqLwoKIC53aWRnZXQtaHNsaWRlciB7CiAgICB3aWR0aDogMzAwcHg7CiAgICBoZWlnaHQ6IDI4cHg7CiAgICBsaW5lLWhlaWdodDogMjhweDsKCiAgICAvKiBPdmVycmlkZSB0aGUgYWxpZ24taXRlbXMgYmFzZWxpbmUuIFRoaXMgd2F5LCB0aGUgZGVzY3JpcHRpb24gYW5kIHJlYWRvdXQKICAgIHN0aWxsIHNlZW0gdG8gYWxpZ24gdGhlaXIgYmFzZWxpbmUgcHJvcGVybHksIGFuZCB3ZSBkb24ndCBoYXZlIHRvIGhhdmUKICAgIGFsaWduLXNlbGY6IHN0cmV0Y2ggaW4gdGhlIC5zbGlkZXItY29udGFpbmVyLiAqLwogICAgLXdlYmtpdC1ib3gtYWxpZ246IGNlbnRlcjsKICAgICAgICAtbXMtZmxleC1hbGlnbjogY2VudGVyOwogICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgogLndpZGdldHMtc2xpZGVyIC5zbGlkZXItY29udGFpbmVyIHsKICAgIG92ZXJmbG93OiB2aXNpYmxlOwp9CgogLndpZGdldC1oc2xpZGVyIC5zbGlkZXItY29udGFpbmVyIHsKICAgIGhlaWdodDogMjhweDsKICAgIG1hcmdpbi1sZWZ0OiA2cHg7CiAgICBtYXJnaW4tcmlnaHQ6IDZweDsKICAgIC13ZWJraXQtYm94LWZsZXg6IDE7CiAgICAgICAgLW1zLWZsZXg6IDEgMSAxNDhweDsKICAgICAgICAgICAgZmxleDogMSAxIDE0OHB4Owp9CgogLndpZGdldC1oc2xpZGVyIC51aS1zbGlkZXIgewogICAgLyogSW5uZXIsIGludmlzaWJsZSBzbGlkZSBkaXYgKi8KICAgIGhlaWdodDogNHB4OwogICAgbWFyZ2luLXRvcDogMTJweDsKICAgIHdpZHRoOiAxMDAlOwp9CgogLyogVmVydGljYWwgU2xpZGVyICovCgogLndpZGdldC12Ym94IC53aWRnZXQtbGFiZWwgewogICAgaGVpZ2h0OiAyOHB4OwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LXZzbGlkZXIgewogICAgLyogVmVydGljYWwgU2xpZGVyICovCiAgICBoZWlnaHQ6IDIwMHB4OwogICAgd2lkdGg6IDcycHg7Cn0KCiAud2lkZ2V0LXZzbGlkZXIgLnNsaWRlci1jb250YWluZXIgewogICAgLXdlYmtpdC1ib3gtZmxleDogMTsKICAgICAgICAtbXMtZmxleDogMSAxIDE0OHB4OwogICAgICAgICAgICBmbGV4OiAxIDEgMTQ4cHg7CiAgICBtYXJnaW4tbGVmdDogYXV0bzsKICAgIG1hcmdpbi1yaWdodDogYXV0bzsKICAgIG1hcmdpbi1ib3R0b206IDZweDsKICAgIG1hcmdpbi10b3A6IDZweDsKICAgIGRpc3BsYXk6IC13ZWJraXQtYm94OwogICAgZGlzcGxheTogLW1zLWZsZXhib3g7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICAgIC13ZWJraXQtYm94LWRpcmVjdGlvbjogbm9ybWFsOwogICAgICAgIC1tcy1mbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwp9CgogLndpZGdldC12c2xpZGVyIC51aS1zbGlkZXItdmVydGljYWwgewogICAgLyogSW5uZXIsIGludmlzaWJsZSBzbGlkZSBkaXYgKi8KICAgIHdpZHRoOiA0cHg7CiAgICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAgIC1tcy1mbGV4LXBvc2l0aXZlOiAxOwogICAgICAgICAgICBmbGV4LWdyb3c6IDE7CiAgICBtYXJnaW4tbGVmdDogYXV0bzsKICAgIG1hcmdpbi1yaWdodDogYXV0bzsKfQoKIC8qIFdpZGdldCBQcm9ncmVzcyBTdHlsaW5nICovCgogLnByb2dyZXNzLWJhciB7CiAgICAtd2Via2l0LXRyYW5zaXRpb246IG5vbmU7CiAgICB0cmFuc2l0aW9uOiBub25lOwp9CgogLnByb2dyZXNzLWJhciB7CiAgICBoZWlnaHQ6IDI4cHg7Cn0KCiAucHJvZ3Jlc3MtYmFyIHsKICAgIGJhY2tncm91bmQtY29sb3I6ICMyMTk2RjM7Cn0KCiAucHJvZ3Jlc3MtYmFyLXN1Y2Nlc3MgewogICAgYmFja2dyb3VuZC1jb2xvcjogIzRDQUY1MDsKfQoKIC5wcm9ncmVzcy1iYXItaW5mbyB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMDBCQ0Q0Owp9CgogLnByb2dyZXNzLWJhci13YXJuaW5nIHsKICAgIGJhY2tncm91bmQtY29sb3I6ICNGRjk4MDA7Cn0KCiAucHJvZ3Jlc3MtYmFyLWRhbmdlciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjQ0MzM2Owp9CgogLnByb2dyZXNzIHsKICAgIGJhY2tncm91bmQtY29sb3I6ICNFRUVFRUU7CiAgICBib3JkZXI6IG5vbmU7CiAgICAtd2Via2l0LWJveC1zaGFkb3c6IG5vbmU7CiAgICAgICAgICAgIGJveC1zaGFkb3c6IG5vbmU7Cn0KCiAvKiBIb3Jpc29udGFsIFByb2dyZXNzICovCgogLndpZGdldC1ocHJvZ3Jlc3MgewogICAgLyogUHJvZ3Jlc3MgQmFyICovCiAgICBoZWlnaHQ6IDI4cHg7CiAgICBsaW5lLWhlaWdodDogMjhweDsKICAgIHdpZHRoOiAzMDBweDsKICAgIC13ZWJraXQtYm94LWFsaWduOiBjZW50ZXI7CiAgICAgICAgLW1zLWZsZXgtYWxpZ246IGNlbnRlcjsKICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKCn0KCiAud2lkZ2V0LWhwcm9ncmVzcyAucHJvZ3Jlc3MgewogICAgLXdlYmtpdC1ib3gtZmxleDogMTsKICAgICAgICAtbXMtZmxleC1wb3NpdGl2ZTogMTsKICAgICAgICAgICAgZmxleC1ncm93OiAxOwogICAgbWFyZ2luLXRvcDogNHB4OwogICAgbWFyZ2luLWJvdHRvbTogNHB4OwogICAgLW1zLWZsZXgtaXRlbS1hbGlnbjogc3RyZXRjaDsKICAgICAgICBhbGlnbi1zZWxmOiBzdHJldGNoOwogICAgLyogT3ZlcnJpZGUgYm9vdHN0cmFwIHN0eWxlICovCiAgICBoZWlnaHQ6IGF1dG87CiAgICBoZWlnaHQ6IGluaXRpYWw7Cn0KCiAvKiBWZXJ0aWNhbCBQcm9ncmVzcyAqLwoKIC53aWRnZXQtdnByb2dyZXNzIHsKICAgIGhlaWdodDogMjAwcHg7CiAgICB3aWR0aDogNzJweDsKfQoKIC53aWRnZXQtdnByb2dyZXNzIC5wcm9ncmVzcyB7CiAgICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAgIC1tcy1mbGV4LXBvc2l0aXZlOiAxOwogICAgICAgICAgICBmbGV4LWdyb3c6IDE7CiAgICB3aWR0aDogMjBweDsKICAgIG1hcmdpbi1sZWZ0OiBhdXRvOwogICAgbWFyZ2luLXJpZ2h0OiBhdXRvOwogICAgbWFyZ2luLWJvdHRvbTogMDsKfQoKIC8qIFNlbGVjdCBXaWRnZXQgU3R5bGluZyAqLwoKIC53aWRnZXQtZHJvcGRvd24gewogICAgaGVpZ2h0OiAyOHB4OwogICAgd2lkdGg6IDMwMHB4OwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LWRyb3Bkb3duID4gc2VsZWN0IHsKICAgIHBhZGRpbmctcmlnaHQ6IDIwcHg7CiAgICBib3JkZXI6IDFweCBzb2xpZCAjOUU5RTlFOwogICAgYm9yZGVyLXJhZGl1czogMDsKICAgIGhlaWdodDogaW5oZXJpdDsKICAgIC13ZWJraXQtYm94LWZsZXg6IDE7CiAgICAgICAgLW1zLWZsZXg6IDEgMSAxNDhweDsKICAgICAgICAgICAgZmxleDogMSAxIDE0OHB4OwogICAgbWluLXdpZHRoOiAwOyAvKiBUaGlzIG1ha2VzIGl0IHBvc3NpYmxlIGZvciB0aGUgZmxleGJveCB0byBzaHJpbmsgdGhpcyBpbnB1dCAqLwogICAgLXdlYmtpdC1ib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgb3V0bGluZTogbm9uZSAhaW1wb3J0YW50OwogICAgLXdlYmtpdC1ib3gtc2hhZG93OiBub25lOwogICAgICAgICAgICBib3gtc2hhZG93OiBub25lOwogICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7CiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAuOCk7CiAgICBmb250LXNpemU6IDEzcHg7CiAgICB2ZXJ0aWNhbC1hbGlnbjogdG9wOwogICAgcGFkZGluZy1sZWZ0OiA4cHg7CglhcHBlYXJhbmNlOiBub25lOwoJLXdlYmtpdC1hcHBlYXJhbmNlOiBub25lOwoJLW1vei1hcHBlYXJhbmNlOiBub25lOwogICAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDsKCWJhY2tncm91bmQtc2l6ZTogMjBweDsKCWJhY2tncm91bmQtcG9zaXRpb246IHJpZ2h0IGNlbnRlcjsKICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQRDk0Yld3Z2RtVnljMmx2YmowaU1TNHdJaUJsYm1OdlpHbHVaejBpZFhSbUxUZ2lQejRLUENFdExTQkhaVzVsY21GMGIzSTZJRUZrYjJKbElFbHNiSFZ6ZEhKaGRHOXlJREU1TGpJdU1Td2dVMVpISUVWNGNHOXlkQ0JRYkhWbkxVbHVJQzRnVTFaSElGWmxjbk5wYjI0NklEWXVNREFnUW5WcGJHUWdNQ2tnSUMwdFBnbzhjM1puSUhabGNuTnBiMjQ5SWpFdU1TSWdhV1E5SWt4aGVXVnlYekVpSUhodGJHNXpQU0pvZEhSd09pOHZkM2QzTG5jekxtOXlaeTh5TURBd0wzTjJaeUlnZUcxc2JuTTZlR3hwYm1zOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6RTVPVGt2ZUd4cGJtc2lJSGc5SWpCd2VDSWdlVDBpTUhCNElnb0pJSFpwWlhkQ2IzZzlJakFnTUNBeE9DQXhPQ0lnYzNSNWJHVTlJbVZ1WVdKc1pTMWlZV05yWjNKdmRXNWtPbTVsZHlBd0lEQWdNVGdnTVRnN0lpQjRiV3c2YzNCaFkyVTlJbkJ5WlhObGNuWmxJajRLUEhOMGVXeGxJSFI1Y0dVOUluUmxlSFF2WTNOeklqNEtDUzV6ZERCN1ptbHNiRHB1YjI1bE8zMEtQQzl6ZEhsc1pUNEtQSEJoZEdnZ1pEMGlUVFV1TWl3MUxqbE1PU3c1TGpkc015NDRMVE11T0d3eExqSXNNUzR5YkMwMExqa3NOV3d0TkM0NUxUVk1OUzR5TERVdU9Yb2lMejRLUEhCaGRHZ2dZMnhoYzNNOUluTjBNQ0lnWkQwaVRUQXRNQzQyYURFNGRqRTRTREJXTFRBdU5ub2lMejRLUEM5emRtYytDZyIpOwp9CgogLndpZGdldC1kcm9wZG93biA+IHNlbGVjdDpmb2N1cyB7CiAgICBib3JkZXItY29sb3I6ICM2NEI1RjY7Cn0KCiAud2lkZ2V0LWRyb3Bkb3duID4gc2VsZWN0OmRpc2FibGVkIHsKICAgIG9wYWNpdHk6IDAuNjsKfQoKIC8qIFRvIGRpc2FibGUgdGhlIGRvdHRlZCBib3JkZXIgaW4gRmlyZWZveCBhcm91bmQgc2VsZWN0IGNvbnRyb2xzLgogICBTZWUgaHR0cDovL3N0YWNrb3ZlcmZsb3cuY29tL2EvMTg4NTMwMDIgKi8KCiAud2lkZ2V0LWRyb3Bkb3duID4gc2VsZWN0Oi1tb3otZm9jdXNyaW5nIHsKICAgIGNvbG9yOiB0cmFuc3BhcmVudDsKICAgIHRleHQtc2hhZG93OiAwIDAgMCAjMDAwOwp9CgogLyogU2VsZWN0IGFuZCBTZWxlY3RNdWx0aXBsZSAqLwoKIC53aWRnZXQtc2VsZWN0IHsKICAgIHdpZHRoOiAzMDBweDsKICAgIGxpbmUtaGVpZ2h0OiAyOHB4OwoKICAgIC8qIEJlY2F1c2UgRmlyZWZveCBkZWZpbmVzIHRoZSBiYXNlbGluZSBvZiBhIHNlbGVjdCBhcyB0aGUgYm90dG9tIG9mIHRoZQogICAgY29udHJvbCwgd2UgYWxpZ24gdGhlIGVudGlyZSBjb250cm9sIHRvIHRoZSB0b3AgYW5kIGFkZCBwYWRkaW5nIHRvIHRoZQogICAgc2VsZWN0IHRvIGdldCBhbiBhcHByb3hpbWF0ZSBmaXJzdCBsaW5lIGJhc2VsaW5lIGFsaWdubWVudC4gKi8KICAgIC13ZWJraXQtYm94LWFsaWduOiBzdGFydDsKICAgICAgICAtbXMtZmxleC1hbGlnbjogc3RhcnQ7CiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0Owp9CgogLndpZGdldC1zZWxlY3QgPiBzZWxlY3QgewogICAgYm9yZGVyOiAxcHggc29saWQgIzlFOUU5RTsKICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOwogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjgpOwogICAgZm9udC1zaXplOiAxM3B4OwogICAgLXdlYmtpdC1ib3gtZmxleDogMTsKICAgICAgICAtbXMtZmxleDogMSAxIDE0OHB4OwogICAgICAgICAgICBmbGV4OiAxIDEgMTQ4cHg7CiAgICBvdXRsaW5lOiBub25lICFpbXBvcnRhbnQ7CiAgICBvdmVyZmxvdzogYXV0bzsKICAgIGhlaWdodDogaW5oZXJpdDsKCiAgICAvKiBCZWNhdXNlIEZpcmVmb3ggZGVmaW5lcyB0aGUgYmFzZWxpbmUgb2YgYSBzZWxlY3QgYXMgdGhlIGJvdHRvbSBvZiB0aGUKICAgIGNvbnRyb2wsIHdlIGFsaWduIHRoZSBlbnRpcmUgY29udHJvbCB0byB0aGUgdG9wIGFuZCBhZGQgcGFkZGluZyB0byB0aGUKICAgIHNlbGVjdCB0byBnZXQgYW4gYXBwcm94aW1hdGUgZmlyc3QgbGluZSBiYXNlbGluZSBhbGlnbm1lbnQuICovCiAgICBwYWRkaW5nLXRvcDogNXB4Owp9CgogLndpZGdldC1zZWxlY3QgPiBzZWxlY3Q6Zm9jdXMgewogICAgYm9yZGVyLWNvbG9yOiAjNjRCNUY2Owp9CgogLndpZ2V0LXNlbGVjdCA+IHNlbGVjdCA+IG9wdGlvbiB7CiAgICBwYWRkaW5nLWxlZnQ6IDRweDsKICAgIGxpbmUtaGVpZ2h0OiAyOHB4OwogICAgLyogbGluZS1oZWlnaHQgZG9lc24ndCB3b3JrIG9uIHNvbWUgYnJvd3NlcnMgZm9yIHNlbGVjdCBvcHRpb25zICovCiAgICBwYWRkaW5nLXRvcDogY2FsYygyOHB4IC0gdmFyKC0tanAtd2lkZ2V0cy1mb250LXNpemUpIC8gMik7CiAgICBwYWRkaW5nLWJvdHRvbTogY2FsYygyOHB4IC0gdmFyKC0tanAtd2lkZ2V0cy1mb250LXNpemUpIC8gMik7Cn0KCiAvKiBUb2dnbGUgQnV0dG9ucyBTdHlsaW5nICovCgogLndpZGdldC10b2dnbGUtYnV0dG9ucyB7CiAgICBsaW5lLWhlaWdodDogMjhweDsKfQoKIC53aWRnZXQtdG9nZ2xlLWJ1dHRvbnMgLndpZGdldC10b2dnbGUtYnV0dG9uIHsKICAgIG1hcmdpbi1sZWZ0OiAycHg7CiAgICBtYXJnaW4tcmlnaHQ6IDJweDsKfQoKIC53aWRnZXQtdG9nZ2xlLWJ1dHRvbnMgLmp1cHl0ZXItYnV0dG9uOmRpc2FibGVkIHsKICAgIG9wYWNpdHk6IDAuNjsKfQoKIC8qIFJhZGlvIEJ1dHRvbnMgU3R5bGluZyAqLwoKIC53aWRnZXQtcmFkaW8gewogICAgd2lkdGg6IDMwMHB4OwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LXJhZGlvLWJveCB7CiAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsKICAgIGRpc3BsYXk6IC1tcy1mbGV4Ym94OwogICAgZGlzcGxheTogZmxleDsKICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7CiAgICAtd2Via2l0LWJveC1kaXJlY3Rpb246IG5vcm1hbDsKICAgICAgICAtbXMtZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIC13ZWJraXQtYm94LWFsaWduOiBzdHJldGNoOwogICAgICAgIC1tcy1mbGV4LWFsaWduOiBzdHJldGNoOwogICAgICAgICAgICBhbGlnbi1pdGVtczogc3RyZXRjaDsKICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgIC13ZWJraXQtYm94LWZsZXg6IDE7CiAgICAgICAgLW1zLWZsZXgtcG9zaXRpdmU6IDE7CiAgICAgICAgICAgIGZsZXgtZ3JvdzogMTsKICAgIG1hcmdpbi1ib3R0b206IDhweDsKfQoKIC53aWRnZXQtcmFkaW8tYm94IGxhYmVsIHsKICAgIGhlaWdodDogMjBweDsKICAgIGxpbmUtaGVpZ2h0OiAyMHB4OwogICAgZm9udC1zaXplOiAxM3B4Owp9CgogLndpZGdldC1yYWRpby1ib3ggaW5wdXQgewogICAgaGVpZ2h0OiAyMHB4OwogICAgbGluZS1oZWlnaHQ6IDIwcHg7CiAgICBtYXJnaW46IDAgOHB4IDAgMXB4OwogICAgZmxvYXQ6IGxlZnQ7Cn0KCiAvKiBDb2xvciBQaWNrZXIgU3R5bGluZyAqLwoKIC53aWRnZXQtY29sb3JwaWNrZXIgewogICAgd2lkdGg6IDMwMHB4OwogICAgaGVpZ2h0OiAyOHB4OwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LWNvbG9ycGlja2VyID4gLndpZGdldC1jb2xvcnBpY2tlci1pbnB1dCB7CiAgICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAgIC1tcy1mbGV4LXBvc2l0aXZlOiAxOwogICAgICAgICAgICBmbGV4LWdyb3c6IDE7CiAgICAtbXMtZmxleC1uZWdhdGl2ZTogMTsKICAgICAgICBmbGV4LXNocmluazogMTsKICAgIG1pbi13aWR0aDogNzJweDsKfQoKIC53aWRnZXQtY29sb3JwaWNrZXIgaW5wdXRbdHlwZT0iY29sb3IiXSB7CiAgICB3aWR0aDogMjhweDsKICAgIGhlaWdodDogMjhweDsKICAgIHBhZGRpbmc6IDAgMnB4OyAvKiBtYWtlIHRoZSBjb2xvciBzcXVhcmUgYWN0dWFsbHkgc3F1YXJlIG9uIENocm9tZSBvbiBPUyBYICovCiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIC44KTsKICAgIGJvcmRlcjogMXB4IHNvbGlkICM5RTlFOUU7CiAgICBib3JkZXItbGVmdDogbm9uZTsKICAgIC13ZWJraXQtYm94LWZsZXg6IDA7CiAgICAgICAgLW1zLWZsZXgtcG9zaXRpdmU6IDA7CiAgICAgICAgICAgIGZsZXgtZ3JvdzogMDsKICAgIC1tcy1mbGV4LW5lZ2F0aXZlOiAwOwogICAgICAgIGZsZXgtc2hyaW5rOiAwOwogICAgLXdlYmtpdC1ib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgICAgICAgICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogICAgLW1zLWZsZXgtaXRlbS1hbGlnbjogc3RyZXRjaDsKICAgICAgICBhbGlnbi1zZWxmOiBzdHJldGNoOwogICAgb3V0bGluZTogbm9uZSAhaW1wb3J0YW50Owp9CgogLndpZGdldC1jb2xvcnBpY2tlci5jb25jaXNlIGlucHV0W3R5cGU9ImNvbG9yIl0gewogICAgYm9yZGVyLWxlZnQ6IDFweCBzb2xpZCAjOUU5RTlFOwp9CgogLndpZGdldC1jb2xvcnBpY2tlciBpbnB1dFt0eXBlPSJjb2xvciJdOmZvY3VzLCAud2lkZ2V0LWNvbG9ycGlja2VyIGlucHV0W3R5cGU9InRleHQiXTpmb2N1cyB7CiAgICBib3JkZXItY29sb3I6ICM2NEI1RjY7Cn0KCiAud2lkZ2V0LWNvbG9ycGlja2VyIGlucHV0W3R5cGU9InRleHQiXSB7CiAgICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAgIC1tcy1mbGV4LXBvc2l0aXZlOiAxOwogICAgICAgICAgICBmbGV4LWdyb3c6IDE7CiAgICBvdXRsaW5lOiBub25lICFpbXBvcnRhbnQ7CiAgICBoZWlnaHQ6IDI4cHg7CiAgICBsaW5lLWhlaWdodDogMjhweDsKICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjgpOwogICAgYm9yZGVyOiAxcHggc29saWQgIzlFOUU5RTsKICAgIGZvbnQtc2l6ZTogMTNweDsKICAgIHBhZGRpbmc6IDRweCA4cHg7CiAgICBtaW4td2lkdGg6IDA7IC8qIFRoaXMgbWFrZXMgaXQgcG9zc2libGUgZm9yIHRoZSBmbGV4Ym94IHRvIHNocmluayB0aGlzIGlucHV0ICovCiAgICAtbXMtZmxleC1uZWdhdGl2ZTogMTsKICAgICAgICBmbGV4LXNocmluazogMTsKICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKfQoKIC53aWRnZXQtY29sb3JwaWNrZXIgaW5wdXRbdHlwZT0idGV4dCJdOmRpc2FibGVkIHsKICAgIG9wYWNpdHk6IDAuNjsKfQoKIC8qIERhdGUgUGlja2VyIFN0eWxpbmcgKi8KCiAud2lkZ2V0LWRhdGVwaWNrZXIgewogICAgd2lkdGg6IDMwMHB4OwogICAgaGVpZ2h0OiAyOHB4OwogICAgbGluZS1oZWlnaHQ6IDI4cHg7Cn0KCiAud2lkZ2V0LWRhdGVwaWNrZXIgaW5wdXRbdHlwZT0iZGF0ZSJdIHsKICAgIC13ZWJraXQtYm94LWZsZXg6IDE7CiAgICAgICAgLW1zLWZsZXgtcG9zaXRpdmU6IDE7CiAgICAgICAgICAgIGZsZXgtZ3JvdzogMTsKICAgIC1tcy1mbGV4LW5lZ2F0aXZlOiAxOwogICAgICAgIGZsZXgtc2hyaW5rOiAxOwogICAgbWluLXdpZHRoOiAwOyAvKiBUaGlzIG1ha2VzIGl0IHBvc3NpYmxlIGZvciB0aGUgZmxleGJveCB0byBzaHJpbmsgdGhpcyBpbnB1dCAqLwogICAgb3V0bGluZTogbm9uZSAhaW1wb3J0YW50OwogICAgaGVpZ2h0OiAyOHB4OwogICAgYm9yZGVyOiAxcHggc29saWQgIzlFOUU5RTsKICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOwogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjgpOwogICAgZm9udC1zaXplOiAxM3B4OwogICAgcGFkZGluZzogNHB4IDhweDsKICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKfQoKIC53aWRnZXQtZGF0ZXBpY2tlciBpbnB1dFt0eXBlPSJkYXRlIl06Zm9jdXMgewogICAgYm9yZGVyLWNvbG9yOiAjNjRCNUY2Owp9CgogLndpZGdldC1kYXRlcGlja2VyIGlucHV0W3R5cGU9ImRhdGUiXTppbnZhbGlkIHsKICAgIGJvcmRlci1jb2xvcjogI0ZGOTgwMDsKfQoKIC53aWRnZXQtZGF0ZXBpY2tlciBpbnB1dFt0eXBlPSJkYXRlIl06ZGlzYWJsZWQgewogICAgb3BhY2l0eTogMC42Owp9CgogLyogUGxheSBXaWRnZXQgKi8KCiAud2lkZ2V0LXBsYXkgewogICAgd2lkdGg6IDE0OHB4OwogICAgZGlzcGxheTogLXdlYmtpdC1ib3g7CiAgICBkaXNwbGF5OiAtbXMtZmxleGJveDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICAtd2Via2l0LWJveC1hbGlnbjogc3RyZXRjaDsKICAgICAgICAtbXMtZmxleC1hbGlnbjogc3RyZXRjaDsKICAgICAgICAgICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7Cn0KCiAud2lkZ2V0LXBsYXkgLmp1cHl0ZXItYnV0dG9uIHsKICAgIC13ZWJraXQtYm94LWZsZXg6IDE7CiAgICAgICAgLW1zLWZsZXgtcG9zaXRpdmU6IDE7CiAgICAgICAgICAgIGZsZXgtZ3JvdzogMTsKICAgIGhlaWdodDogYXV0bzsKfQoKIC53aWRnZXQtcGxheSAuanVweXRlci1idXR0b246ZGlzYWJsZWQgewogICAgb3BhY2l0eTogMC42Owp9CgogLyogVGFiIFdpZGdldCAqLwoKIC5qdXB5dGVyLXdpZGdldHMud2lkZ2V0LXRhYiB7CiAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsKICAgIGRpc3BsYXk6IC1tcy1mbGV4Ym94OwogICAgZGlzcGxheTogZmxleDsKICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7CiAgICAtd2Via2l0LWJveC1kaXJlY3Rpb246IG5vcm1hbDsKICAgICAgICAtbXMtZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKfQoKIC5qdXB5dGVyLXdpZGdldHMud2lkZ2V0LXRhYiA+IC5wLVRhYkJhciB7CiAgICAvKiBOZWNlc3Nhcnkgc28gdGhhdCBhIHRhYiBjYW4gYmUgc2hpZnRlZCBkb3duIHRvIG92ZXJsYXkgdGhlIGJvcmRlciBvZiB0aGUgYm94IGJlbG93LiAqLwogICAgb3ZlcmZsb3cteDogdmlzaWJsZTsKICAgIG92ZXJmbG93LXk6IHZpc2libGU7Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIgPiAucC1UYWJCYXItY29udGVudCB7CiAgICAvKiBNYWtlIHN1cmUgdGhhdCB0aGUgdGFiIGdyb3dzIGZyb20gYm90dG9tIHVwICovCiAgICAtd2Via2l0LWJveC1hbGlnbjogZW5kOwogICAgICAgIC1tcy1mbGV4LWFsaWduOiBlbmQ7CiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LWVuZDsKICAgIG1pbi13aWR0aDogMDsKICAgIG1pbi1oZWlnaHQ6IDA7Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAud2lkZ2V0LXRhYi1jb250ZW50cyB7CiAgICB3aWR0aDogMTAwJTsKICAgIC13ZWJraXQtYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgICAgICAgICAgYm94LXNpemluZzogYm9yZGVyLWJveDsKICAgIG1hcmdpbjogMDsKICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjgpOwogICAgYm9yZGVyOiAxcHggc29saWQgIzlFOUU5RTsKICAgIHBhZGRpbmc6IDE1cHg7CiAgICAtd2Via2l0LWJveC1mbGV4OiAxOwogICAgICAgIC1tcy1mbGV4LXBvc2l0aXZlOiAxOwogICAgICAgICAgICBmbGV4LWdyb3c6IDE7CiAgICBvdmVyZmxvdzogYXV0bzsKfQoKIC5qdXB5dGVyLXdpZGdldHMud2lkZ2V0LXRhYiA+IC5wLVRhYkJhciB7CiAgICBmb250OiAxM3B4IEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7CiAgICBtaW4taGVpZ2h0OiAyNXB4Owp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyIC5wLVRhYkJhci10YWIgewogICAgLXdlYmtpdC1ib3gtZmxleDogMDsKICAgICAgICAtbXMtZmxleDogMCAxIDE0NHB4OwogICAgICAgICAgICBmbGV4OiAwIDEgMTQ0cHg7CiAgICBtaW4td2lkdGg6IDM1cHg7CiAgICBtaW4taGVpZ2h0OiAyNXB4OwogICAgbGluZS1oZWlnaHQ6IDI0cHg7CiAgICBtYXJnaW4tbGVmdDogLTFweDsKICAgIHBhZGRpbmc6IDBweCAxMHB4OwogICAgYmFja2dyb3VuZDogI0VFRUVFRTsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIC41KTsKICAgIGJvcmRlcjogMXB4IHNvbGlkICM5RTlFOUU7CiAgICBib3JkZXItYm90dG9tOiBub25lOwogICAgcG9zaXRpb246IHJlbGF0aXZlOwp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyIC5wLVRhYkJhci10YWIucC1tb2QtY3VycmVudCB7CiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAxLjApOwogICAgLyogV2Ugd2FudCB0aGUgYmFja2dyb3VuZCB0byBtYXRjaCB0aGUgdGFiIGNvbnRlbnQgYmFja2dyb3VuZCAqLwogICAgYmFja2dyb3VuZDogd2hpdGU7CiAgICBtaW4taGVpZ2h0OiAyNnB4OwogICAgLXdlYmtpdC10cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMXB4KTsKICAgICAgICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDFweCk7CiAgICBvdmVyZmxvdzogdmlzaWJsZTsKfQoKIC5qdXB5dGVyLXdpZGdldHMud2lkZ2V0LXRhYiA+IC5wLVRhYkJhciAucC1UYWJCYXItdGFiLnAtbW9kLWN1cnJlbnQ6YmVmb3JlIHsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogLTFweDsKICAgIGxlZnQ6IC0xcHg7CiAgICBjb250ZW50OiAnJzsKICAgIGhlaWdodDogMnB4OwogICAgd2lkdGg6IGNhbGMoMTAwJSArIDJweCk7CiAgICBiYWNrZ3JvdW5kOiAjMjE5NkYzOwp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyIC5wLVRhYkJhci10YWI6Zmlyc3QtY2hpbGQgewogICAgbWFyZ2luLWxlZnQ6IDA7Cn0KCiAuanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIgLnAtVGFiQmFyLXRhYjpob3Zlcjpub3QoLnAtbW9kLWN1cnJlbnQpIHsKICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjgpOwp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyIC5wLW1vZC1jbG9zYWJsZSA+IC5wLVRhYkJhci10YWJDbG9zZUljb24gewogICAgbWFyZ2luLWxlZnQ6IDRweDsKfQoKIC5qdXB5dGVyLXdpZGdldHMud2lkZ2V0LXRhYiA+IC5wLVRhYkJhciAucC1tb2QtY2xvc2FibGUgPiAucC1UYWJCYXItdGFiQ2xvc2VJY29uOmJlZm9yZSB7CiAgICBmb250LWZhbWlseTogRm9udEF3ZXNvbWU7CiAgICBjb250ZW50OiAnXGYwMGQnOyAvKiBjbG9zZSAqLwp9CgogLmp1cHl0ZXItd2lkZ2V0cy53aWRnZXQtdGFiID4gLnAtVGFiQmFyIC5wLVRhYkJhci10YWJJY29uLAouanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIgLnAtVGFiQmFyLXRhYkxhYmVsLAouanVweXRlci13aWRnZXRzLndpZGdldC10YWIgPiAucC1UYWJCYXIgLnAtVGFiQmFyLXRhYkNsb3NlSWNvbiB7CiAgICBsaW5lLWhlaWdodDogMjRweDsKfQoKIC8qIEFjY29yZGlvbiBXaWRnZXQgKi8KCiAucC1Db2xsYXBzZSB7CiAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsKICAgIGRpc3BsYXk6IC1tcy1mbGV4Ym94OwogICAgZGlzcGxheTogZmxleDsKICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7CiAgICAtd2Via2l0LWJveC1kaXJlY3Rpb246IG5vcm1hbDsKICAgICAgICAtbXMtZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIC13ZWJraXQtYm94LWFsaWduOiBzdHJldGNoOwogICAgICAgIC1tcy1mbGV4LWFsaWduOiBzdHJldGNoOwogICAgICAgICAgICBhbGlnbi1pdGVtczogc3RyZXRjaDsKfQoKIC5wLUNvbGxhcHNlLWhlYWRlciB7CiAgICBwYWRkaW5nOiA0cHg7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICBjb2xvcjogcmdiYSgwLCAwLCAwLCAuNSk7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRUVFRUVFOwogICAgYm9yZGVyOiAxcHggc29saWQgIzlFOUU5RTsKICAgIHBhZGRpbmc6IDEwcHggMTVweDsKICAgIGZvbnQtd2VpZ2h0OiBib2xkOwp9CgogLnAtQ29sbGFwc2UtaGVhZGVyOmhvdmVyIHsKICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOwogICAgY29sb3I6IHJnYmEoMCwgMCwgMCwgLjgpOwp9CgogLnAtQ29sbGFwc2Utb3BlbiA+IC5wLUNvbGxhcHNlLWhlYWRlciB7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIDEuMCk7CiAgICBjdXJzb3I6IGRlZmF1bHQ7CiAgICBib3JkZXItYm90dG9tOiBub25lOwp9CgogLnAtQ29sbGFwc2UgLnAtQ29sbGFwc2UtaGVhZGVyOjpiZWZvcmUgewogICAgY29udGVudDogJ1xmMGRhXDAwQTAnOyAgLyogY2FyZXQtcmlnaHQsIG5vbi1icmVha2luZyBzcGFjZSAqLwogICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgZm9udDogbm9ybWFsIG5vcm1hbCBub3JtYWwgMTRweC8xIEZvbnRBd2Vzb21lOwogICAgZm9udC1zaXplOiBpbmhlcml0OwogICAgdGV4dC1yZW5kZXJpbmc6IGF1dG87CiAgICAtd2Via2l0LWZvbnQtc21vb3RoaW5nOiBhbnRpYWxpYXNlZDsKICAgIC1tb3otb3N4LWZvbnQtc21vb3RoaW5nOiBncmF5c2NhbGU7Cn0KCiAucC1Db2xsYXBzZS1vcGVuID4gLnAtQ29sbGFwc2UtaGVhZGVyOjpiZWZvcmUgewogICAgY29udGVudDogJ1xmMGQ3XDAwQTAnOyAvKiBjYXJldC1kb3duLCBub24tYnJlYWtpbmcgc3BhY2UgKi8KfQoKIC5wLUNvbGxhcHNlLWNvbnRlbnRzIHsKICAgIHBhZGRpbmc6IDE1cHg7CiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB3aGl0ZTsKICAgIGNvbG9yOiByZ2JhKDAsIDAsIDAsIC44KTsKICAgIGJvcmRlci1sZWZ0OiAxcHggc29saWQgIzlFOUU5RTsKICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkICM5RTlFOUU7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgIzlFOUU5RTsKICAgIG92ZXJmbG93OiBhdXRvOwp9CgogLnAtQWNjb3JkaW9uIHsKICAgIGRpc3BsYXk6IC13ZWJraXQtYm94OwogICAgZGlzcGxheTogLW1zLWZsZXhib3g7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsKICAgIC13ZWJraXQtYm94LWRpcmVjdGlvbjogbm9ybWFsOwogICAgICAgIC1tcy1mbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgLXdlYmtpdC1ib3gtYWxpZ246IHN0cmV0Y2g7CiAgICAgICAgLW1zLWZsZXgtYWxpZ246IHN0cmV0Y2g7CiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBzdHJldGNoOwp9CgogLnAtQWNjb3JkaW9uIC5wLUNvbGxhcHNlIHsKICAgIG1hcmdpbi1ib3R0b206IDA7Cn0KCiAucC1BY2NvcmRpb24gLnAtQ29sbGFwc2UgKyAucC1Db2xsYXBzZSB7CiAgICBtYXJnaW4tdG9wOiA0cHg7Cn0KCiAvKiBIVE1MIHdpZGdldCAqLwoKIC53aWRnZXQtaHRtbCwgLndpZGdldC1odG1sbWF0aCB7CiAgICBmb250LXNpemU6IDEzcHg7Cn0KCiAud2lkZ2V0LWh0bWwgPiAud2lkZ2V0LWh0bWwtY29udGVudCwgLndpZGdldC1odG1sbWF0aCA+IC53aWRnZXQtaHRtbC1jb250ZW50IHsKICAgIC8qIEZpbGwgb3V0IHRoZSBhcmVhIGluIHRoZSBIVE1MIHdpZGdldCAqLwogICAgLW1zLWZsZXgtaXRlbS1hbGlnbjogc3RyZXRjaDsKICAgICAgICBhbGlnbi1zZWxmOiBzdHJldGNoOwogICAgLXdlYmtpdC1ib3gtZmxleDogMTsKICAgICAgICAtbXMtZmxleC1wb3NpdGl2ZTogMTsKICAgICAgICAgICAgZmxleC1ncm93OiAxOwogICAgLW1zLWZsZXgtbmVnYXRpdmU6IDE7CiAgICAgICAgZmxleC1zaHJpbms6IDE7CiAgICAvKiBNYWtlcyBzdXJlIHRoZSBiYXNlbGluZSBpcyBzdGlsbCBhbGlnbmVkIHdpdGggb3RoZXIgZWxlbWVudHMgKi8KICAgIGxpbmUtaGVpZ2h0OiAyOHB4OwogICAgLyogTWFrZSBpdCBwb3NzaWJsZSB0byBoYXZlIGFic29sdXRlbHktcG9zaXRpb25lZCBlbGVtZW50cyBpbiB0aGUgaHRtbCAqLwogICAgcG9zaXRpb246IHJlbGF0aXZlOwp9CgovKiMgc291cmNlTWFwcGluZ1VSTD1kYXRhOmFwcGxpY2F0aW9uL2pzb247YmFzZTY0LGV5SjJaWEp6YVc5dUlqb3pMQ0p6YjNWeVkyVnpJanBiSWk0dUwyNXZaR1ZmYlc5a2RXeGxjeTlBYW5Wd2VYUmxjaTEzYVdSblpYUnpMMk52Ym5SeWIyeHpMMk56Y3k5M2FXUm5aWFJ6TG1OemN5SXNJaTR1TDI1dlpHVmZiVzlrZFd4bGN5OUFhblZ3ZVhSbGNpMTNhV1JuWlhSekwyTnZiblJ5YjJ4ekwyTnpjeTlzWVdKMllYSnBZV0pzWlhNdVkzTnpJaXdpTGk0dmJtOWtaVjl0YjJSMWJHVnpMMEJxZFhCNWRHVnlMWGRwWkdkbGRITXZZMjl1ZEhKdmJITXZZM056TDIxaGRHVnlhV0ZzWTI5c2IzSnpMbU56Y3lJc0lpNHVMMjV2WkdWZmJXOWtkV3hsY3k5QWFuVndlWFJsY2kxM2FXUm5aWFJ6TDJOdmJuUnliMnh6TDJOemN5OTNhV1JuWlhSekxXSmhjMlV1WTNOeklpd2lMaTR2Ym05a1pWOXRiMlIxYkdWekwwQnFkWEI1ZEdWeUxYZHBaR2RsZEhNdlkyOXVkSEp2YkhNdlkzTnpMM0JvYjNOd2FHOXlMbU56Y3lKZExDSnVZVzFsY3lJNlcxMHNJbTFoY0hCcGJtZHpJam9pUVVGQlFUczdSMEZGUnpzN1EwRkZSanM3YTBOQlJXbERPenREUTA1c1F6czdPeXRGUVVjclJUczdRMEZGTDBVN096czdSVUZKUlRzN1EwTlVSanM3T3pzN096czdPenM3T3pzN096czdPenM3T3pzN096czdPenM3UjBFMlFrYzdPME5FYUVKSU96czdPenM3T3pzN096czdPenM3T3pzN08wVkJiVUpGT3p0RFFVZEdPenRIUVVWSE96dERRVU5HTEhsRVFVRjVSRHM3UTBGRE1VUXNlVVZCUVhsRk96dERRVVY2UlRzN1IwRkZSenM3UTBGUFNEczdSVUZGUlRzN08wdEJSMGM3TzBWQlVVZzdPenM3U1VGSlJTeERRVWwzUWl4dlFrRkJiMElzUTBGSGFFSXNNRU5CUVRCRE96dEZRVWQ0UlRzN1NVRkZSVHM3UlVGUFJqczdTMEZGUnpzN1JVRlBTRHM3TzBsQlIwVXNRMEZYZDBJc2IwSkJRVzlDT3pzN1JVRlZPVU03T3pzN1NVRkpSVHM3UlVGUFJpeHJRa0ZCYTBJN08wVkJXV3hDTEN0RFFVRXJRenM3UlVGelFpOURMREJDUVVFd1FqdEZRV0V4UWpzMFJVRkRNRVU3UlVGRk1VVTdkMFZCUTNORk96dEZRVWQwUlN3NFFrRkJPRUk3TzBWQlN6bENMRFpDUVVFMlFqczdSVUZKTjBJc05rSkJRVFpDTzBOQlVUbENPenREUlhwTlJEczdSMEZGUnpzN1EwRkZTRHM3T3p0SFFVbEhPenREUTFKSU96czdPenM3T3pzN096czdPenM3T3pzN096czdPenM3T3pzN096czdSVUU0UWtVN08wTkJSVVk3T3p0SFFVZEhPenREUVVWSU8wVkJRMFVzY1VKQlFXTTdSVUZCWkN4eFFrRkJZenRGUVVGa0xHTkJRV003UlVGRFpDd3dRa0ZCTUVJN1JVRkRNVUlzZFVKQlFYVkNPMFZCUTNaQ0xITkNRVUZ6UWp0RlFVTjBRaXhyUWtGQmEwSTdRMEZEYmtJN08wTkJSMFE3UlVGRFJTd3JRa0ZCYjBJN1JVRkJjRUlzT0VKQlFXOUNPMDFCUVhCQ0xIZENRVUZ2UWp0VlFVRndRaXh2UWtGQmIwSTdRMEZEY2tJN08wTkJSMFE3UlVGRFJTdzJRa0ZCZFVJN1JVRkJka0lzT0VKQlFYVkNPMDFCUVhaQ0xESkNRVUYxUWp0VlFVRjJRaXgxUWtGQmRVSTdRMEZEZUVJN08wTkJSMFE3UlVGRFJTeFZRVUZWTzBWQlExWXNWMEZCVnp0RlFVTllMSEZDUVVGak8wVkJRV1FzY1VKQlFXTTdSVUZCWkN4alFVRmpPMFZCUTJRc2IwSkJRV1U3VFVGQlppeHRRa0ZCWlR0VlFVRm1MR1ZCUVdVN1JVRkRaaXh6UWtGQmMwSTdRMEZEZGtJN08wTkJSMFE3UlVGRFJTd3JRa0ZCYjBJN1JVRkJjRUlzT0VKQlFXOUNPMDFCUVhCQ0xIZENRVUZ2UWp0VlFVRndRaXh2UWtGQmIwSTdRMEZEY2tJN08wTkJSMFE3UlVGRFJTdzJRa0ZCZFVJN1JVRkJka0lzT0VKQlFYVkNPMDFCUVhaQ0xESkNRVUYxUWp0VlFVRjJRaXgxUWtGQmRVSTdRMEZEZUVJN08wTkJSMFE3UlVGRFJTeHhRa0ZCWXp0RlFVRmtMSEZDUVVGak8wVkJRV1FzWTBGQll6dEZRVU5rTEN0Q1FVRnZRanRGUVVGd1FpdzRRa0ZCYjBJN1RVRkJjRUlzZDBKQlFXOUNPMVZCUVhCQ0xHOUNRVUZ2UWp0RlFVTndRaXdyUWtGQmRVSTdWVUZCZGtJc2RVSkJRWFZDTzBWQlEzWkNMR2xDUVVGcFFqdERRVU5zUWpzN1EwRkhSRHM3UlVGRlJTeHZRa0ZCWlR0TlFVRm1MRzFDUVVGbE8xVkJRV1lzWlVGQlpUdERRVU5vUWpzN1EwRkhSRHRGUVVORkxHOUNRVUZsTzAxQlFXWXNiVUpCUVdVN1ZVRkJaaXhsUVVGbE8wVkJRMllzYVVKQlFXbENPMFZCUTJwQ0xHOUNRVUZ2UWp0RFFVTnlRanM3UTBGSFJEdEZRVU5GTEhsQ1FVRjVRanREUVVNeFFqczdRMEZIUkR0RlFVTkZMRzFDUVVGdFFqdERRVU53UWpzN1EwRkhSRHRGUVVORkxGRkJRVkU3UlVGRFVpeHZRMEZCTkVJN1JVRkJOVUlzTkVKQlFUUkNPME5CUXpkQ096dERRVWRFTzBWQlEwVXNUMEZCVHp0RlFVTlFMRzFEUVVFeVFqdEZRVUV6UWl3eVFrRkJNa0k3UTBGRE5VSTdPME5CUjBRN1JVRkRSU3g1UWtGQmFVSTdSVUZCYWtJc2FVSkJRV2xDTzBOQlEyeENPenREUVVWRUxHOUNRVUZ2UWpzN1EwUTVSM0JDTEZGQlZYRkRMRzlEUVVGdlF6czdTVUV5UW5KRkxDdENRVUVyUWp0RFFVbHNRenM3UTBGRlJEdEpRVU5KTEZsQlFXbERPMGxCUTJwRExDdENRVUYxUWp0WlFVRjJRaXgxUWtGQmRVSTdTVUZEZGtJc1lVRkJLMEk3U1VGREwwSXNhMEpCUVd0Q08wTkJRM0pDT3p0RFFVVkVPMGxCUTBrc2EwSkJRVFpETzBsQlF6ZERMR0ZCUVhkRE8wTkJRek5ET3p0RFFVVkVPMGxCUTBrc1pVRkJaVHRKUVVObUxHZENRVUZuUWp0RFFVTnVRanM3UTBGRlJDeHRRa0ZCYlVJN08wTkJSVzVDTzBsQlEwa3NkMEpCUVhkQ08wbEJRM2hDTEN0Q1FVRjFRanRaUVVGMlFpeDFRa0ZCZFVJN1NVRkRka0lzY1VKQlFXTTdTVUZCWkN4eFFrRkJZenRKUVVGa0xHTkJRV003U1VGRFpDd3JRa0ZCYjBJN1NVRkJjRUlzT0VKQlFXOUNPMUZCUVhCQ0xIZENRVUZ2UWp0WlFVRndRaXh2UWtGQmIwSTdTVUZEY0VJc05FSkJRWE5DTzFGQlFYUkNMSGxDUVVGelFqdFpRVUYwUWl4elFrRkJjMEk3UTBGRGVrSTdPME5CUlVRN1NVRkRTU3h6UWtGQmMwSTdTVUZEZEVJc0swSkJRWFZDTzFsQlFYWkNMSFZDUVVGMVFqdEpRVU4yUWl4eFFrRkJZenRKUVVGa0xIRkNRVUZqTzBsQlFXUXNZMEZCWXp0SlFVTmtMRFpDUVVGMVFqdEpRVUYyUWl3NFFrRkJkVUk3VVVGQmRrSXNNa0pCUVhWQ08xbEJRWFpDTEhWQ1FVRjFRanRKUVVOMlFpd3dRa0ZCYjBJN1VVRkJjRUlzZFVKQlFXOUNPMWxCUVhCQ0xHOUNRVUZ2UWp0RFFVTjJRanM3UTBGRlJEdEpRVU5KTEN0Q1FVRjFRanRaUVVGMlFpeDFRa0ZCZFVJN1NVRkRka0lzY1VKQlFXTTdTVUZCWkN4eFFrRkJZenRKUVVGa0xHTkJRV003U1VGRFpDeFZRVUZWTzBsQlExWXNaVUZCWlR0RFFVTnNRanM3UTBGRlJEdEpRVU5KTEN0Q1FVRjFRanRaUVVGMlFpeDFRa0ZCZFVJN1NVRkRka0lzWTBGQll6dEpRVU5rTEZWQlFWVTdTVUZEVml4bFFVRmxPME5CUTJ4Q096dERRVVZFTzBsQlEwa3NLMEpCUVc5Q08wbEJRWEJDTERoQ1FVRnZRanRSUVVGd1FpeDNRa0ZCYjBJN1dVRkJjRUlzYjBKQlFXOUNPME5CUTNaQ096dERRVVZFTzBsQlEwa3NOa0pCUVhWQ08wbEJRWFpDTERoQ1FVRjFRanRSUVVGMlFpd3lRa0ZCZFVJN1dVRkJka0lzZFVKQlFYVkNPME5CUXpGQ096dERRVVZFTERSQ1FVRTBRanM3UTBGRk5VSTdTVUZEU1N4dFFrRkJiVUk3U1VGRGJrSXNiMEpCUVc5Q08wbEJRM0JDTEdsQ1FVRnBRanRKUVVOcVFpeHZRa0ZCYjBJN1NVRkRjRUlzYzBKQlFYTkNPMGxCUTNSQ0xHOUNRVUZ2UWp0SlFVTndRaXhwUWtGQmFVSTdTVUZEYWtJc2QwSkJRWGRDTzBsQlEzaENMRzFDUVVGdFFqdEpRVU51UWl4blFrRkJkVU03U1VGRGRrTXNaMEpCUVdkQ096dEpRVVZvUWl4aFFVRjNRenRKUVVONFF5eHJRa0ZCYTBJN1NVRkRiRUlzYTBKQlFUWkRPMGxCUXpkRExIbENRVUZwUWp0WlFVRnFRaXhwUWtGQmFVSTdPMGxCUldwQ0xIbENRVUZuUXp0SlFVTm9ReXd3UWtGQk1FTTdTVUZETVVNc2MwSkJRWE5ETzBsQlEzUkRMR0ZCUVdFN1EwRkRhRUk3TzBOQlJVUTdTVUZEU1N4clFrRkJPRU03U1VGRE9VTXNjVUpCUVhGQ08wTkJRM2hDT3p0RFFVVkVPMGxCUTBrc2FVSkJRV2xDTEVOQlFVTXNjMEpCUVhOQ08wTkJRek5ET3p0RFFVVkVPMGxCUTBrc1lVRkJORU03UTBGREwwTTdPME5CUlVRN1NVRkRTU3huUWtGQlowSTdRMEZEYmtJN08wTkJSVVE3U1VGRFNTeDNRa0ZCZDBJN1NVRkRlRUk3T3l0RFFVVXJSVHRaUVVZdlJUczdLME5CUlN0Rk8wTkJRMnhHT3p0RFFVVkVPMGxCUTBrc2QwSkJRWGRDTzBsQlEzaENPenRwUkVGRk5rVTdXVUZHTjBVN08ybEVRVVUyUlR0SlFVTTNSU3g1UWtGQlowTTdTVUZEYUVNc01FSkJRVEJETzBOQlF6ZERPenREUVVWRU8wbEJRMGtzTWtKQlFUaEVPME5CUTJwRk96dERRVVZFTERoQ1FVRTRRanM3UTBGRk9VSTdTVUZEU1N4blEwRkJkME03U1VGRGVFTXNNRUpCUVhsRE8wTkJRelZET3p0RFFVVkVPMGxCUTBrc09FSkJRWGRETzBsQlEzaERMREJDUVVGNVF6dERRVU0xUXpzN1EwRkZSRHRKUVVOSkxEaENRVUYzUXp0SlFVTjRReXd3UWtGQmVVTTdRMEZETlVNN08wTkJSVVFzT0VKQlFUaENPenREUVVVNVFqdEpRVU5KTEdkRFFVRjNRenRKUVVONFF5d3dRa0ZCTWtNN1EwRkRPVU03TzBOQlJVUTdTVUZEU1N3NFFrRkJkME03U1VGRGVFTXNNRUpCUVRKRE8wVkJRemRET3p0RFFVVkdPMGxCUTBrc09FSkJRWGRETzBsQlEzaERMREJDUVVFeVF6dEZRVU0zUXpzN1EwRkZSQ3d5UWtGQk1rSTdPME5CUlRWQ08wbEJRMGtzWjBOQlFYZERPMGxCUTNoRExEQkNRVUYzUXp0RFFVTXpRenM3UTBGRlJEdEpRVU5KTERoQ1FVRjNRenRKUVVONFF5d3dRa0ZCZDBNN1EwRkRNME03TzBOQlJVUTdTVUZEU1N3NFFrRkJkME03U1VGRGVFTXNNRUpCUVhkRE8wTkJRek5ET3p0RFFVVkVMRGhDUVVFNFFqczdRMEZGT1VJN1NVRkRTU3huUTBGQmQwTTdTVUZEZUVNc01FSkJRWGRETzBOQlF6TkRPenREUVVWRU8wbEJRMGtzT0VKQlFYZERPMGxCUTNoRExEQkNRVUYzUXp0RFFVTXpRenM3UTBGRlJEdEpRVU5KTERoQ1FVRjNRenRKUVVONFF5d3dRa0ZCZDBNN1EwRkRNME03TzBOQlJVUXNOa0pCUVRaQ096dERRVVUzUWp0SlFVTkpMR2REUVVGM1F6dEpRVU40UXl3d1FrRkJlVU03UTBGRE5VTTdPME5CUlVRN1NVRkRTU3c0UWtGQmQwTTdTVUZEZUVNc01FSkJRWGxETzBOQlF6VkRPenREUVVWRU8wbEJRMGtzT0VKQlFYZERPMGxCUTNoRExEQkNRVUY1UXp0RFFVTTFRenM3UTBGRlJDeHJRa0ZCYTBJN08wTkJSV3hDTzBsQlEwa3NZVUZCTkVNN1EwRkRMME03TzBOQlJVUXNNRUpCUVRCQ096dERRVVV4UWl4clEwRkJhME03TzBOQlEyeERPMGxCUTBrc2FVSkJRWFZDTzBsQlFYWkNMSFZDUVVGMVFqdERRVU14UWpzN1EwRkZSRHRKUVVOSkxHbENRVUZwUWp0SlFVTnFRaXhoUVVGeFF6dEpRVU55UXl4blFrRkJkVU03U1VGRGRrTXNhVUpCUVdsQ08wbEJRMnBDTEhkQ1FVRjNRanRKUVVONFFpeHZRa0ZCYjBJN1NVRkRjRUlzYTBKQlFUWkRPME5CUTJoRU96dERRVVZFTzBsQlEwa3NWMEZCVnp0SlFVTllMR0ZCUVhGRE8wbEJRM0pETEdkQ1FVRjFRenRKUVVOMlF5eHBRa0ZCYVVJN1NVRkRha0lzZDBKQlFYZENPMGxCUTNoQ0xHOUNRVUZ2UWp0SlFVTndRaXhyUWtGQk5rTTdRMEZEYUVRN08wTkJSVVE3U1VGRFNTdzJRa0ZCTmtJN1NVRkROMElzWVVGQmNVTTdTVUZEY2tNc2EwSkJRV3RDTzBsQlEyeENMR3RDUVVFd1JEdEpRVU14UkN4WlFVRTBRenRKUVVNMVF5eHhRa0ZCWlR0UlFVRm1MR1ZCUVdVN1EwRkRiRUk3TzBOQlJVUTdTVUZEU1N3eVFrRkJNa0k3U1VGRE0wSXNZVUZCY1VNN1NVRkRja01zYlVKQlFXMUNPMGxCUTI1Q0xHdENRVUUyUXp0RFFVTm9SRHM3UTBGRlJDdzBRa0ZCTkVJN08wTkJSVFZDTzBsQlEwa3NZVUZCZFVNN1NVRkRka01zWjBKQlFYVkRPMGxCUTNaRExHRkJRWGRETzBsQlEzaERMR3RDUVVFMlF6dEpRVU0zUXl4cFFrRkJhVUk3U1VGRGFrSXNiMEpCUVc5Q08wbEJRM0JDTEcxQ1FVRnRRanREUVVOMFFqczdRMEZGUkR0SlFVTkpMSGxDUVVGNVFqczdTVUZGZWtJN096czdUMEZKUnp0SlFVTklPenQxUkVGRmIwUTdPMGxCVFhCRU96c3JRMEZGTkVNN1EwRkRMME03TzBOQlJVUTdTVUZEU1N4M1FrRkJkMEk3U1VGRGVFSXNiVUpCUVcxQ08wbEJRMjVDTEdsQ1FVRm5SRHRKUVVOb1JDeG5Ra0ZCSzBNN1NVRkRMME1zYVVKQlFUWkRPME5CUTJoRU96dERRVVZFTzBsQlEwa3NjMEpCUVhOQ08wbEJRM1JDTEdkQ1FVRTBRenRKUVVNMVF5d3lRa0ZCTWtJN1NVRkRNMElzWlVGQlpUdERRVU5zUWpzN1EwRkZSQ3cyUWtGQk5rSTdPME5CUlRkQ08wbEJRMGtzWVVGQmMwTTdTVUZEZEVNc1lVRkJkME03U1VGRGVFTXNhMEpCUVRaRE8wTkJRMmhFT3p0RFFVVkVPMGxCUTBrc2QwSkJRV2RGTzBsQlEyaEZMR3RDUVVFMlF6dEpRVU0zUXl4cFFrRkJhVUk3U1VGRGFrSXNiMEpCUVdFN1VVRkJZaXh4UWtGQllUdFpRVUZpTEdGQlFXRTdTVUZEWWl4eFFrRkJaVHRSUVVGbUxHVkJRV1U3U1VGRFppdzBRa0ZCYlVJN1VVRkJia0lzYlVKQlFXMUNPME5CUTNSQ096dERRVVZFTERCQ1FVRXdRanM3UTBGRk1VSTdTVUZEU1N4aFFVRjNRenRKUVVONFF5eHJRa0ZCTmtNN1NVRkROME1zWVVGQk5FTTdTVUZETlVNc1owSkJRWFZETzBOQlF6RkRPenREUVVWRU8wbEJRMGtzYTBKQlFUWkRPMGxCUXpkRExHdENRVUU0UXp0SlFVTTVReXhwUWtGQk5rTTdPMGxCUlRkRExEQktRVUV3U2p0SlFVTXhTaXh6UWtGQmMwSTdTVUZEZEVJc09FTkJRVGhETzBsQlF6bERMRzFDUVVGdFFqdEpRVU51UWl4eFFrRkJjVUk3U1VGRGNrSXNiME5CUVc5RE8wbEJRM0JETEcxRFFVRnRRenREUVVOMFF6czdRMEZGUkR0SlFVTkpMR2xDUVVGcFFqdEpRVU5xUWl4aFFVRmhPME5CUTJoQ096dERRVVZFTzBsQlEwa3NhVUpCUVdsQ08wbEJRMnBDTEZkQlFWYzdRMEZEWkRzN1EwRkZSRHRKUVVOSkxHTkJRV003UTBGRGFrSTdPME5CUlVRc2NVTkJRWEZET3p0RFFVVnlRenRKUVVOSkxHRkJRWE5ETzBOQlEzcERPenREUVVWRU8wbEJRMGtzWVVGQmQwTTdTVUZEZUVNc2EwSkJRVFpETzBOQlEyaEVPenREUVVWRU8wbEJRMGtzWVVGQk5FTTdRMEZETDBNN08wTkJSVVE3U1VGRFNTd3JRa0ZCZFVJN1dVRkJka0lzZFVKQlFYVkNPMGxCUTNaQ0xEQkNRVUYzUmp0SlFVTjRSaXgzUWtGQk1rUTdTVUZETTBRc2VVSkJRWEZETzBsQlEzSkRMR2RDUVVGMVF6dEpRVU4yUXl4cFFrRkJjMFk3U1VGRGRFWXNiMEpCUVdFN1VVRkJZaXh4UWtGQllUdFpRVUZpTEdGQlFXRTdTVUZEWWl4aFFVRmhMRU5CUVVNc2FVVkJRV2xGTzBsQlF5OUZMSEZDUVVGbE8xRkJRV1lzWlVGQlpUdEpRVU5tTEhsQ1FVRjVRanREUVVNMVFqczdRMEZGUkR0SlFVTkpMR2RDUVVGblFqdEpRVU5vUWl4bFFVRmxPME5CUTJ4Q096dERRVVZFTzBsQlEwa3NjMEpCUVhsRU8wTkJRelZFT3p0RFFVVkVMRzFDUVVGdFFqczdRMEZGYmtJN1NVRkRTU3hyUWtGQmEwSTdTVUZEYkVJc01FSkJRVFJGTzBsQlF6VkZMRzlDUVVGdlF6dEpRVU53UXl3clFrRkJkVUk3V1VGQmRrSXNkVUpCUVhWQ08wbEJRM1pDTEcxQ1FVRnRRanRKUVVOdVFpeHRRa0ZCYlVJN1EwRkRkRUk3TzBOQlJVUTdTVUZEU1N4dFFrRkJiVUk3U1VGRGJrSXNlVUpCUVhsQ0xFTkJRVU1zYjBSQlFXOUVPMGxCUXpsRkxHMUNRVUZ0UWp0SlFVTnVRaXgzUWtGQmJVVTdTVUZEYmtVc01FSkJRV2xITzBsQlEycEhMQ3RDUVVGMVFqdFpRVUYyUWl4MVFrRkJkVUk3U1VGRGRrSXNWMEZCVnp0SlFVTllMSFZDUVVGMVFpeERRVUZETEhkQ1FVRjNRanREUVVOdVJEczdRMEZGUkN4M1FrRkJkMEk3TzBOQlEzaENPMGxCUTBrc01FSkJRU3RFTzBsQlF5OUVMREJDUVVGcFJ6dERRVU53UnpzN1EwRkZSRHRKUVVOSkxEQkNRVUVyUkR0SlFVTXZSQ3h6UWtGQk1rUTdTVUZETTBRc1YwRkJWenRKUVVOWUxEaENRVUZ6UWp0WlFVRjBRaXh6UWtGQmMwSTdRMEZEZWtJN08wTkJSVVE3U1VGRFNTeHBSVUZCYVVVN1NVRkRha1VzYlVKQlFXMUNPMGxCUTI1Q0xHOUNRVUY1UkR0SlFVTjZSQ3hYUVVGWE8wTkJRMlE3TzBOQlJVUXNPRUpCUVRoQ096dERRVVU1UWp0SlFVTkpMRmxCUVRSRE8wbEJRelZETEdGQlFUWkRPMGxCUXpkRExHbENRVUZuU2p0SlFVTm9TaXhyUWtGQmNVYzdTVUZEY2tjc2JVSkJRVzFDTzBsQlEyNUNMRTlCUVU4N1EwRkRWanM3UTBGRlJEdEpRVU5KTEZsQlFUUkRPMGxCUXpWRExHRkJRVFpETzBsQlF6ZERMRzlDUVVGMVJ6dEpRVU4yUnl4clFrRkJhVW83U1VGRGFrb3NiVUpCUVcxQ08wbEJRMjVDTEZGQlFWRTdRMEZEV0RzN1EwRkZSRHRKUVVOSkxGbEJRVFpFTzBsQlF6ZEVMR2xDUVVGNVNqdERRVU0xU2pzN1EwRkZSRHRKUVVOSkxGZEJRVFJFTzBsQlF6VkVMR3RDUVVFd1NqdERRVU0zU2pzN1EwRkZSQ3gxUWtGQmRVSTdPME5CUlhaQ08wbEJRMGtzWVVGQmMwTTdTVUZEZEVNc1lVRkJkME03U1VGRGVFTXNhMEpCUVRaRE96dEpRVVUzUXpzN2IwUkJSV2RFTzBsQlEyaEVMREJDUVVGdlFqdFJRVUZ3UWl4MVFrRkJiMEk3V1VGQmNFSXNiMEpCUVc5Q08wTkJRM1pDT3p0RFFVVkVPMGxCUTBrc2EwSkJRV3RDTzBOQlEzSkNPenREUVVWRU8wbEJRMGtzWVVGQmQwTTdTVUZEZUVNc2FVSkJRWGRITzBsQlEzaEhMR3RDUVVGNVJ6dEpRVU42Unl4dlFrRkJLME03VVVGQkwwTXNiMEpCUVN0RE8xbEJRUzlETEdkQ1FVRXJRenREUVVOc1JEczdRMEZGUkR0SlFVTkpMR2REUVVGblF6dEpRVU5vUXl4WlFVRnBSRHRKUVVOcVJDeHBRa0ZCYlVjN1NVRkRia2NzV1VGQldUdERRVU5tT3p0RFFVVkVMSEZDUVVGeFFqczdRMEZGY2tJN1NVRkRTU3hoUVVGM1F6dEpRVU40UXl4clFrRkJOa003UTBGRGFFUTdPME5CUlVRN1NVRkRTU3h4UWtGQmNVSTdTVUZEY2tJc1kwRkJNRU03U1VGRE1VTXNXVUZCTWtNN1EwRkRPVU03TzBOQlJVUTdTVUZEU1N4dlFrRkJLME03VVVGQkwwTXNiMEpCUVN0RE8xbEJRUzlETEdkQ1FVRXJRenRKUVVNdlF5eHJRa0ZCYTBJN1NVRkRiRUlzYlVKQlFXMUNPMGxCUTI1Q0xHMUNRVUV3Unp0SlFVTXhSeXhuUWtGQmRVYzdTVUZEZGtjc2NVSkJRV003U1VGQlpDeHhRa0ZCWXp0SlFVRmtMR05CUVdNN1NVRkRaQ3cyUWtGQmRVSTdTVUZCZGtJc09FSkJRWFZDTzFGQlFYWkNMREpDUVVGMVFqdFpRVUYyUWl4MVFrRkJkVUk3UTBGRE1VSTdPME5CUlVRN1NVRkRTU3huUTBGQlowTTdTVUZEYUVNc1YwRkJaMFE3U1VGRGFFUXNiMEpCUVdFN1VVRkJZaXh4UWtGQllUdFpRVUZpTEdGQlFXRTdTVUZEWWl4clFrRkJhMEk3U1VGRGJFSXNiVUpCUVcxQ08wTkJRM1JDT3p0RFFVVkVMRFpDUVVFMlFqczdRMEZGTjBJN1NVRkRTU3g1UWtGQmVVSTdTVUZKZWtJc2FVSkJRV2xDTzBOQlEzQkNPenREUVVWRU8wbEJRMGtzWVVGQmQwTTdRMEZETTBNN08wTkJSVVE3U1VGRFNTd3dRa0ZCZVVNN1EwRkROVU03TzBOQlJVUTdTVUZEU1N3d1FrRkJNa003UTBGRE9VTTdPME5CUlVRN1NVRkRTU3d3UWtGQmQwTTdRMEZETTBNN08wTkJSVVE3U1VGRFNTd3dRa0ZCZDBNN1EwRkRNME03TzBOQlJVUTdTVUZEU1N3d1FrRkJlVU03UTBGRE5VTTdPME5CUlVRN1NVRkRTU3d3UWtGQk1FTTdTVUZETVVNc1lVRkJZVHRKUVVOaUxIbENRVUZwUWp0WlFVRnFRaXhwUWtGQmFVSTdRMEZEY0VJN08wTkJSVVFzZVVKQlFYbENPenREUVVWNlFqdEpRVU5KTEd0Q1FVRnJRanRKUVVOc1FpeGhRVUYzUXp0SlFVTjRReXhyUWtGQk5rTTdTVUZETjBNc1lVRkJjME03U1VGRGRFTXNNRUpCUVc5Q08xRkJRWEJDTEhWQ1FVRnZRanRaUVVGd1FpeHZRa0ZCYjBJN08wTkJSWFpDT3p0RFFVVkVPMGxCUTBrc2IwSkJRV0U3VVVGQllpeHhRa0ZCWVR0WlFVRmlMR0ZCUVdFN1NVRkRZaXhuUWtGQk5FTTdTVUZETlVNc2JVSkJRU3RETzBsQlF5OURMRFpDUVVGdlFqdFJRVUZ3UWl4dlFrRkJiMEk3U1VGRGNFSXNPRUpCUVRoQ08wbEJRemxDTEdGQlFXZENPMGxCUVdoQ0xHZENRVUZuUWp0RFFVTnVRanM3UTBGRlJDeDFRa0ZCZFVJN08wTkJSWFpDTzBsQlEwa3NZMEZCTUVNN1NVRkRNVU1zV1VGQk1rTTdRMEZET1VNN08wTkJSVVE3U1VGRFNTeHZRa0ZCWVR0UlFVRmlMSEZDUVVGaE8xbEJRV0lzWVVGQllUdEpRVU5pTEZsQlFUUkRPMGxCUXpWRExHdENRVUZyUWp0SlFVTnNRaXh0UWtGQmJVSTdTVUZEYmtJc2FVSkJRV2xDTzBOQlEzQkNPenREUVVWRUxESkNRVUV5UWpzN1EwRkZNMEk3U1VGRFNTeGhRVUYzUXp0SlFVTjRReXhoUVVGelF6dEpRVU4wUXl4clFrRkJOa003UTBGRGFFUTdPME5CUlVRN1NVRkRTU3h2UWtGQmIwSTdTVUZEY0VJc01FSkJRWGRHTzBsQlEzaEdMR2xDUVVGcFFqdEpRVU5xUWl4blFrRkJaMEk3U1VGRGFFSXNiMEpCUVN0RE8xRkJRUzlETEc5Q1FVRXJRenRaUVVFdlF5eG5Ra0ZCSzBNN1NVRkRMME1zWVVGQllTeERRVUZETEdsRlFVRnBSVHRKUVVNdlJTd3JRa0ZCZFVJN1dVRkJka0lzZFVKQlFYVkNPMGxCUTNaQ0xIbENRVUY1UWp0SlFVTjZRaXg1UWtGQmFVSTdXVUZCYWtJc2FVSkJRV2xDTzBsQlEycENMSGRDUVVFeVJEdEpRVU16UkN4NVFrRkJjVU03U1VGRGNrTXNaMEpCUVhWRE8wbEJRM1pETEc5Q1FVRnZRanRKUVVOd1FpeHJRa0ZCZVVRN1EwRkROVVFzYVVKQlFXbENPME5CUTJwQ0xIbENRVUY1UWp0RFFVTjZRaXh6UWtGQmMwSTdTVUZEYmtJc05rSkJRVFpDTzBOQlEyaERMSE5DUVVGelFqdERRVU4wUWl4clEwRkJhME03U1VGREwwSXNhM1ZDUVVGdFJEdERRVU4wUkRzN1EwRkRSRHRKUVVOSkxITkNRVUY1UkR0RFFVTTFSRHM3UTBGRlJEdEpRVU5KTEdGQlFUUkRPME5CUXk5RE96dERRVVZFT3paRFFVTTJRenM3UTBGRE4wTTdTVUZEU1N4dFFrRkJiVUk3U1VGRGJrSXNkMEpCUVhkQ08wTkJRek5DT3p0RFFVVkVMQ3RDUVVFclFqczdRMEZGTDBJN1NVRkRTU3hoUVVGelF6dEpRVU4wUXl4clFrRkJOa003TzBsQlJUZERPenRyUlVGRk9FUTdTVUZET1VRc2VVSkJRWGRDTzFGQlFYaENMSE5DUVVGM1FqdFpRVUY0UWl4M1FrRkJkMEk3UTBGRE0wSTdPME5CUlVRN1NVRkRTU3d3UWtGQmQwWTdTVUZEZUVZc2QwSkJRVEpFTzBsQlF6TkVMSGxDUVVGeFF6dEpRVU55UXl4blFrRkJkVU03U1VGRGRrTXNiMEpCUVN0RE8xRkJRUzlETEc5Q1FVRXJRenRaUVVFdlF5eG5Ra0ZCSzBNN1NVRkRMME1zZVVKQlFYbENPMGxCUTNwQ0xHVkJRV1U3U1VGRFppeG5Ra0ZCWjBJN08wbEJSV2hDT3p0clJVRkZPRVE3U1VGRE9VUXNhVUpCUVdsQ08wTkJRM0JDT3p0RFFVVkVPMGxCUTBrc2MwSkJRWGxFTzBOQlF6VkVPenREUVVWRU8wbEJRMGtzYTBKQlFUaERPMGxCUXpsRExHdENRVUUyUXp0SlFVTTNReXhyUlVGQmEwVTdTVUZEYkVVc01FUkJRV2xHTzBsQlEycEdMRFpFUVVGdlJqdERRVU4yUmpzN1EwRkpSQ3cwUWtGQk5FSTdPME5CUlRWQ08wbEJRMGtzYTBKQlFUWkRPME5CUTJoRU96dERRVVZFTzBsQlEwa3NhVUpCUVhORE8wbEJRM1JETEd0Q1FVRjFRenREUVVNeFF6czdRMEZGUkR0SlFVTkpMR0ZCUVRSRE8wTkJReTlET3p0RFFVVkVMREpDUVVFeVFqczdRMEZGTTBJN1NVRkRTU3hoUVVGelF6dEpRVU4wUXl4clFrRkJOa003UTBGRGFFUTdPME5CUlVRN1NVRkRTU3h4UWtGQll6dEpRVUZrTEhGQ1FVRmpPMGxCUVdRc1kwRkJZenRKUVVOa0xEWkNRVUYxUWp0SlFVRjJRaXc0UWtGQmRVSTdVVUZCZGtJc01rSkJRWFZDTzFsQlFYWkNMSFZDUVVGMVFqdEpRVU4yUWl3eVFrRkJjVUk3VVVGQmNrSXNkMEpCUVhGQ08xbEJRWEpDTEhGQ1FVRnhRanRKUVVOeVFpd3JRa0ZCZFVJN1dVRkJka0lzZFVKQlFYVkNPMGxCUTNaQ0xHOUNRVUZoTzFGQlFXSXNjVUpCUVdFN1dVRkJZaXhoUVVGaE8wbEJRMklzYlVKQlFUaEVPME5CUTJwRk96dERRVVZFTzBsQlEwa3NZVUZCTkVNN1NVRkROVU1zYTBKQlFXbEVPMGxCUTJwRUxHZENRVUYxUXp0RFFVTXhRenM3UTBGRlJEdEpRVU5KTEdGQlFUUkRPMGxCUXpWRExHdENRVUZwUkR0SlFVTnFSQ3h2UWtGQk5FUTdTVUZETlVRc1dVRkJXVHREUVVObU96dERRVVZFTERCQ1FVRXdRanM3UTBGRk1VSTdTVUZEU1N4aFFVRnpRenRKUVVOMFF5eGhRVUYzUXp0SlFVTjRReXhyUWtGQk5rTTdRMEZEYUVRN08wTkJSVVE3U1VGRFNTeHZRa0ZCWVR0UlFVRmlMSEZDUVVGaE8xbEJRV0lzWVVGQllUdEpRVU5pTEhGQ1FVRmxPMUZCUVdZc1pVRkJaVHRKUVVObUxHZENRVUVyUXp0RFFVTnNSRHM3UTBGRlJEdEpRVU5KTEZsQlFYVkRPMGxCUTNaRExHRkJRWGRETzBsQlEzaERMR1ZCUVdVc1EwRkJReXcyUkVGQk5rUTdTVUZETjBVc2EwSkJRWEZFTzBsQlEzSkVMSGxDUVVGeFF6dEpRVU55UXl3d1FrRkJkMFk3U1VGRGVFWXNhMEpCUVd0Q08wbEJRMnhDTEc5Q1FVRmhPMUZCUVdJc2NVSkJRV0U3V1VGQllpeGhRVUZoTzBsQlEySXNjVUpCUVdVN1VVRkJaaXhsUVVGbE8wbEJRMllzSzBKQlFYVkNPMWxCUVhaQ0xIVkNRVUYxUWp0SlFVTjJRaXcyUWtGQmIwSTdVVUZCY0VJc2IwSkJRVzlDTzBsQlEzQkNMSGxDUVVGNVFqdERRVU0xUWpzN1EwRkZSRHRKUVVOSkxDdENRVUUyUmp0RFFVTm9SenM3UTBGRlJEdEpRVU5KTEhOQ1FVRjVSRHREUVVNMVJEczdRMEZGUkR0SlFVTkpMRzlDUVVGaE8xRkJRV0lzY1VKQlFXRTdXVUZCWWl4aFFVRmhPMGxCUTJJc2VVSkJRWGxDTzBsQlEzcENMR0ZCUVhkRE8wbEJRM2hETEd0Q1FVRTJRenRKUVVNM1F5eHJRa0ZCY1VRN1NVRkRja1FzZVVKQlFYRkRPMGxCUTNKRExEQkNRVUYzUmp0SlFVTjRSaXhuUWtGQmRVTTdTVUZEZGtNc2FVSkJRWE5HTzBsQlEzUkdMR0ZCUVdFc1EwRkJReXhwUlVGQmFVVTdTVUZETDBVc2NVSkJRV1U3VVVGQlppeGxRVUZsTzBsQlEyWXNLMEpCUVhWQ08xbEJRWFpDTEhWQ1FVRjFRanREUVVNeFFqczdRMEZGUkR0SlFVTkpMR0ZCUVRSRE8wTkJReTlET3p0RFFVVkVMSGxDUVVGNVFqczdRMEZGZWtJN1NVRkRTU3hoUVVGelF6dEpRVU4wUXl4aFFVRjNRenRKUVVONFF5eHJRa0ZCTmtNN1EwRkRhRVE3TzBOQlJVUTdTVUZEU1N4dlFrRkJZVHRSUVVGaUxIRkNRVUZoTzFsQlFXSXNZVUZCWVR0SlFVTmlMSEZDUVVGbE8xRkJRV1lzWlVGQlpUdEpRVU5tTEdGQlFXRXNRMEZCUXl4cFJVRkJhVVU3U1VGREwwVXNlVUpCUVhsQ08wbEJRM3BDTEdGQlFYZERPMGxCUTNoRExEQkNRVUYzUmp0SlFVTjRSaXgzUWtGQk1rUTdTVUZETTBRc2VVSkJRWEZETzBsQlEzSkRMR2RDUVVGMVF6dEpRVU4yUXl4cFFrRkJjMFk3U1VGRGRFWXNLMEpCUVhWQ08xbEJRWFpDTEhWQ1FVRjFRanREUVVNeFFqczdRMEZGUkR0SlFVTkpMSE5DUVVGNVJEdERRVU0xUkRzN1EwRkZSRHRKUVVOSkxITkNRVUZ2UXp0RFFVTjJRenM3UTBGRlJEdEpRVU5KTEdGQlFUUkRPME5CUXk5RE96dERRVVZFTEdsQ1FVRnBRanM3UTBGRmFrSTdTVUZEU1N4aFFVRTBRenRKUVVNMVF5eHhRa0ZCWXp0SlFVRmtMSEZDUVVGak8wbEJRV1FzWTBGQll6dEpRVU5rTERKQ1FVRnhRanRSUVVGeVFpeDNRa0ZCY1VJN1dVRkJja0lzY1VKQlFYRkNPME5CUTNoQ096dERRVVZFTzBsQlEwa3NiMEpCUVdFN1VVRkJZaXh4UWtGQllUdFpRVUZpTEdGQlFXRTdTVUZEWWl4aFFVRmhPME5CUTJoQ096dERRVVZFTzBsQlEwa3NZVUZCTkVNN1EwRkRMME03TzBOQlJVUXNaMEpCUVdkQ096dERRVVZvUWp0SlFVTkpMSEZDUVVGak8wbEJRV1FzY1VKQlFXTTdTVUZCWkN4alFVRmpPMGxCUTJRc05rSkJRWFZDTzBsQlFYWkNMRGhDUVVGMVFqdFJRVUYyUWl3eVFrRkJkVUk3V1VGQmRrSXNkVUpCUVhWQ08wTkJRekZDT3p0RFFVVkVPMGxCUTBrc2VVWkJRWGxHTzBsQlEzcEdMRzlDUVVGdlFqdEpRVU53UWl4dlFrRkJiMEk3UTBGRGRrSTdPME5CUlVRN1NVRkRTU3hwUkVGQmFVUTdTVUZEYWtRc2RVSkJRWE5DTzFGQlFYUkNMRzlDUVVGelFqdFpRVUYwUWl4elFrRkJjMEk3U1VGRGRFSXNZVUZCWVR0SlFVTmlMR05CUVdNN1EwRkRha0k3TzBOQlJVUTdTVUZEU1N4WlFVRlpPMGxCUTFvc0swSkJRWFZDTzFsQlFYWkNMSFZDUVVGMVFqdEpRVU4yUWl4VlFVRlZPMGxCUTFZc2EwSkJRVzlETzBsQlEzQkRMSGxDUVVGblF6dEpRVU5vUXl3d1FrRkJOa1E3U1VGRE4wUXNZMEZCTmtNN1NVRkROME1zYjBKQlFXRTdVVUZCWWl4eFFrRkJZVHRaUVVGaUxHRkJRV0U3U1VGRFlpeGxRVUZsTzBOQlEyeENPenREUVVWRU8wbEJRMGtzZDBOQlFTdEVPMGxCUXk5RUxHbENRVUZ0Ump0RFFVTjBSanM3UTBGRlJEdEpRVU5KTEc5Q1FVRnBSRHRSUVVGcVJDeHZRa0ZCYVVRN1dVRkJha1FzWjBKQlFXbEVPMGxCUTJwRUxHZENRVUZuUWp0SlFVTm9RaXhwUWtGQmJVWTdTVUZEYmtZc2EwSkJRWEZFTzBsQlEzSkVMR3RDUVVFclF6dEpRVU12UXl4clFrRkJhMEk3U1VGRGJFSXNiMEpCUVc5RE8wbEJRM0JETEhsQ1FVRm5RenRKUVVOb1F5d3dRa0ZCTmtRN1NVRkROMFFzYjBKQlFXOUNPMGxCUTNCQ0xHMUNRVUZ0UWp0RFFVTjBRanM3UTBGRlJEdEpRVU5KTERCQ1FVRm5RenRKUVVOb1F5eG5SVUZCWjBVN1NVRkRhRVVzYTBKQlFXOURPMGxCUTNCRExHbENRVUYxUmp0SlFVTjJSaXh0UTBGQk9FTTdXVUZCT1VNc01rSkJRVGhETzBsQlF6bERMR3RDUVVGclFqdERRVU55UWpzN1EwRkZSRHRKUVVOSkxHMUNRVUZ0UWp0SlFVTnVRaXhWUVVGMVF6dEpRVU4yUXl4WFFVRjNRenRKUVVONFF5eFpRVUZaTzBsQlExb3NXVUZCYjBRN1NVRkRjRVFzZDBKQlFTdERPMGxCUXk5RExHOUNRVUZ0UXp0RFFVTjBRenM3UTBGRlJEdEpRVU5KTEdWQlFXVTdRMEZEYkVJN08wTkJSVVE3U1VGRFNTeHJRa0ZCYjBNN1NVRkRjRU1zZVVKQlFXZERPME5CUTI1RE96dERRVVZFTzBsQlEwa3NhVUpCUVdsQ08wTkJRM0JDT3p0RFFVVkVPMGxCUTBrc2VVSkJRWGxDTzBsQlEzcENMR2xDUVVGcFFpeERRVUZETEZkQlFWYzdRMEZEYUVNN08wTkJSVVE3T3p0SlFVZEpMR3RDUVVGeFJEdERRVU40UkRzN1EwRkZSQ3h6UWtGQmMwSTdPME5CUlhSQ08wbEJRMGtzY1VKQlFXTTdTVUZCWkN4eFFrRkJZenRKUVVGa0xHTkJRV003U1VGRFpDdzJRa0ZCZFVJN1NVRkJka0lzT0VKQlFYVkNPMUZCUVhaQ0xESkNRVUYxUWp0WlFVRjJRaXgxUWtGQmRVSTdTVUZEZGtJc01rSkJRWEZDTzFGQlFYSkNMSGRDUVVGeFFqdFpRVUZ5UWl4eFFrRkJjVUk3UTBGRGVFSTdPME5CUlVRN1NVRkRTU3hoUVVGNVF6dEpRVU42UXl4blFrRkJaMEk3U1VGRGFFSXNlVUpCUVdkRE8wbEJRMmhETERCQ1FVRXdRenRKUVVNeFF5d3dRa0ZCY1VVN1NVRkRja1VzYlVKQlFTdEdPMGxCUXk5R0xHdENRVUZyUWp0RFFVTnlRanM3UTBGRlJEdEpRVU5KTEhkQ1FVRXdRenRKUVVNeFF5eDVRa0ZCWjBNN1EwRkRia003TzBOQlJVUTdTVUZEU1N4M1FrRkJNRU03U1VGRE1VTXNNRUpCUVdkRE8wbEJRMmhETEdkQ1FVRm5RanRKUVVOb1FpeHZRa0ZCYjBJN1EwRkRka0k3TzBOQlJVUTdTVUZEU1N4elFrRkJjMElzUlVGQlJTeHhRMEZCY1VNN1NVRkROMFFzYzBKQlFYTkNPMGxCUTNSQ0xEaERRVUU0UXp0SlFVTTVReXh0UWtGQmJVSTdTVUZEYmtJc2NVSkJRWEZDTzBsQlEzSkNMRzlEUVVGdlF6dEpRVU53UXl4dFEwRkJiVU03UTBGRGRFTTdPME5CUlVRN1NVRkRTU3h6UWtGQmMwSXNRMEZCUXl4dlEwRkJiME03UTBGRE9VUTdPME5CUlVRN1NVRkRTU3hqUVVFMlF6dEpRVU0zUXl4M1FrRkJNRU03U1VGRE1VTXNlVUpCUVdkRE8wbEJRMmhETEN0Q1FVRXdSVHRKUVVNeFJTeG5RMEZCTWtVN1NVRkRNMFVzYVVOQlFUUkZPMGxCUXpWRkxHVkJRV1U3UTBGRGJFSTdPME5CUlVRN1NVRkRTU3h4UWtGQll6dEpRVUZrTEhGQ1FVRmpPMGxCUVdRc1kwRkJZenRKUVVOa0xEWkNRVUYxUWp0SlFVRjJRaXc0UWtGQmRVSTdVVUZCZGtJc01rSkJRWFZDTzFsQlFYWkNMSFZDUVVGMVFqdEpRVU4yUWl3eVFrRkJjVUk3VVVGQmNrSXNkMEpCUVhGQ08xbEJRWEpDTEhGQ1FVRnhRanREUVVONFFqczdRMEZGUkR0SlFVTkpMR2xDUVVGcFFqdERRVU53UWpzN1EwRkZSRHRKUVVOSkxHZENRVUZuUWp0RFFVTnVRanM3UTBGSlJDeHBRa0ZCYVVJN08wTkJSV3BDTzBsQlEwa3NaMEpCUVhWRE8wTkJRekZET3p0RFFVVkVPMGxCUTBrc01FTkJRVEJETzBsQlF6RkRMRFpDUVVGdlFqdFJRVUZ3UWl4dlFrRkJiMEk3U1VGRGNFSXNiMEpCUVdFN1VVRkJZaXh4UWtGQllUdFpRVUZpTEdGQlFXRTdTVUZEWWl4eFFrRkJaVHRSUVVGbUxHVkJRV1U3U1VGRFppeHJSVUZCYTBVN1NVRkRiRVVzYTBKQlFUWkRPMGxCUXpkRExIbEZRVUY1UlR0SlFVTjZSU3h0UWtGQmJVSTdRMEZEZEVJaUxDSm1hV3hsSWpvaVkyOXVkSEp2YkhNdVkzTnpJaXdpYzI5MWNtTmxjME52Ym5SbGJuUWlPbHNpTHlvZ1EyOXdlWEpwWjJoMElDaGpLU0JLZFhCNWRHVnlJRVJsZG1Wc2IzQnRaVzUwSUZSbFlXMHVYRzRnS2lCRWFYTjBjbWxpZFhSbFpDQjFibVJsY2lCMGFHVWdkR1Z5YlhNZ2IyWWdkR2hsSUUxdlpHbG1hV1ZrSUVKVFJDQk1hV05sYm5ObExseHVJQ292WEc1Y2JpQXZLaUJYWlNCcGJYQnZjblFnWVd4c0lHOW1JSFJvWlhObElIUnZaMlYwYUdWeUlHbHVJR0VnYzJsdVoyeGxJR056Y3lCbWFXeGxJR0psWTJGMWMyVWdkR2hsSUZkbFluQmhZMnRjYm14dllXUmxjaUJ6WldWeklHOXViSGtnYjI1bElHWnBiR1VnWVhRZ1lTQjBhVzFsTGlCVWFHbHpJR0ZzYkc5M2N5QndiM04wWTNOeklIUnZJSE5sWlNCMGFHVWdkbUZ5YVdGaWJHVmNibVJsWm1sdWFYUnBiMjV6SUhkb1pXNGdkR2hsZVNCaGNtVWdkWE5sWkM0Z0tpOWNibHh1UUdsdGNHOXlkQ0JjSWk0dmJHRmlkbUZ5YVdGaWJHVnpMbU56YzF3aU8xeHVRR2x0Y0c5eWRDQmNJaTR2ZDJsa1oyVjBjeTFpWVhObExtTnpjMXdpTzF4dUlpd2lMeW90TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExWeHVmQ0JEYjNCNWNtbG5hSFFnS0dNcElFcDFjSGwwWlhJZ1JHVjJaV3h2Y0cxbGJuUWdWR1ZoYlM1Y2Jud2dSR2x6ZEhKcFluVjBaV1FnZFc1a1pYSWdkR2hsSUhSbGNtMXpJRzltSUhSb1pTQk5iMlJwWm1sbFpDQkNVMFFnVEdsalpXNXpaUzVjYm53dExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRMUzB0TFMwdExTMHRLaTljYmx4dUx5cGNibFJvYVhNZ1ptbHNaU0JwY3lCamIzQnBaV1FnWm5KdmJTQjBhR1VnU25Wd2VYUmxja3hoWWlCd2NtOXFaV04wSUhSdklHUmxabWx1WlNCa1pXWmhkV3gwSUhOMGVXeHBibWNnWm05eVhHNTNhR1Z1SUhSb1pTQjNhV1JuWlhRZ2MzUjViR2x1WnlCcGN5QmpiMjF3YVd4bFpDQmtiM2R1SUhSdklHVnNhVzFwYm1GMFpTQkRVMU1nZG1GeWFXRmliR1Z6TGlCWFpTQnRZV3RsSUc5dVpWeHVZMmhoYm1kbElDMGdkMlVnWTI5dGJXVnVkQ0J2ZFhRZ2RHaGxJR1p2Ym5RZ2FXMXdiM0owSUdKbGJHOTNMbHh1S2k5Y2JseHVRR2x0Y0c5eWRDQmNJaTR2YldGMFpYSnBZV3hqYjJ4dmNuTXVZM056WENJN1hHNWNiaThxWEc1VWFHVWdabTlzYkc5M2FXNW5JRU5UVXlCMllYSnBZV0pzWlhNZ1pHVm1hVzVsSUhSb1pTQnRZV2x1TENCd2RXSnNhV01nUVZCSklHWnZjaUJ6ZEhsc2FXNW5JRXAxY0hsMFpYSk1ZV0l1WEc1VWFHVnpaU0IyWVhKcFlXSnNaWE1nYzJodmRXeGtJR0psSUhWelpXUWdZbmtnWVd4c0lIQnNkV2RwYm5NZ2QyaGxjbVYyWlhJZ2NHOXpjMmxpYkdVdUlFbHVJRzkwYUdWeVhHNTNiM0prY3l3Z2NHeDFaMmx1Y3lCemFHOTFiR1FnYm05MElHUmxabWx1WlNCamRYTjBiMjBnWTI5c2IzSnpMQ0J6YVhwbGN5d2daWFJqSUhWdWJHVnpjeUJoWW5OdmJIVjBaV3g1WEc1dVpXTmxjM05oY25rdUlGUm9hWE1nWlc1aFlteGxjeUIxYzJWeWN5QjBieUJqYUdGdVoyVWdkR2hsSUhacGMzVmhiQ0IwYUdWdFpTQnZaaUJLZFhCNWRHVnlUR0ZpWEc1aWVTQmphR0Z1WjJsdVp5QjBhR1Z6WlNCMllYSnBZV0pzWlhNdVhHNWNiazFoYm5rZ2RtRnlhV0ZpYkdWeklHRndjR1ZoY2lCcGJpQmhiaUJ2Y21SbGNtVmtJSE5sY1hWbGJtTmxJQ2d3TERFc01pd3pLUzRnVkdobGMyVWdjMlZ4ZFdWdVkyVnpYRzVoY21VZ1pHVnphV2R1WldRZ2RHOGdkMjl5YXlCM1pXeHNJSFJ2WjJWMGFHVnlMQ0J6YnlCbWIzSWdaWGhoYlhCc1pTd2dZQzB0YW5BdFltOXlaR1Z5TFdOdmJHOXlNV0FnYzJodmRXeGtYRzVpWlNCMWMyVmtJSGRwZEdnZ1lDMHRhbkF0YkdGNWIzVjBMV052Ykc5eU1XQXVJRlJvWlNCdWRXMWlaWEp6SUdoaGRtVWdkR2hsSUdadmJHeHZkMmx1WnlCdFpXRnVhVzVuY3pwY2JseHVLaUF3T2lCemRYQmxjaTF3Y21sdFlYSjVMQ0J5WlhObGNuWmxaQ0JtYjNJZ2MzQmxZMmxoYkNCbGJYQm9ZWE5wYzF4dUtpQXhPaUJ3Y21sdFlYSjVMQ0J0YjNOMElHbHRjRzl5ZEdGdWRDQjFibVJsY2lCdWIzSnRZV3dnYzJsMGRXRjBhVzl1YzF4dUtpQXlPaUJ6WldOdmJtUmhjbmtzSUc1bGVIUWdiVzl6ZENCcGJYQnZjblJoYm5RZ2RXNWtaWElnYm05eWJXRnNJSE5wZEhWaGRHbHZibk5jYmlvZ016b2dkR1Z5ZEdsaGNua3NJRzVsZUhRZ2JXOXpkQ0JwYlhCdmNuUmhiblFnZFc1a1pYSWdibTl5YldGc0lITnBkSFZoZEdsdmJuTmNibHh1VkdoeWIzVm5hRzkxZENCS2RYQjVkR1Z5VEdGaUxDQjNaU0JoY21VZ2JXOXpkR3g1SUdadmJHeHZkMmx1WnlCd2NtbHVZMmx3YkdWeklHWnliMjBnUjI5dloyeGxKM05jYmsxaGRHVnlhV0ZzSUVSbGMybG5iaUIzYUdWdUlITmxiR1ZqZEdsdVp5QmpiMnh2Y25NdUlGZGxJR0Z5WlNCdWIzUXNJR2h2ZDJWMlpYSXNJR1p2Ykd4dmQybHVaMXh1WVd4c0lHOW1JRTFFSUdGeklHbDBJR2x6SUc1dmRDQnZjSFJwYldsNlpXUWdabTl5SUdSbGJuTmxMQ0JwYm1admNtMWhkR2x2YmlCeWFXTm9JRlZKY3k1Y2Jpb3ZYRzVjYmx4dUx5cGNiaUFxSUU5d2RHbHZibUZzSUcxdmJtOXpjR0ZqWlNCbWIyNTBJR1p2Y2lCcGJuQjFkQzl2ZFhSd2RYUWdjSEp2YlhCMExseHVJQ292WEc0Z0x5b2dRMjl0YldWdWRHVmtJRzkxZENCcGJpQnBjSGwzYVdSblpYUnpJSE5wYm1ObElIZGxJR1J2YmlkMElHNWxaV1FnYVhRdUlDb3ZYRzR2S2lCQWFXMXdiM0owSUhWeWJDZ25hSFIwY0hNNkx5OW1iMjUwY3k1bmIyOW5iR1ZoY0dsekxtTnZiUzlqYzNNL1ptRnRhV3g1UFZKdlltOTBieXROYjI1dkp5azdJQ292WEc1Y2JpOHFYRzRnS2lCQlpHUmxaQ0JtYjNJZ1kyOXRjR0ZpYVhScGJHbDBlU0IzYVhSb0lHOTFkSEIxZENCaGNtVmhYRzRnS2k5Y2JqcHliMjkwSUh0Y2JpQWdMUzFxY0MxcFkyOXVMWE5sWVhKamFEb2dibTl1WlR0Y2JpQWdMUzFxY0MxMWFTMXpaV3hsWTNRdFkyRnlaWFE2SUc1dmJtVTdYRzU5WEc1Y2JseHVPbkp2YjNRZ2UxeHVYRzRnSUM4cUlFSnZjbVJsY25OY2JseHVJQ0JVYUdVZ1ptOXNiRzkzYVc1bklIWmhjbWxoWW14bGN5d2djM0JsWTJsbWVTQjBhR1VnZG1semRXRnNJSE4wZVd4cGJtY2diMllnWW05eVpHVnljeUJwYmlCS2RYQjVkR1Z5VEdGaUxseHVJQ0FnS2k5Y2JseHVJQ0F0TFdwd0xXSnZjbVJsY2kxM2FXUjBhRG9nTVhCNE8xeHVJQ0F0TFdwd0xXSnZjbVJsY2kxamIyeHZjakE2SUhaaGNpZ3RMVzFrTFdkeVpYa3ROekF3S1R0Y2JpQWdMUzFxY0MxaWIzSmtaWEl0WTI5c2IzSXhPaUIyWVhJb0xTMXRaQzFuY21WNUxUVXdNQ2s3WEc0Z0lDMHRhbkF0WW05eVpHVnlMV052Ykc5eU1qb2dkbUZ5S0MwdGJXUXRaM0psZVMwek1EQXBPMXh1SUNBdExXcHdMV0p2Y21SbGNpMWpiMnh2Y2pNNklIWmhjaWd0TFcxa0xXZHlaWGt0TVRBd0tUdGNibHh1SUNBdktpQlZTU0JHYjI1MGMxeHVYRzRnSUZSb1pTQlZTU0JtYjI1MElFTlRVeUIyWVhKcFlXSnNaWE1nWVhKbElIVnpaV1FnWm05eUlIUm9aU0IwZVhCdlozSmhjR2g1SUdGc2JDQnZaaUIwYUdVZ1NuVndlWFJsY2t4aFlseHVJQ0IxYzJWeUlHbHVkR1Z5Wm1GalpTQmxiR1Z0Wlc1MGN5QjBhR0YwSUdGeVpTQnViM1FnWkdseVpXTjBiSGtnZFhObGNpQm5aVzVsY21GMFpXUWdZMjl1ZEdWdWRDNWNiaUFnS2k5Y2JseHVJQ0F0TFdwd0xYVnBMV1p2Ym5RdGMyTmhiR1V0Wm1GamRHOXlPaUF4TGpJN1hHNGdJQzB0YW5BdGRXa3RabTl1ZEMxemFYcGxNRG9nWTJGc1l5aDJZWElvTFMxcWNDMTFhUzFtYjI1MExYTnBlbVV4S1M5MllYSW9MUzFxY0MxMWFTMW1iMjUwTFhOallXeGxMV1poWTNSdmNpa3BPMXh1SUNBdExXcHdMWFZwTFdadmJuUXRjMmw2WlRFNklERXpjSGc3SUM4cUlFSmhjMlVnWm05dWRDQnphWHBsSUNvdlhHNGdJQzB0YW5BdGRXa3RabTl1ZEMxemFYcGxNam9nWTJGc1l5aDJZWElvTFMxcWNDMTFhUzFtYjI1MExYTnBlbVV4S1NwMllYSW9MUzFxY0MxMWFTMW1iMjUwTFhOallXeGxMV1poWTNSdmNpa3BPMXh1SUNBdExXcHdMWFZwTFdadmJuUXRjMmw2WlRNNklHTmhiR01vZG1GeUtDMHRhbkF0ZFdrdFptOXVkQzF6YVhwbE1pa3FkbUZ5S0MwdGFuQXRkV2t0Wm05dWRDMXpZMkZzWlMxbVlXTjBiM0lwS1R0Y2JpQWdMUzFxY0MxMWFTMXBZMjl1TFdadmJuUXRjMmw2WlRvZ01UUndlRHNnTHlvZ1JXNXpkWEpsY3lCd2VDQndaWEptWldOMElFWnZiblJCZDJWemIyMWxJR2xqYjI1eklDb3ZYRzRnSUMwdGFuQXRkV2t0Wm05dWRDMW1ZVzFwYkhrNklGd2lTR1ZzZG1WMGFXTmhJRTVsZFdWY0lpd2dTR1ZzZG1WMGFXTmhMQ0JCY21saGJDd2djMkZ1Y3kxelpYSnBaanRjYmx4dUlDQXZLaUJWYzJVZ2RHaGxjMlVnWm05dWRDQmpiMnh2Y25NZ1lXZGhhVzV6ZENCMGFHVWdZMjl5Y21WemNHOXVaR2x1WnlCdFlXbHVJR3hoZVc5MWRDQmpiMnh2Y25NdVhHNGdJQ0FnSUVsdUlHRWdiR2xuYUhRZ2RHaGxiV1VzSUhSb1pYTmxJR2R2SUdaeWIyMGdaR0Z5YXlCMGJ5QnNhV2RvZEM1Y2JpQWdLaTljYmx4dUlDQXRMV3B3TFhWcExXWnZiblF0WTI5c2IzSXdPaUJ5WjJKaEtEQXNNQ3d3TERFdU1DazdYRzRnSUMwdGFuQXRkV2t0Wm05dWRDMWpiMnh2Y2pFNklISm5ZbUVvTUN3d0xEQXNNQzQ0S1R0Y2JpQWdMUzFxY0MxMWFTMW1iMjUwTFdOdmJHOXlNam9nY21kaVlTZ3dMREFzTUN3d0xqVXBPMXh1SUNBdExXcHdMWFZwTFdadmJuUXRZMjlzYjNJek9pQnlaMkpoS0RBc01Dd3dMREF1TXlrN1hHNWNiaUFnTHlvZ1ZYTmxJSFJvWlhObElHRm5ZV2x1YzNRZ2RHaGxJR0p5WVc1a0wyRmpZMlZ1ZEM5M1lYSnVMMlZ5Y205eUlHTnZiRzl5Y3k1Y2JpQWdJQ0FnVkdobGMyVWdkMmxzYkNCMGVYQnBZMkZzYkhrZ1oyOGdabkp2YlNCc2FXZG9kQ0IwYnlCa1lYSnJaWElzSUdsdUlHSnZkR2dnWVNCa1lYSnJJR0Z1WkNCc2FXZG9kQ0IwYUdWdFpWeHVJQ0FnS2k5Y2JseHVJQ0F0TFdwd0xXbHVkbVZ5YzJVdGRXa3RabTl1ZEMxamIyeHZjakE2SUhKblltRW9NalUxTERJMU5Td3lOVFVzTVNrN1hHNGdJQzB0YW5BdGFXNTJaWEp6WlMxMWFTMW1iMjUwTFdOdmJHOXlNVG9nY21kaVlTZ3lOVFVzTWpVMUxESTFOU3d4TGpBcE8xeHVJQ0F0TFdwd0xXbHVkbVZ5YzJVdGRXa3RabTl1ZEMxamIyeHZjakk2SUhKblltRW9NalUxTERJMU5Td3lOVFVzTUM0M0tUdGNiaUFnTFMxcWNDMXBiblpsY25ObExYVnBMV1p2Ym5RdFkyOXNiM0l6T2lCeVoySmhLREkxTlN3eU5UVXNNalUxTERBdU5TazdYRzVjYmlBZ0x5b2dRMjl1ZEdWdWRDQkdiMjUwYzF4dVhHNGdJRU52Ym5SbGJuUWdabTl1ZENCMllYSnBZV0pzWlhNZ1lYSmxJSFZ6WldRZ1ptOXlJSFI1Y0c5bmNtRndhSGtnYjJZZ2RYTmxjaUJuWlc1bGNtRjBaV1FnWTI5dWRHVnVkQzVjYmlBZ0tpOWNibHh1SUNBdExXcHdMV052Ym5SbGJuUXRabTl1ZEMxemFYcGxPaUF4TTNCNE8xeHVJQ0F0TFdwd0xXTnZiblJsYm5RdGJHbHVaUzFvWldsbmFIUTZJREV1TlR0Y2JpQWdMUzFxY0MxamIyNTBaVzUwTFdadmJuUXRZMjlzYjNJd09pQmliR0ZqYXp0Y2JpQWdMUzFxY0MxamIyNTBaVzUwTFdadmJuUXRZMjlzYjNJeE9pQmliR0ZqYXp0Y2JpQWdMUzFxY0MxamIyNTBaVzUwTFdadmJuUXRZMjlzYjNJeU9pQjJZWElvTFMxdFpDMW5jbVY1TFRjd01DazdYRzRnSUMwdGFuQXRZMjl1ZEdWdWRDMW1iMjUwTFdOdmJHOXlNem9nZG1GeUtDMHRiV1F0WjNKbGVTMDFNREFwTzF4dVhHNGdJQzB0YW5BdGRXa3RabTl1ZEMxelkyRnNaUzFtWVdOMGIzSTZJREV1TWp0Y2JpQWdMUzFxY0MxMWFTMW1iMjUwTFhOcGVtVXdPaUJqWVd4aktIWmhjaWd0TFdwd0xYVnBMV1p2Ym5RdGMybDZaVEVwTDNaaGNpZ3RMV3B3TFhWcExXWnZiblF0YzJOaGJHVXRabUZqZEc5eUtTazdYRzRnSUMwdGFuQXRkV2t0Wm05dWRDMXphWHBsTVRvZ01UTndlRHNnTHlvZ1FtRnpaU0JtYjI1MElITnBlbVVnS2k5Y2JpQWdMUzFxY0MxMWFTMW1iMjUwTFhOcGVtVXlPaUJqWVd4aktIWmhjaWd0TFdwd0xYVnBMV1p2Ym5RdGMybDZaVEVwS25aaGNpZ3RMV3B3TFhWcExXWnZiblF0YzJOaGJHVXRabUZqZEc5eUtTazdYRzRnSUMwdGFuQXRkV2t0Wm05dWRDMXphWHBsTXpvZ1kyRnNZeWgyWVhJb0xTMXFjQzExYVMxbWIyNTBMWE5wZW1VeUtTcDJZWElvTFMxcWNDMTFhUzFtYjI1MExYTmpZV3hsTFdaaFkzUnZjaWtwTzF4dVhHNGdJQzB0YW5BdFkyOWtaUzFtYjI1MExYTnBlbVU2SURFemNIZzdYRzRnSUMwdGFuQXRZMjlrWlMxc2FXNWxMV2hsYVdkb2REb2dNUzR6TURjN1hHNGdJQzB0YW5BdFkyOWtaUzF3WVdSa2FXNW5PaUExY0hnN1hHNGdJQzB0YW5BdFkyOWtaUzFtYjI1MExXWmhiV2xzZVRvZ2JXOXViM053WVdObE8xeHVYRzVjYmlBZ0x5b2dUR0Y1YjNWMFhHNWNiaUFnVkdobElHWnZiR3h2ZDJsdVp5QmhjbVVnZEdobElHMWhhVzRnYkdGNWIzVjBJR052Ykc5eWN5QjFjMlVnYVc0Z1NuVndlWFJsY2t4aFlpNGdTVzRnWVNCc2FXZG9kRnh1SUNCMGFHVnRaU0IwYUdWelpTQjNiM1ZzWkNCbmJ5Qm1jbTl0SUd4cFoyaDBJSFJ2SUdSaGNtc3VYRzRnSUNvdlhHNWNiaUFnTFMxcWNDMXNZWGx2ZFhRdFkyOXNiM0l3T2lCM2FHbDBaVHRjYmlBZ0xTMXFjQzFzWVhsdmRYUXRZMjlzYjNJeE9pQjNhR2wwWlR0Y2JpQWdMUzFxY0Mxc1lYbHZkWFF0WTI5c2IzSXlPaUIyWVhJb0xTMXRaQzFuY21WNUxUSXdNQ2s3WEc0Z0lDMHRhbkF0YkdGNWIzVjBMV052Ykc5eU16b2dkbUZ5S0MwdGJXUXRaM0psZVMwME1EQXBPMXh1WEc0Z0lDOHFJRUp5WVc1a0wyRmpZMlZ1ZENBcUwxeHVYRzRnSUMwdGFuQXRZbkpoYm1RdFkyOXNiM0l3T2lCMllYSW9MUzF0WkMxaWJIVmxMVGN3TUNrN1hHNGdJQzB0YW5BdFluSmhibVF0WTI5c2IzSXhPaUIyWVhJb0xTMXRaQzFpYkhWbExUVXdNQ2s3WEc0Z0lDMHRhbkF0WW5KaGJtUXRZMjlzYjNJeU9pQjJZWElvTFMxdFpDMWliSFZsTFRNd01DazdYRzRnSUMwdGFuQXRZbkpoYm1RdFkyOXNiM0l6T2lCMllYSW9MUzF0WkMxaWJIVmxMVEV3TUNrN1hHNWNiaUFnTFMxcWNDMWhZMk5sYm5RdFkyOXNiM0l3T2lCMllYSW9MUzF0WkMxbmNtVmxiaTAzTURBcE8xeHVJQ0F0TFdwd0xXRmpZMlZ1ZEMxamIyeHZjakU2SUhaaGNpZ3RMVzFrTFdkeVpXVnVMVFV3TUNrN1hHNGdJQzB0YW5BdFlXTmpaVzUwTFdOdmJHOXlNam9nZG1GeUtDMHRiV1F0WjNKbFpXNHRNekF3S1R0Y2JpQWdMUzFxY0MxaFkyTmxiblF0WTI5c2IzSXpPaUIyWVhJb0xTMXRaQzFuY21WbGJpMHhNREFwTzF4dVhHNGdJQzhxSUZOMFlYUmxJR052Ykc5eWN5QW9kMkZ5Yml3Z1pYSnliM0lzSUhOMVkyTmxjM01zSUdsdVptOHBJQ292WEc1Y2JpQWdMUzFxY0MxM1lYSnVMV052Ykc5eU1Eb2dkbUZ5S0MwdGJXUXRiM0poYm1kbExUY3dNQ2s3WEc0Z0lDMHRhbkF0ZDJGeWJpMWpiMnh2Y2pFNklIWmhjaWd0TFcxa0xXOXlZVzVuWlMwMU1EQXBPMXh1SUNBdExXcHdMWGRoY200dFkyOXNiM0l5T2lCMllYSW9MUzF0WkMxdmNtRnVaMlV0TXpBd0tUdGNiaUFnTFMxcWNDMTNZWEp1TFdOdmJHOXlNem9nZG1GeUtDMHRiV1F0YjNKaGJtZGxMVEV3TUNrN1hHNWNiaUFnTFMxcWNDMWxjbkp2Y2kxamIyeHZjakE2SUhaaGNpZ3RMVzFrTFhKbFpDMDNNREFwTzF4dUlDQXRMV3B3TFdWeWNtOXlMV052Ykc5eU1Ub2dkbUZ5S0MwdGJXUXRjbVZrTFRVd01DazdYRzRnSUMwdGFuQXRaWEp5YjNJdFkyOXNiM0l5T2lCMllYSW9MUzF0WkMxeVpXUXRNekF3S1R0Y2JpQWdMUzFxY0MxbGNuSnZjaTFqYjJ4dmNqTTZJSFpoY2lndExXMWtMWEpsWkMweE1EQXBPMXh1WEc0Z0lDMHRhbkF0YzNWalkyVnpjeTFqYjJ4dmNqQTZJSFpoY2lndExXMWtMV2R5WldWdUxUY3dNQ2s3WEc0Z0lDMHRhbkF0YzNWalkyVnpjeTFqYjJ4dmNqRTZJSFpoY2lndExXMWtMV2R5WldWdUxUVXdNQ2s3WEc0Z0lDMHRhbkF0YzNWalkyVnpjeTFqYjJ4dmNqSTZJSFpoY2lndExXMWtMV2R5WldWdUxUTXdNQ2s3WEc0Z0lDMHRhbkF0YzNWalkyVnpjeTFqYjJ4dmNqTTZJSFpoY2lndExXMWtMV2R5WldWdUxURXdNQ2s3WEc1Y2JpQWdMUzFxY0MxcGJtWnZMV052Ykc5eU1Eb2dkbUZ5S0MwdGJXUXRZM2xoYmkwM01EQXBPMXh1SUNBdExXcHdMV2x1Wm04dFkyOXNiM0l4T2lCMllYSW9MUzF0WkMxamVXRnVMVFV3TUNrN1hHNGdJQzB0YW5BdGFXNW1ieTFqYjJ4dmNqSTZJSFpoY2lndExXMWtMV041WVc0dE16QXdLVHRjYmlBZ0xTMXFjQzFwYm1adkxXTnZiRzl5TXpvZ2RtRnlLQzB0YldRdFkzbGhiaTB4TURBcE8xeHVYRzRnSUM4cUlFTmxiR3dnYzNCbFkybG1hV01nYzNSNWJHVnpJQ292WEc1Y2JpQWdMUzFxY0MxalpXeHNMWEJoWkdScGJtYzZJRFZ3ZUR0Y2JpQWdMUzFxY0MxalpXeHNMV1ZrYVhSdmNpMWlZV05yWjNKdmRXNWtPaUFqWmpkbU4yWTNPMXh1SUNBdExXcHdMV05sYkd3dFpXUnBkRzl5TFdKdmNtUmxjaTFqYjJ4dmNqb2dJMk5tWTJaalpqdGNiaUFnTFMxcWNDMWpaV3hzTFdWa2FYUnZjaTFpWVdOclozSnZkVzVrTFdWa2FYUTZJSFpoY2lndExXcHdMWFZwTFd4aGVXOTFkQzFqYjJ4dmNqRXBPMXh1SUNBdExXcHdMV05sYkd3dFpXUnBkRzl5TFdKdmNtUmxjaTFqYjJ4dmNpMWxaR2wwT2lCMllYSW9MUzFxY0MxaWNtRnVaQzFqYjJ4dmNqRXBPMXh1SUNBdExXcHdMV05sYkd3dGNISnZiWEIwTFhkcFpIUm9PaUF4TURCd2VEdGNiaUFnTFMxcWNDMWpaV3hzTFhCeWIyMXdkQzFtYjI1MExXWmhiV2xzZVRvZ0oxSnZZbTkwYnlCTmIyNXZKeXdnYlc5dWIzTndZV05sTzF4dUlDQXRMV3B3TFdObGJHd3RjSEp2YlhCMExXeGxkSFJsY2kxemNHRmphVzVuT2lBd2NIZzdYRzRnSUMwdGFuQXRZMlZzYkMxd2NtOXRjSFF0YjNCaFkybDBlVG9nTVM0d08xeHVJQ0F0TFdwd0xXTmxiR3d0Y0hKdmJYQjBMVzl3WVdOcGRIa3RibTkwTFdGamRHbDJaVG9nTUM0ME8xeHVJQ0F0TFdwd0xXTmxiR3d0Y0hKdmJYQjBMV1p2Ym5RdFkyOXNiM0l0Ym05MExXRmpkR2wyWlRvZ2RtRnlLQzB0YldRdFozSmxlUzAzTURBcE8xeHVJQ0F2S2lCQklHTjFjM1J2YlNCaWJHVnVaQ0J2WmlCTlJDQm5jbVY1SUdGdVpDQmliSFZsSURZd01GeHVJQ0FnS2lCVFpXVWdhSFIwY0hNNkx5OXRaWGxsY25kbFlpNWpiMjB2WlhKcFl5OTBiMjlzY3k5amIyeHZjaTFpYkdWdVpDOGpOVFEyUlRkQk9qRkZPRGhGTlRvMU9taGxlQ0FxTDF4dUlDQXRMV3B3TFdObGJHd3RhVzV3Y205dGNIUXRabTl1ZEMxamIyeHZjam9nSXpNd04wWkRNVHRjYmlBZ0x5b2dRU0JqZFhOMGIyMGdZbXhsYm1RZ2IyWWdUVVFnWjNKbGVTQmhibVFnYjNKaGJtZGxJRFl3TUZ4dUlDQWdLaUJvZEhSd2N6b3ZMMjFsZVdWeWQyVmlMbU52YlM5bGNtbGpMM1J2YjJ4ekwyTnZiRzl5TFdKc1pXNWtMeU0xTkRaRk4wRTZSalExTVRGRk9qVTZhR1Y0SUNvdlhHNGdJQzB0YW5BdFkyVnNiQzF2ZFhSd2NtOXRjSFF0Wm05dWRDMWpiMnh2Y2pvZ0kwSkdOVUl6UkR0Y2JseHVJQ0F2S2lCT2IzUmxZbTl2YXlCemNHVmphV1pwWXlCemRIbHNaWE1nS2k5Y2JseHVJQ0F0TFdwd0xXNXZkR1ZpYjI5ckxYQmhaR1JwYm1jNklERXdjSGc3WEc0Z0lDMHRhbkF0Ym05MFpXSnZiMnN0YzJOeWIyeHNMWEJoWkdScGJtYzZJREV3TUhCNE8xeHVYRzRnSUM4cUlFTnZibk52YkdVZ2MzQmxZMmxtYVdNZ2MzUjViR1Z6SUNvdlhHNWNiaUFnTFMxcWNDMWpiMjV6YjJ4bExXSmhZMnRuY205MWJtUTZJSFpoY2lndExXMWtMV2R5WlhrdE1UQXdLVHRjYmx4dUlDQXZLaUJVYjI5c1ltRnlJSE53WldOcFptbGpJSE4wZVd4bGN5QXFMMXh1WEc0Z0lDMHRhbkF0ZEc5dmJHSmhjaTFpYjNKa1pYSXRZMjlzYjNJNklIWmhjaWd0TFcxa0xXZHlaWGt0TkRBd0tUdGNiaUFnTFMxcWNDMTBiMjlzWW1GeUxXMXBZM0p2TFdobGFXZG9kRG9nT0hCNE8xeHVJQ0F0TFdwd0xYUnZiMnhpWVhJdFltRmphMmR5YjNWdVpEb2dkbUZ5S0MwdGFuQXRiR0Y1YjNWMExXTnZiRzl5TUNrN1hHNGdJQzB0YW5BdGRHOXZiR0poY2kxaWIzZ3RjMmhoWkc5M09pQXdjSGdnTUhCNElESndlQ0F3Y0hnZ2NtZGlZU2d3TERBc01Dd3dMakkwS1R0Y2JpQWdMUzFxY0MxMGIyOXNZbUZ5TFdobFlXUmxjaTF0WVhKbmFXNDZJRFJ3ZUNBMGNIZ2dNSEI0SURSd2VEdGNiaUFnTFMxcWNDMTBiMjlzWW1GeUxXRmpkR2wyWlMxaVlXTnJaM0p2ZFc1a09pQjJZWElvTFMxdFpDMW5jbVY1TFRNd01DazdYRzU5WEc0aUxDSXZLaXBjYmlBcUlGUm9aU0J0WVhSbGNtbGhiQ0JrWlhOcFoyNGdZMjlzYjNKeklHRnlaU0JoWkdGd2RHVmtJR1p5YjIwZ1oyOXZaMnhsTFcxaGRHVnlhV0ZzTFdOdmJHOXlJSFl4TGpJdU5seHVJQ29nYUhSMGNITTZMeTluYVhSb2RXSXVZMjl0TDJSaGJteGxkbUZ1TDJkdmIyZHNaUzF0WVhSbGNtbGhiQzFqYjJ4dmNseHVJQ29nYUhSMGNITTZMeTluYVhSb2RXSXVZMjl0TDJSaGJteGxkbUZ1TDJkdmIyZHNaUzF0WVhSbGNtbGhiQzFqYjJ4dmNpOWliRzlpTDJZMk4yTmhOV1kwTURJNFlqSm1NV0l6TkRnMk1tWTJOR0l3WTJFMk56TXlNMlk1TVdJd09EZ3ZaR2x6ZEM5d1lXeGxkSFJsTG5aaGNpNWpjM05jYmlBcVhHNGdLaUJVYUdVZ2JHbGpaVzV6WlNCbWIzSWdkR2hsSUcxaGRHVnlhV0ZzSUdSbGMybG5iaUJqYjJ4dmNpQkRVMU1nZG1GeWFXRmliR1Z6SUdseklHRnpJR1p2Ykd4dmQzTWdLSE5sWlZ4dUlDb2dhSFIwY0hNNkx5OW5hWFJvZFdJdVkyOXRMMlJoYm14bGRtRnVMMmR2YjJkc1pTMXRZWFJsY21saGJDMWpiMnh2Y2k5aWJHOWlMMlkyTjJOaE5XWTBNREk0WWpKbU1XSXpORGcyTW1ZMk5HSXdZMkUyTnpNeU0yWTVNV0l3T0RndlRFbERSVTVUUlNsY2JpQXFYRzRnS2lCVWFHVWdUVWxVSUV4cFkyVnVjMlVnS0UxSlZDbGNiaUFxWEc0Z0tpQkRiM0I1Y21sbmFIUWdLR01wSURJd01UUWdSR0Z1SUV4bElGWmhibHh1SUNwY2JpQXFJRkJsY20xcGMzTnBiMjRnYVhNZ2FHVnlaV0o1SUdkeVlXNTBaV1FzSUdaeVpXVWdiMllnWTJoaGNtZGxMQ0IwYnlCaGJua2djR1Z5YzI5dUlHOWlkR0ZwYm1sdVp5QmhJR052Y0hsY2JpQXFJRzltSUhSb2FYTWdjMjltZEhkaGNtVWdZVzVrSUdGemMyOWphV0YwWldRZ1pHOWpkVzFsYm5SaGRHbHZiaUJtYVd4bGN5QW9kR2hsSUZ3aVUyOW1kSGRoY21WY0lpa3NJSFJ2SUdSbFlXeGNiaUFxSUdsdUlIUm9aU0JUYjJaMGQyRnlaU0IzYVhSb2IzVjBJSEpsYzNSeWFXTjBhVzl1TENCcGJtTnNkV1JwYm1jZ2QybDBhRzkxZENCc2FXMXBkR0YwYVc5dUlIUm9aU0J5YVdkb2RITmNiaUFxSUhSdklIVnpaU3dnWTI5d2VTd2diVzlrYVdaNUxDQnRaWEpuWlN3Z2NIVmliR2x6YUN3Z1pHbHpkSEpwWW5WMFpTd2djM1ZpYkdsalpXNXpaU3dnWVc1a0wyOXlJSE5sYkd4Y2JpQXFJR052Y0dsbGN5QnZaaUIwYUdVZ1UyOW1kSGRoY21Vc0lHRnVaQ0IwYnlCd1pYSnRhWFFnY0dWeWMyOXVjeUIwYnlCM2FHOXRJSFJvWlNCVGIyWjBkMkZ5WlNCcGMxeHVJQ29nWm5WeWJtbHphR1ZrSUhSdklHUnZJSE52TENCemRXSnFaV04wSUhSdklIUm9aU0JtYjJ4c2IzZHBibWNnWTI5dVpHbDBhVzl1Y3pwY2JpQXFYRzRnS2lCVWFHVWdZV0p2ZG1VZ1kyOXdlWEpwWjJoMElHNXZkR2xqWlNCaGJtUWdkR2hwY3lCd1pYSnRhWE56YVc5dUlHNXZkR2xqWlNCemFHRnNiQ0JpWlNCcGJtTnNkV1JsWkNCcGJseHVJQ29nWVd4c0lHTnZjR2xsY3lCdmNpQnpkV0p6ZEdGdWRHbGhiQ0J3YjNKMGFXOXVjeUJ2WmlCMGFHVWdVMjltZEhkaGNtVXVYRzRnS2x4dUlDb2dWRWhGSUZOUFJsUlhRVkpGSUVsVElGQlNUMVpKUkVWRUlGd2lRVk1nU1ZOY0lpd2dWMGxVU0U5VlZDQlhRVkpTUVU1VVdTQlBSaUJCVGxrZ1MwbE9SQ3dnUlZoUVVrVlRVeUJQVWx4dUlDb2dTVTFRVEVsRlJDd2dTVTVEVEZWRVNVNUhJRUpWVkNCT1QxUWdURWxOU1ZSRlJDQlVUeUJVU0VVZ1YwRlNVa0ZPVkVsRlV5QlBSaUJOUlZKRFNFRk9WRUZDU1V4SlZGa3NYRzRnS2lCR1NWUk9SVk5USUVaUFVpQkJJRkJCVWxSSlExVk1RVklnVUZWU1VFOVRSU0JCVGtRZ1RrOU9TVTVHVWtsT1IwVk5SVTVVTGlCSlRpQk9UeUJGVmtWT1ZDQlRTRUZNVENCVVNFVmNiaUFxSUVGVlZFaFBVbE1nVDFJZ1EwOVFXVkpKUjBoVUlFaFBURVJGVWxNZ1FrVWdURWxCUWt4RklFWlBVaUJCVGxrZ1EweEJTVTBzSUVSQlRVRkhSVk1nVDFJZ1QxUklSVkpjYmlBcUlFeEpRVUpKVEVsVVdTd2dWMGhGVkVoRlVpQkpUaUJCVGlCQlExUkpUMDRnVDBZZ1EwOU9WRkpCUTFRc0lGUlBVbFFnVDFJZ1QxUklSVkpYU1ZORkxDQkJVa2xUU1U1SElFWlNUMDBzWEc0Z0tpQlBWVlFnVDBZZ1QxSWdTVTRnUTA5T1RrVkRWRWxQVGlCWFNWUklJRlJJUlNCVFQwWlVWMEZTUlNCUFVpQlVTRVVnVlZORklFOVNJRTlVU0VWU0lFUkZRVXhKVGtkVElFbE9JRlJJUlZ4dUlDb2dVMDlHVkZkQlVrVXVYRzRnS2k5Y2JqcHliMjkwSUh0Y2JpQWdMUzF0WkMxeVpXUXROVEE2SUNOR1JrVkNSVVU3WEc0Z0lDMHRiV1F0Y21Wa0xURXdNRG9nSTBaR1EwUkVNanRjYmlBZ0xTMXRaQzF5WldRdE1qQXdPaUFqUlVZNVFUbEJPMXh1SUNBdExXMWtMWEpsWkMwek1EQTZJQ05GTlRjek56TTdYRzRnSUMwdGJXUXRjbVZrTFRRd01Eb2dJMFZHTlRNMU1EdGNiaUFnTFMxdFpDMXlaV1F0TlRBd09pQWpSalEwTXpNMk8xeHVJQ0F0TFcxa0xYSmxaQzAyTURBNklDTkZOVE01TXpVN1hHNGdJQzB0YldRdGNtVmtMVGN3TURvZ0kwUXpNa1l5Ump0Y2JpQWdMUzF0WkMxeVpXUXRPREF3T2lBalF6WXlPREk0TzF4dUlDQXRMVzFrTFhKbFpDMDVNREE2SUNOQ056RkRNVU03WEc0Z0lDMHRiV1F0Y21Wa0xVRXhNREE2SUNOR1JqaEJPREE3WEc0Z0lDMHRiV1F0Y21Wa0xVRXlNREE2SUNOR1JqVXlOVEk3WEc0Z0lDMHRiV1F0Y21Wa0xVRTBNREE2SUNOR1JqRTNORFE3WEc0Z0lDMHRiV1F0Y21Wa0xVRTNNREE2SUNORU5UQXdNREE3WEc1Y2JpQWdMUzF0WkMxd2FXNXJMVFV3T2lBalJrTkZORVZETzF4dUlDQXRMVzFrTFhCcGJtc3RNVEF3T2lBalJqaENRa1F3TzF4dUlDQXRMVzFrTFhCcGJtc3RNakF3T2lBalJqUTRSa0l4TzF4dUlDQXRMVzFrTFhCcGJtc3RNekF3T2lBalJqQTJNamt5TzF4dUlDQXRMVzFrTFhCcGJtc3ROREF3T2lBalJVTTBNRGRCTzF4dUlDQXRMVzFrTFhCcGJtc3ROVEF3T2lBalJUa3hSVFl6TzF4dUlDQXRMVzFrTFhCcGJtc3ROakF3T2lBalJEZ3hRall3TzF4dUlDQXRMVzFrTFhCcGJtc3ROekF3T2lBalF6SXhPRFZDTzF4dUlDQXRMVzFrTFhCcGJtc3RPREF3T2lBalFVUXhORFUzTzF4dUlDQXRMVzFrTFhCcGJtc3RPVEF3T2lBak9EZ3dSVFJHTzF4dUlDQXRMVzFrTFhCcGJtc3RRVEV3TURvZ0kwWkdPREJCUWp0Y2JpQWdMUzF0WkMxd2FXNXJMVUV5TURBNklDTkdSalF3T0RFN1hHNGdJQzB0YldRdGNHbHVheTFCTkRBd09pQWpSalV3TURVM08xeHVJQ0F0TFcxa0xYQnBibXN0UVRjd01Eb2dJME0xTVRFMk1qdGNibHh1SUNBdExXMWtMWEIxY25Cc1pTMDFNRG9nSTBZelJUVkdOVHRjYmlBZ0xTMXRaQzF3ZFhKd2JHVXRNVEF3T2lBalJURkNSVVUzTzF4dUlDQXRMVzFrTFhCMWNuQnNaUzB5TURBNklDTkRSVGt6UkRnN1hHNGdJQzB0YldRdGNIVnljR3hsTFRNd01Eb2dJMEpCTmpoRE9EdGNiaUFnTFMxdFpDMXdkWEp3YkdVdE5EQXdPaUFqUVVJME4wSkRPMXh1SUNBdExXMWtMWEIxY25Cc1pTMDFNREE2SUNNNVF6STNRakE3WEc0Z0lDMHRiV1F0Y0hWeWNHeGxMVFl3TURvZ0l6aEZNalJCUVR0Y2JpQWdMUzF0WkMxd2RYSndiR1V0TnpBd09pQWpOMEl4UmtFeU8xeHVJQ0F0TFcxa0xYQjFjbkJzWlMwNE1EQTZJQ00yUVRGQ09VRTdYRzRnSUMwdGJXUXRjSFZ5Y0d4bExUa3dNRG9nSXpSQk1UUTRRenRjYmlBZ0xTMXRaQzF3ZFhKd2JHVXRRVEV3TURvZ0kwVkJPREJHUXp0Y2JpQWdMUzF0WkMxd2RYSndiR1V0UVRJd01Eb2dJMFV3TkRCR1FqdGNiaUFnTFMxdFpDMXdkWEp3YkdVdFFUUXdNRG9nSTBRMU1EQkdPVHRjYmlBZ0xTMXRaQzF3ZFhKd2JHVXRRVGN3TURvZ0kwRkJNREJHUmp0Y2JseHVJQ0F0TFcxa0xXUmxaWEF0Y0hWeWNHeGxMVFV3T2lBalJVUkZOMFkyTzF4dUlDQXRMVzFrTFdSbFpYQXRjSFZ5Y0d4bExURXdNRG9nSTBReFF6UkZPVHRjYmlBZ0xTMXRaQzFrWldWd0xYQjFjbkJzWlMweU1EQTZJQ05DTXpsRVJFSTdYRzRnSUMwdGJXUXRaR1ZsY0Mxd2RYSndiR1V0TXpBd09pQWpPVFUzTlVORU8xeHVJQ0F0TFcxa0xXUmxaWEF0Y0hWeWNHeGxMVFF3TURvZ0l6ZEZOVGRETWp0Y2JpQWdMUzF0WkMxa1pXVndMWEIxY25Cc1pTMDFNREE2SUNNMk56TkJRamM3WEc0Z0lDMHRiV1F0WkdWbGNDMXdkWEp3YkdVdE5qQXdPaUFqTlVVek5VSXhPMXh1SUNBdExXMWtMV1JsWlhBdGNIVnljR3hsTFRjd01Eb2dJelV4TWtSQk9EdGNiaUFnTFMxdFpDMWtaV1Z3TFhCMWNuQnNaUzA0TURBNklDTTBOVEkzUVRBN1hHNGdJQzB0YldRdFpHVmxjQzF3ZFhKd2JHVXRPVEF3T2lBak16RXhRamt5TzF4dUlDQXRMVzFrTFdSbFpYQXRjSFZ5Y0d4bExVRXhNREE2SUNOQ016ZzRSa1k3WEc0Z0lDMHRiV1F0WkdWbGNDMXdkWEp3YkdVdFFUSXdNRG9nSXpkRE5FUkdSanRjYmlBZ0xTMXRaQzFrWldWd0xYQjFjbkJzWlMxQk5EQXdPaUFqTmpVeFJrWkdPMXh1SUNBdExXMWtMV1JsWlhBdGNIVnljR3hsTFVFM01EQTZJQ00yTWpBd1JVRTdYRzVjYmlBZ0xTMXRaQzFwYm1ScFoyOHROVEE2SUNORk9FVkJSalk3WEc0Z0lDMHRiV1F0YVc1a2FXZHZMVEV3TURvZ0kwTTFRMEZGT1R0Y2JpQWdMUzF0WkMxcGJtUnBaMjh0TWpBd09pQWpPVVpCT0VSQk8xeHVJQ0F0TFcxa0xXbHVaR2xuYnkwek1EQTZJQ00zT1RnMlEwSTdYRzRnSUMwdGJXUXRhVzVrYVdkdkxUUXdNRG9nSXpWRE5rSkRNRHRjYmlBZ0xTMXRaQzFwYm1ScFoyOHROVEF3T2lBak0wWTFNVUkxTzF4dUlDQXRMVzFrTFdsdVpHbG5ieTAyTURBNklDTXpPVFE1UVVJN1hHNGdJQzB0YldRdGFXNWthV2R2TFRjd01Eb2dJek13TTBZNVJqdGNiaUFnTFMxdFpDMXBibVJwWjI4dE9EQXdPaUFqTWpnek5Ua3pPMXh1SUNBdExXMWtMV2x1WkdsbmJ5MDVNREE2SUNNeFFUSXpOMFU3WEc0Z0lDMHRiV1F0YVc1a2FXZHZMVUV4TURBNklDTTRRemxGUmtZN1hHNGdJQzB0YldRdGFXNWthV2R2TFVFeU1EQTZJQ00xTXpaRVJrVTdYRzRnSUMwdGJXUXRhVzVrYVdkdkxVRTBNREE2SUNNelJEVkJSa1U3WEc0Z0lDMHRiV1F0YVc1a2FXZHZMVUUzTURBNklDTXpNRFJHUmtVN1hHNWNiaUFnTFMxdFpDMWliSFZsTFRVd09pQWpSVE5HTWtaRU8xeHVJQ0F0TFcxa0xXSnNkV1V0TVRBd09pQWpRa0pFUlVaQ08xeHVJQ0F0TFcxa0xXSnNkV1V0TWpBd09pQWpPVEJEUVVZNU8xeHVJQ0F0TFcxa0xXSnNkV1V0TXpBd09pQWpOalJDTlVZMk8xeHVJQ0F0TFcxa0xXSnNkV1V0TkRBd09pQWpOREpCTlVZMU8xeHVJQ0F0TFcxa0xXSnNkV1V0TlRBd09pQWpNakU1TmtZek8xeHVJQ0F0TFcxa0xXSnNkV1V0TmpBd09pQWpNVVU0T0VVMU8xeHVJQ0F0TFcxa0xXSnNkV1V0TnpBd09pQWpNVGszTmtReU8xeHVJQ0F0TFcxa0xXSnNkV1V0T0RBd09pQWpNVFUyTlVNd08xeHVJQ0F0TFcxa0xXSnNkV1V0T1RBd09pQWpNRVEwTjBFeE8xeHVJQ0F0TFcxa0xXSnNkV1V0UVRFd01Eb2dJemd5UWpGR1JqdGNiaUFnTFMxdFpDMWliSFZsTFVFeU1EQTZJQ00wTkRoQlJrWTdYRzRnSUMwdGJXUXRZbXgxWlMxQk5EQXdPaUFqTWprM09VWkdPMXh1SUNBdExXMWtMV0pzZFdVdFFUY3dNRG9nSXpJNU5qSkdSanRjYmx4dUlDQXRMVzFrTFd4cFoyaDBMV0pzZFdVdE5UQTZJQ05GTVVZMVJrVTdYRzRnSUMwdGJXUXRiR2xuYUhRdFlteDFaUzB4TURBNklDTkNNMFUxUmtNN1hHNGdJQzB0YldRdGJHbG5hSFF0WW14MVpTMHlNREE2SUNNNE1VUTBSa0U3WEc0Z0lDMHRiV1F0YkdsbmFIUXRZbXgxWlMwek1EQTZJQ00wUmtNelJqYzdYRzRnSUMwdGJXUXRiR2xuYUhRdFlteDFaUzAwTURBNklDTXlPVUkyUmpZN1hHNGdJQzB0YldRdGJHbG5hSFF0WW14MVpTMDFNREE2SUNNd00wRTVSalE3WEc0Z0lDMHRiV1F0YkdsbmFIUXRZbXgxWlMwMk1EQTZJQ013TXpsQ1JUVTdYRzRnSUMwdGJXUXRiR2xuYUhRdFlteDFaUzAzTURBNklDTXdNamc0UkRFN1hHNGdJQzB0YldRdGJHbG5hSFF0WW14MVpTMDRNREE2SUNNd01qYzNRa1E3WEc0Z0lDMHRiV1F0YkdsbmFIUXRZbXgxWlMwNU1EQTZJQ013TVRVM09VSTdYRzRnSUMwdGJXUXRiR2xuYUhRdFlteDFaUzFCTVRBd09pQWpPREJFT0VaR08xeHVJQ0F0TFcxa0xXeHBaMmgwTFdKc2RXVXRRVEl3TURvZ0l6UXdRelJHUmp0Y2JpQWdMUzF0WkMxc2FXZG9kQzFpYkhWbExVRTBNREE2SUNNd01FSXdSa1k3WEc0Z0lDMHRiV1F0YkdsbmFIUXRZbXgxWlMxQk56QXdPaUFqTURBNU1VVkJPMXh1WEc0Z0lDMHRiV1F0WTNsaGJpMDFNRG9nSTBVd1JqZEdRVHRjYmlBZ0xTMXRaQzFqZVdGdUxURXdNRG9nSTBJeVJVSkdNanRjYmlBZ0xTMXRaQzFqZVdGdUxUSXdNRG9nSXpnd1JFVkZRVHRjYmlBZ0xTMXRaQzFqZVdGdUxUTXdNRG9nSXpSRVJEQkZNVHRjYmlBZ0xTMXRaQzFqZVdGdUxUUXdNRG9nSXpJMlF6WkVRVHRjYmlBZ0xTMXRaQzFqZVdGdUxUVXdNRG9nSXpBd1FrTkVORHRjYmlBZ0xTMXRaQzFqZVdGdUxUWXdNRG9nSXpBd1FVTkRNVHRjYmlBZ0xTMXRaQzFqZVdGdUxUY3dNRG9nSXpBd09UZEJOenRjYmlBZ0xTMXRaQzFqZVdGdUxUZ3dNRG9nSXpBd09ETTRSanRjYmlBZ0xTMXRaQzFqZVdGdUxUa3dNRG9nSXpBd05qQTJORHRjYmlBZ0xTMXRaQzFqZVdGdUxVRXhNREE2SUNNNE5FWkdSa1k3WEc0Z0lDMHRiV1F0WTNsaGJpMUJNakF3T2lBak1UaEdSa1pHTzF4dUlDQXRMVzFrTFdONVlXNHRRVFF3TURvZ0l6QXdSVFZHUmp0Y2JpQWdMUzF0WkMxamVXRnVMVUUzTURBNklDTXdNRUk0UkRRN1hHNWNiaUFnTFMxdFpDMTBaV0ZzTFRVd09pQWpSVEJHTWtZeE8xeHVJQ0F0TFcxa0xYUmxZV3d0TVRBd09pQWpRakpFUmtSQ08xeHVJQ0F0TFcxa0xYUmxZV3d0TWpBd09pQWpPREJEUWtNME8xeHVJQ0F0TFcxa0xYUmxZV3d0TXpBd09pQWpORVJDTmtGRE8xeHVJQ0F0TFcxa0xYUmxZV3d0TkRBd09pQWpNalpCTmpsQk8xeHVJQ0F0TFcxa0xYUmxZV3d0TlRBd09pQWpNREE1TmpnNE8xeHVJQ0F0TFcxa0xYUmxZV3d0TmpBd09pQWpNREE0T1RkQ08xeHVJQ0F0TFcxa0xYUmxZV3d0TnpBd09pQWpNREEzT1RaQ08xeHVJQ0F0TFcxa0xYUmxZV3d0T0RBd09pQWpNREEyT1RWRE8xeHVJQ0F0TFcxa0xYUmxZV3d0T1RBd09pQWpNREEwUkRRd08xeHVJQ0F0TFcxa0xYUmxZV3d0UVRFd01Eb2dJMEUzUmtaRlFqdGNiaUFnTFMxdFpDMTBaV0ZzTFVFeU1EQTZJQ00yTkVaR1JFRTdYRzRnSUMwdGJXUXRkR1ZoYkMxQk5EQXdPaUFqTVVSRk9VSTJPMXh1SUNBdExXMWtMWFJsWVd3dFFUY3dNRG9nSXpBd1FrWkJOVHRjYmx4dUlDQXRMVzFrTFdkeVpXVnVMVFV3T2lBalJUaEdOVVU1TzF4dUlDQXRMVzFrTFdkeVpXVnVMVEV3TURvZ0kwTTRSVFpET1R0Y2JpQWdMUzF0WkMxbmNtVmxiaTB5TURBNklDTkJOVVEyUVRjN1hHNGdJQzB0YldRdFozSmxaVzR0TXpBd09pQWpPREZETnpnME8xeHVJQ0F0TFcxa0xXZHlaV1Z1TFRRd01Eb2dJelkyUWtJMlFUdGNiaUFnTFMxdFpDMW5jbVZsYmkwMU1EQTZJQ00wUTBGR05UQTdYRzRnSUMwdGJXUXRaM0psWlc0dE5qQXdPaUFqTkROQk1EUTNPMXh1SUNBdExXMWtMV2R5WldWdUxUY3dNRG9nSXpNNE9FVXpRenRjYmlBZ0xTMXRaQzFuY21WbGJpMDRNREE2SUNNeVJUZEVNekk3WEc0Z0lDMHRiV1F0WjNKbFpXNHRPVEF3T2lBak1VSTFSVEl3TzF4dUlDQXRMVzFrTFdkeVpXVnVMVUV4TURBNklDTkNPVVkyUTBFN1hHNGdJQzB0YldRdFozSmxaVzR0UVRJd01Eb2dJelk1UmpCQlJUdGNiaUFnTFMxdFpDMW5jbVZsYmkxQk5EQXdPaUFqTURCRk5qYzJPMXh1SUNBdExXMWtMV2R5WldWdUxVRTNNREE2SUNNd01FTTROVE03WEc1Y2JpQWdMUzF0WkMxc2FXZG9kQzFuY21WbGJpMDFNRG9nSTBZeFJqaEZPVHRjYmlBZ0xTMXRaQzFzYVdkb2RDMW5jbVZsYmkweE1EQTZJQ05FUTBWRVF6ZzdYRzRnSUMwdGJXUXRiR2xuYUhRdFozSmxaVzR0TWpBd09pQWpRelZGTVVFMU8xeHVJQ0F0TFcxa0xXeHBaMmgwTFdkeVpXVnVMVE13TURvZ0kwRkZSRFU0TVR0Y2JpQWdMUzF0WkMxc2FXZG9kQzFuY21WbGJpMDBNREE2SUNNNVEwTkROalU3WEc0Z0lDMHRiV1F0YkdsbmFIUXRaM0psWlc0dE5UQXdPaUFqT0VKRE16UkJPMXh1SUNBdExXMWtMV3hwWjJoMExXZHlaV1Z1TFRZd01Eb2dJemREUWpNME1qdGNiaUFnTFMxdFpDMXNhV2RvZEMxbmNtVmxiaTAzTURBNklDTTJPRGxHTXpnN1hHNGdJQzB0YldRdGJHbG5hSFF0WjNKbFpXNHRPREF3T2lBak5UVTRRakpHTzF4dUlDQXRMVzFrTFd4cFoyaDBMV2R5WldWdUxUa3dNRG9nSXpNek5qa3hSVHRjYmlBZ0xTMXRaQzFzYVdkb2RDMW5jbVZsYmkxQk1UQXdPaUFqUTBOR1Jqa3dPMXh1SUNBdExXMWtMV3hwWjJoMExXZHlaV1Z1TFVFeU1EQTZJQ05DTWtaR05UazdYRzRnSUMwdGJXUXRiR2xuYUhRdFozSmxaVzR0UVRRd01Eb2dJemMyUmtZd016dGNiaUFnTFMxdFpDMXNhV2RvZEMxbmNtVmxiaTFCTnpBd09pQWpOalJFUkRFM08xeHVYRzRnSUMwdGJXUXRiR2x0WlMwMU1Eb2dJMFk1UmtKRk56dGNiaUFnTFMxdFpDMXNhVzFsTFRFd01Eb2dJMFl3UmpSRE16dGNiaUFnTFMxdFpDMXNhVzFsTFRJd01Eb2dJMFUyUlVVNVF6dGNiaUFnTFMxdFpDMXNhVzFsTFRNd01Eb2dJMFJEUlRjM05UdGNiaUFnTFMxdFpDMXNhVzFsTFRRd01Eb2dJMFEwUlRFMU56dGNiaUFnTFMxdFpDMXNhVzFsTFRVd01Eb2dJME5FUkVNek9UdGNiaUFnTFMxdFpDMXNhVzFsTFRZd01Eb2dJME13UTBFek16dGNiaUFnTFMxdFpDMXNhVzFsTFRjd01Eb2dJMEZHUWpReVFqdGNiaUFnTFMxdFpDMXNhVzFsTFRnd01Eb2dJemxGT1VReU5EdGNiaUFnTFMxdFpDMXNhVzFsTFRrd01Eb2dJemd5TnpjeE56dGNiaUFnTFMxdFpDMXNhVzFsTFVFeE1EQTZJQ05HTkVaR09ERTdYRzRnSUMwdGJXUXRiR2x0WlMxQk1qQXdPaUFqUlVWR1JqUXhPMXh1SUNBdExXMWtMV3hwYldVdFFUUXdNRG9nSTBNMlJrWXdNRHRjYmlBZ0xTMXRaQzFzYVcxbExVRTNNREE2SUNOQlJVVkJNREE3WEc1Y2JpQWdMUzF0WkMxNVpXeHNiM2N0TlRBNklDTkdSa1pFUlRjN1hHNGdJQzB0YldRdGVXVnNiRzkzTFRFd01Eb2dJMFpHUmpsRE5EdGNiaUFnTFMxdFpDMTVaV3hzYjNjdE1qQXdPaUFqUmtaR05UbEVPMXh1SUNBdExXMWtMWGxsYkd4dmR5MHpNREE2SUNOR1JrWXhOelk3WEc0Z0lDMHRiV1F0ZVdWc2JHOTNMVFF3TURvZ0kwWkdSVVUxT0R0Y2JpQWdMUzF0WkMxNVpXeHNiM2N0TlRBd09pQWpSa1pGUWpOQ08xeHVJQ0F0TFcxa0xYbGxiR3h2ZHkwMk1EQTZJQ05HUkVRNE16VTdYRzRnSUMwdGJXUXRlV1ZzYkc5M0xUY3dNRG9nSTBaQ1F6QXlSRHRjYmlBZ0xTMXRaQzE1Wld4c2IzY3RPREF3T2lBalJqbEJPREkxTzF4dUlDQXRMVzFrTFhsbGJHeHZkeTA1TURBNklDTkdOVGRHTVRjN1hHNGdJQzB0YldRdGVXVnNiRzkzTFVFeE1EQTZJQ05HUmtaR09FUTdYRzRnSUMwdGJXUXRlV1ZzYkc5M0xVRXlNREE2SUNOR1JrWkdNREE3WEc0Z0lDMHRiV1F0ZVdWc2JHOTNMVUUwTURBNklDTkdSa1ZCTURBN1hHNGdJQzB0YldRdGVXVnNiRzkzTFVFM01EQTZJQ05HUmtRMk1EQTdYRzVjYmlBZ0xTMXRaQzFoYldKbGNpMDFNRG9nSTBaR1JqaEZNVHRjYmlBZ0xTMXRaQzFoYldKbGNpMHhNREE2SUNOR1JrVkRRak03WEc0Z0lDMHRiV1F0WVcxaVpYSXRNakF3T2lBalJrWkZNRGd5TzF4dUlDQXRMVzFrTFdGdFltVnlMVE13TURvZ0kwWkdSRFUwUmp0Y2JpQWdMUzF0WkMxaGJXSmxjaTAwTURBNklDTkdSa05CTWpnN1hHNGdJQzB0YldRdFlXMWlaWEl0TlRBd09pQWpSa1pETVRBM08xeHVJQ0F0TFcxa0xXRnRZbVZ5TFRZd01Eb2dJMFpHUWpNd01EdGNiaUFnTFMxdFpDMWhiV0psY2kwM01EQTZJQ05HUmtFd01EQTdYRzRnSUMwdGJXUXRZVzFpWlhJdE9EQXdPaUFqUmtZNFJqQXdPMXh1SUNBdExXMWtMV0Z0WW1WeUxUa3dNRG9nSTBaR05rWXdNRHRjYmlBZ0xTMXRaQzFoYldKbGNpMUJNVEF3T2lBalJrWkZOVGRHTzF4dUlDQXRMVzFrTFdGdFltVnlMVUV5TURBNklDTkdSa1EzTkRBN1hHNGdJQzB0YldRdFlXMWlaWEl0UVRRd01Eb2dJMFpHUXpRd01EdGNiaUFnTFMxdFpDMWhiV0psY2kxQk56QXdPaUFqUmtaQlFqQXdPMXh1WEc0Z0lDMHRiV1F0YjNKaGJtZGxMVFV3T2lBalJrWkdNMFV3TzF4dUlDQXRMVzFrTFc5eVlXNW5aUzB4TURBNklDTkdSa1V3UWpJN1hHNGdJQzB0YldRdGIzSmhibWRsTFRJd01Eb2dJMFpHUTBNNE1EdGNiaUFnTFMxdFpDMXZjbUZ1WjJVdE16QXdPaUFqUmtaQ056UkVPMXh1SUNBdExXMWtMVzl5WVc1blpTMDBNREE2SUNOR1JrRTNNalk3WEc0Z0lDMHRiV1F0YjNKaGJtZGxMVFV3TURvZ0kwWkdPVGd3TUR0Y2JpQWdMUzF0WkMxdmNtRnVaMlV0TmpBd09pQWpSa0k0UXpBd08xeHVJQ0F0TFcxa0xXOXlZVzVuWlMwM01EQTZJQ05HTlRkRE1EQTdYRzRnSUMwdGJXUXRiM0poYm1kbExUZ3dNRG9nSTBWR05rTXdNRHRjYmlBZ0xTMXRaQzF2Y21GdVoyVXRPVEF3T2lBalJUWTFNVEF3TzF4dUlDQXRMVzFrTFc5eVlXNW5aUzFCTVRBd09pQWpSa1pFTVRnd08xeHVJQ0F0TFcxa0xXOXlZVzVuWlMxQk1qQXdPaUFqUmtaQlFqUXdPMXh1SUNBdExXMWtMVzl5WVc1blpTMUJOREF3T2lBalJrWTVNVEF3TzF4dUlDQXRMVzFrTFc5eVlXNW5aUzFCTnpBd09pQWpSa1kyUkRBd08xeHVYRzRnSUMwdGJXUXRaR1ZsY0MxdmNtRnVaMlV0TlRBNklDTkdRa1U1UlRjN1hHNGdJQzB0YldRdFpHVmxjQzF2Y21GdVoyVXRNVEF3T2lBalJrWkRRMEpETzF4dUlDQXRMVzFrTFdSbFpYQXRiM0poYm1kbExUSXdNRG9nSTBaR1FVSTVNVHRjYmlBZ0xTMXRaQzFrWldWd0xXOXlZVzVuWlMwek1EQTZJQ05HUmpoQk5qVTdYRzRnSUMwdGJXUXRaR1ZsY0MxdmNtRnVaMlV0TkRBd09pQWpSa1kzTURRek8xeHVJQ0F0TFcxa0xXUmxaWEF0YjNKaGJtZGxMVFV3TURvZ0kwWkdOVGN5TWp0Y2JpQWdMUzF0WkMxa1pXVndMVzl5WVc1blpTMDJNREE2SUNOR05EVXhNVVU3WEc0Z0lDMHRiV1F0WkdWbGNDMXZjbUZ1WjJVdE56QXdPaUFqUlRZMFFURTVPMXh1SUNBdExXMWtMV1JsWlhBdGIzSmhibWRsTFRnd01Eb2dJMFE0TkRNeE5UdGNiaUFnTFMxdFpDMWtaV1Z3TFc5eVlXNW5aUzA1TURBNklDTkNSak0yTUVNN1hHNGdJQzB0YldRdFpHVmxjQzF2Y21GdVoyVXRRVEV3TURvZ0kwWkdPVVU0TUR0Y2JpQWdMUzF0WkMxa1pXVndMVzl5WVc1blpTMUJNakF3T2lBalJrWTJSVFF3TzF4dUlDQXRMVzFrTFdSbFpYQXRiM0poYm1kbExVRTBNREE2SUNOR1JqTkVNREE3WEc0Z0lDMHRiV1F0WkdWbGNDMXZjbUZ1WjJVdFFUY3dNRG9nSTBSRU1rTXdNRHRjYmx4dUlDQXRMVzFrTFdKeWIzZHVMVFV3T2lBalJVWkZRa1U1TzF4dUlDQXRMVzFrTFdKeWIzZHVMVEV3TURvZ0kwUTNRME5ET0R0Y2JpQWdMUzF0WkMxaWNtOTNiaTB5TURBNklDTkNRMEZCUVRRN1hHNGdJQzB0YldRdFluSnZkMjR0TXpBd09pQWpRVEU0T0RkR08xeHVJQ0F0TFcxa0xXSnliM2R1TFRRd01Eb2dJemhFTmtVMk16dGNiaUFnTFMxdFpDMWljbTkzYmkwMU1EQTZJQ00zT1RVMU5EZzdYRzRnSUMwdGJXUXRZbkp2ZDI0dE5qQXdPaUFqTmtRMFF6UXhPMXh1SUNBdExXMWtMV0p5YjNkdUxUY3dNRG9nSXpWRU5EQXpOenRjYmlBZ0xTMXRaQzFpY205M2JpMDRNREE2SUNNMFJUTTBNa1U3WEc0Z0lDMHRiV1F0WW5KdmQyNHRPVEF3T2lBak0wVXlOekl6TzF4dVhHNGdJQzB0YldRdFozSmxlUzAxTURvZ0kwWkJSa0ZHUVR0Y2JpQWdMUzF0WkMxbmNtVjVMVEV3TURvZ0kwWTFSalZHTlR0Y2JpQWdMUzF0WkMxbmNtVjVMVEl3TURvZ0kwVkZSVVZGUlR0Y2JpQWdMUzF0WkMxbmNtVjVMVE13TURvZ0kwVXdSVEJGTUR0Y2JpQWdMUzF0WkMxbmNtVjVMVFF3TURvZ0kwSkVRa1JDUkR0Y2JpQWdMUzF0WkMxbmNtVjVMVFV3TURvZ0l6bEZPVVU1UlR0Y2JpQWdMUzF0WkMxbmNtVjVMVFl3TURvZ0l6YzFOelUzTlR0Y2JpQWdMUzF0WkMxbmNtVjVMVGN3TURvZ0l6WXhOakUyTVR0Y2JpQWdMUzF0WkMxbmNtVjVMVGd3TURvZ0l6UXlOREkwTWp0Y2JpQWdMUzF0WkMxbmNtVjVMVGt3TURvZ0l6SXhNakV5TVR0Y2JseHVJQ0F0TFcxa0xXSnNkV1V0WjNKbGVTMDFNRG9nSTBWRFJVWkdNVHRjYmlBZ0xTMXRaQzFpYkhWbExXZHlaWGt0TVRBd09pQWpRMFpFT0VSRE8xeHVJQ0F0TFcxa0xXSnNkV1V0WjNKbGVTMHlNREE2SUNOQ01FSkZRelU3WEc0Z0lDMHRiV1F0WW14MVpTMW5jbVY1TFRNd01Eb2dJemt3UVRSQlJUdGNiaUFnTFMxdFpDMWliSFZsTFdkeVpYa3ROREF3T2lBak56ZzVNRGxETzF4dUlDQXRMVzFrTFdKc2RXVXRaM0psZVMwMU1EQTZJQ00yTURkRU9FSTdYRzRnSUMwdGJXUXRZbXgxWlMxbmNtVjVMVFl3TURvZ0l6VTBOa1UzUVR0Y2JpQWdMUzF0WkMxaWJIVmxMV2R5WlhrdE56QXdPaUFqTkRVMVFUWTBPMXh1SUNBdExXMWtMV0pzZFdVdFozSmxlUzA0TURBNklDTXpOelEzTkVZN1hHNGdJQzB0YldRdFlteDFaUzFuY21WNUxUa3dNRG9nSXpJMk16SXpPRHRjYm4waUxDSXZLaUJEYjNCNWNtbG5hSFFnS0dNcElFcDFjSGwwWlhJZ1JHVjJaV3h2Y0cxbGJuUWdWR1ZoYlM1Y2JpQXFJRVJwYzNSeWFXSjFkR1ZrSUhWdVpHVnlJSFJvWlNCMFpYSnRjeUJ2WmlCMGFHVWdUVzlrYVdacFpXUWdRbE5FSUV4cFkyVnVjMlV1WEc0Z0tpOWNibHh1THlwY2JpQXFJRmRsSUdGemMzVnRaU0IwYUdGMElIUm9aU0JEVTFNZ2RtRnlhV0ZpYkdWeklHbHVYRzRnS2lCb2RIUndjem92TDJkcGRHaDFZaTVqYjIwdmFuVndlWFJsY214aFlpOXFkWEI1ZEdWeWJHRmlMMkpzYjJJdmJXRnpkR1Z5TDNOeVl5OWtaV1poZFd4MExYUm9aVzFsTDNaaGNtbGhZbXhsY3k1amMzTmNiaUFxSUdoaGRtVWdZbVZsYmlCa1pXWnBibVZrTGx4dUlDb3ZYRzVjYmtCcGJYQnZjblFnWENJdUwzQm9iM053YUc5eUxtTnpjMXdpTzF4dVhHNDZjbTl2ZENCN1hHNGdJQ0FnTFMxcWNDMTNhV1JuWlhSekxXTnZiRzl5T2lCMllYSW9MUzFxY0MxamIyNTBaVzUwTFdadmJuUXRZMjlzYjNJeEtUdGNiaUFnSUNBdExXcHdMWGRwWkdkbGRITXRiR0ZpWld3dFkyOXNiM0k2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10WTI5c2IzSXBPMXh1SUNBZ0lDMHRhbkF0ZDJsa1oyVjBjeTF5WldGa2IzVjBMV052Ykc5eU9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXTnZiRzl5S1R0Y2JpQWdJQ0F0TFdwd0xYZHBaR2RsZEhNdFptOXVkQzF6YVhwbE9pQjJZWElvTFMxcWNDMTFhUzFtYjI1MExYTnBlbVV4S1R0Y2JpQWdJQ0F0TFdwd0xYZHBaR2RsZEhNdGJXRnlaMmx1T2lBeWNIZzdYRzRnSUNBZ0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMW9aV2xuYUhRNklESTRjSGc3WEc0Z0lDQWdMUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzEzYVdSMGFEb2dNekF3Y0hnN1hHNGdJQ0FnTFMxcWNDMTNhV1JuWlhSekxXbHViR2x1WlMxM2FXUjBhQzF6YUc5eWREb2dZMkZzWXloMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzEzYVdSMGFDa2dMeUF5SUMwZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxdFlYSm5hVzRwS1R0Y2JpQWdJQ0F0TFdwd0xYZHBaR2RsZEhNdGFXNXNhVzVsTFhkcFpIUm9MWFJwYm5rNklHTmhiR01vZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRkMmxrZEdndGMyaHZjblFwSUM4Z01pQXRJSFpoY2lndExXcHdMWGRwWkdkbGRITXRiV0Z5WjJsdUtTazdYRzRnSUNBZ0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMXRZWEpuYVc0NklEUndlRHNnTHlvZ2JXRnlaMmx1SUdKbGRIZGxaVzRnYVc1c2FXNWxJR1ZzWlcxbGJuUnpJQ292WEc0Z0lDQWdMUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzFzWVdKbGJDMTNhV1IwYURvZ09EQndlRHRjYmlBZ0lDQXRMV3B3TFhkcFpHZGxkSE10WW05eVpHVnlMWGRwWkhSb09pQjJZWElvTFMxcWNDMWliM0prWlhJdGQybGtkR2dwTzF4dUlDQWdJQzB0YW5BdGQybGtaMlYwY3kxMlpYSjBhV05oYkMxb1pXbG5hSFE2SURJd01IQjRPMXh1SUNBZ0lDMHRhbkF0ZDJsa1oyVjBjeTFvYjNKcGVtOXVkR0ZzTFhSaFlpMW9aV2xuYUhRNklESTBjSGc3WEc0Z0lDQWdMUzFxY0MxM2FXUm5aWFJ6TFdodmNtbDZiMjUwWVd3dGRHRmlMWGRwWkhSb09pQXhORFJ3ZUR0Y2JpQWdJQ0F0TFdwd0xYZHBaR2RsZEhNdGFHOXlhWHB2Ym5SaGJDMTBZV0l0ZEc5d0xXSnZjbVJsY2pvZ01uQjRPMXh1SUNBZ0lDMHRhbkF0ZDJsa1oyVjBjeTF3Y205bmNtVnpjeTEwYUdsamEyNWxjM002SURJd2NIZzdYRzRnSUNBZ0xTMXFjQzEzYVdSblpYUnpMV052Ym5SaGFXNWxjaTF3WVdSa2FXNW5PaUF4TlhCNE8xeHVJQ0FnSUMwdGFuQXRkMmxrWjJWMGN5MXBibkIxZEMxd1lXUmthVzVuT2lBMGNIZzdYRzRnSUNBZ0xTMXFjQzEzYVdSblpYUnpMWEpoWkdsdkxXbDBaVzB0YUdWcFoyaDBMV0ZrYW5WemRHMWxiblE2SURod2VEdGNiaUFnSUNBdExXcHdMWGRwWkdkbGRITXRjbUZrYVc4dGFYUmxiUzFvWldsbmFIUTZJR05oYkdNb2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0YUdWcFoyaDBLU0F0SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10Y21Ga2FXOHRhWFJsYlMxb1pXbG5hSFF0WVdScWRYTjBiV1Z1ZENrcE8xeHVJQ0FnSUMwdGFuQXRkMmxrWjJWMGN5MXpiR2xrWlhJdGRISmhZMnN0ZEdocFkydHVaWE56T2lBMGNIZzdYRzRnSUNBZ0xTMXFjQzEzYVdSblpYUnpMWE5zYVdSbGNpMWliM0prWlhJdGQybGtkR2c2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10WW05eVpHVnlMWGRwWkhSb0tUdGNiaUFnSUNBdExXcHdMWGRwWkdkbGRITXRjMnhwWkdWeUxXaGhibVJzWlMxemFYcGxPaUF4Tm5CNE8xeHVJQ0FnSUMwdGFuQXRkMmxrWjJWMGN5MXpiR2xrWlhJdGFHRnVaR3hsTFdKdmNtUmxjaTFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRZbTl5WkdWeUxXTnZiRzl5TVNrN1hHNGdJQ0FnTFMxcWNDMTNhV1JuWlhSekxYTnNhV1JsY2kxb1lXNWtiR1V0WW1GamEyZHliM1Z1WkMxamIyeHZjam9nZG1GeUtDMHRhbkF0YkdGNWIzVjBMV052Ykc5eU1TazdYRzRnSUNBZ0xTMXFjQzEzYVdSblpYUnpMWE5zYVdSbGNpMWhZM1JwZG1VdGFHRnVaR3hsTFdOdmJHOXlPaUIyWVhJb0xTMXFjQzFpY21GdVpDMWpiMnh2Y2pFcE8xeHVJQ0FnSUMwdGFuQXRkMmxrWjJWMGN5MXRaVzUxTFdsMFpXMHRhR1ZwWjJoME9pQXlOSEI0TzF4dUlDQWdJQzB0YW5BdGQybGtaMlYwY3kxa2NtOXdaRzkzYmkxaGNuSnZkem9nZFhKc0tGd2laR0YwWVRwcGJXRm5aUzl6ZG1jcmVHMXNPMkpoYzJVMk5DeFFSRGswWWxkM1oyUnRWbmxqTW14Mlltb3dhVTFUTkhkSmFVSnNZbTFPZGxwSGJIVmFlakJwWkZoU2JVeFVaMmxRZWpSTFVFTkZkRXhUUWtoYVZ6VnNZMjFHTUdJelNUWkpSVVpyWWpKS2JFbEZiSE5pU0ZaNlpFaEthR1JIT1hsSlJFVTFUR3BKZFUxVGQyZFZNVnBJU1VWV05HTkhPWGxrUTBKUllraFdia3hWYkhWSlF6Um5WVEZhU0VsR1dteGpiazV3WWpJME5rbEVXWFZOUkVGblVXNVdjR0pIVVdkTlEydG5TVU13ZEZCbmJ6aGpNMXB1U1VoYWJHTnVUbkJpTWpRNVNXcEZkVTFUU1dkaFYxRTVTV3Q0YUdWWFZubFlla1ZwU1Vob2RHSkhOWHBRVTBwdlpFaFNkMDlwT0haa00yUXpURzVqZWt4dE9YbGFlVGg1VFVSQmQwd3pUakphZVVsblpVY3hjMkp1VFRabFIzaHdZbTF6T1VsdGFEQmtTRUUyVEhrNU0yUXpZM1ZrZWsxMVlqTktia3g2UlRWUFZHdDJaVWQ0Y0dKdGMybEpTR2M1U1dwQ2QyVkRTV2RsVkRCcFRVaENORWxuYjBwSlNGcHdXbGhrUTJJelp6bEpha0ZuVFVOQmVFOURRWGhQUTBsbll6TlNOV0pIVlRsSmJWWjFXVmRLYzFwVE1XbFpWMDV5V2pOS2RtUlhOV3RQYlRWc1pIbEJkMGxFUVdkTlZHZG5UVlJuTjBscFFqUmlWM2MyWXpOQ2FGa3lWVGxKYmtKNVdsaE9iR051V214SmFqUkxVRWhPTUdWWGVHeEpTRkkxWTBkVk9VbHVVbXhsU0ZGMldUTk9la2xxTkV0RFV6VjZaRVJDTjFwdGJITmlSSEIxWWpJMWJFOHpNRXRRUXpsNlpFaHNjMXBVTkV0UVNFSm9aRWRuWjFwRU1HbFVWRlYxVFdsM01VeHFiRTFQVTNjMVRHcGtjMDE1TkRSTVZFMTFUMGQzZUV4cVNYTk5VelI1WWtNd01FeHFhM05PVjNkMFRrTTBOVXhVVmsxT1V6UjVURVJWZFU5WWIybE1lalJMVUVoQ2FHUkhaMmRaTW5ob1l6Tk5PVWx1VGpCTlEwbG5Xa1F3YVZSVVFYUk5RelF5WVVSRk5HUnFSVFJUUkVKWFRGUkJkVTV1YjJsTWVqUkxVRU01ZW1SdFl5dERaMXdpS1R0Y2JpQWdJQ0F0TFdwd0xYZHBaR2RsZEhNdGFXNXdkWFF0WTI5c2IzSTZJSFpoY2lndExXcHdMWFZwTFdadmJuUXRZMjlzYjNJeEtUdGNiaUFnSUNBdExXcHdMWGRwWkdkbGRITXRhVzV3ZFhRdFltRmphMmR5YjNWdVpDMWpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGJHRjViM1YwTFdOdmJHOXlNU2s3WEc0Z0lDQWdMUzFxY0MxM2FXUm5aWFJ6TFdsdWNIVjBMV0p2Y21SbGNpMWpiMnh2Y2pvZ2RtRnlLQzB0YW5BdFltOXlaR1Z5TFdOdmJHOXlNU2s3WEc0Z0lDQWdMUzFxY0MxM2FXUm5aWFJ6TFdsdWNIVjBMV1p2WTNWekxXSnZjbVJsY2kxamIyeHZjam9nZG1GeUtDMHRhbkF0WW5KaGJtUXRZMjlzYjNJeUtUdGNiaUFnSUNBdExXcHdMWGRwWkdkbGRITXRhVzV3ZFhRdFltOXlaR1Z5TFhkcFpIUm9PaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV0p2Y21SbGNpMTNhV1IwYUNrN1hHNGdJQ0FnTFMxcWNDMTNhV1JuWlhSekxXUnBjMkZpYkdWa0xXOXdZV05wZEhrNklEQXVOanRjYmx4dUlDQWdJQzhxSUVaeWIyMGdUV0YwWlhKcFlXd2dSR1Z6YVdkdUlFeHBkR1VnS2k5Y2JpQWdJQ0F0TFcxa0xYTm9ZV1J2ZHkxclpYa3RkVzFpY21FdGIzQmhZMmwwZVRvZ01DNHlPMXh1SUNBZ0lDMHRiV1F0YzJoaFpHOTNMV3RsZVMxd1pXNTFiV0p5WVMxdmNHRmphWFI1T2lBd0xqRTBPMXh1SUNBZ0lDMHRiV1F0YzJoaFpHOTNMV0Z0WW1sbGJuUXRjMmhoWkc5M0xXOXdZV05wZEhrNklEQXVNVEk3WEc1OVhHNWNiaTVxZFhCNWRHVnlMWGRwWkdkbGRITWdlMXh1SUNBZ0lHMWhjbWRwYmpvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxdFlYSm5hVzRwTzF4dUlDQWdJR0p2ZUMxemFYcHBibWM2SUdKdmNtUmxjaTFpYjNnN1hHNGdJQ0FnWTI5c2IzSTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRZMjlzYjNJcE8xeHVJQ0FnSUc5MlpYSm1iRzkzT2lCMmFYTnBZbXhsTzF4dWZWeHVYRzR1YW5Wd2VYUmxjaTEzYVdSblpYUnpMbXAxY0hsMFpYSXRkMmxrWjJWMGN5MWthWE5qYjI1dVpXTjBaV1E2T21KbFptOXlaU0I3WEc0Z0lDQWdiR2x1WlMxb1pXbG5hSFE2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMV2hsYVdkb2RDazdYRzRnSUNBZ2FHVnBaMmgwT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzFvWldsbmFIUXBPMXh1ZlZ4dVhHNHVhbkF0VDNWMGNIVjBMWEpsYzNWc2RDQStJQzVxZFhCNWRHVnlMWGRwWkdkbGRITWdlMXh1SUNBZ0lHMWhjbWRwYmkxc1pXWjBPaUF3TzF4dUlDQWdJRzFoY21kcGJpMXlhV2RvZERvZ01EdGNibjFjYmx4dUx5b2dkbUp2ZUNCaGJtUWdhR0p2ZUNBcUwxeHVYRzR1ZDJsa1oyVjBMV2x1YkdsdVpTMW9ZbTk0SUh0Y2JpQWdJQ0F2S2lCSWIzSnBlbTl1ZEdGc0lIZHBaR2RsZEhNZ0tpOWNiaUFnSUNCaWIzZ3RjMmw2YVc1bk9pQmliM0prWlhJdFltOTRPMXh1SUNBZ0lHUnBjM0JzWVhrNklHWnNaWGc3WEc0Z0lDQWdabXhsZUMxa2FYSmxZM1JwYjI0NklISnZkenRjYmlBZ0lDQmhiR2xuYmkxcGRHVnRjem9nWW1GelpXeHBibVU3WEc1OVhHNWNiaTUzYVdSblpYUXRhVzVzYVc1bExYWmliM2dnZTF4dUlDQWdJQzhxSUZabGNuUnBZMkZzSUZkcFpHZGxkSE1nS2k5Y2JpQWdJQ0JpYjNndGMybDZhVzVuT2lCaWIzSmtaWEl0WW05NE8xeHVJQ0FnSUdScGMzQnNZWGs2SUdac1pYZzdYRzRnSUNBZ1pteGxlQzFrYVhKbFkzUnBiMjQ2SUdOdmJIVnRianRjYmlBZ0lDQmhiR2xuYmkxcGRHVnRjem9nWTJWdWRHVnlPMXh1ZlZ4dVhHNHVkMmxrWjJWMExXSnZlQ0I3WEc0Z0lDQWdZbTk0TFhOcGVtbHVaem9nWW05eVpHVnlMV0p2ZUR0Y2JpQWdJQ0JrYVhOd2JHRjVPaUJtYkdWNE8xeHVJQ0FnSUcxaGNtZHBiam9nTUR0Y2JpQWdJQ0J2ZG1WeVpteHZkem9nWVhWMGJ6dGNibjFjYmx4dUxuZHBaR2RsZEMxbmNtbGtZbTk0SUh0Y2JpQWdJQ0JpYjNndGMybDZhVzVuT2lCaWIzSmtaWEl0WW05NE8xeHVJQ0FnSUdScGMzQnNZWGs2SUdkeWFXUTdYRzRnSUNBZ2JXRnlaMmx1T2lBd08xeHVJQ0FnSUc5MlpYSm1iRzkzT2lCaGRYUnZPMXh1ZlZ4dVhHNHVkMmxrWjJWMExXaGliM2dnZTF4dUlDQWdJR1pzWlhndFpHbHlaV04wYVc5dU9pQnliM2M3WEc1OVhHNWNiaTUzYVdSblpYUXRkbUp2ZUNCN1hHNGdJQ0FnWm14bGVDMWthWEpsWTNScGIyNDZJR052YkhWdGJqdGNibjFjYmx4dUx5b2dSMlZ1WlhKaGJDQkNkWFIwYjI0Z1UzUjViR2x1WnlBcUwxeHVYRzR1YW5Wd2VYUmxjaTFpZFhSMGIyNGdlMXh1SUNBZ0lIQmhaR1JwYm1jdGJHVm1kRG9nTVRCd2VEdGNiaUFnSUNCd1lXUmthVzVuTFhKcFoyaDBPaUF4TUhCNE8xeHVJQ0FnSUhCaFpHUnBibWN0ZEc5d09pQXdjSGc3WEc0Z0lDQWdjR0ZrWkdsdVp5MWliM1IwYjIwNklEQndlRHRjYmlBZ0lDQmthWE53YkdGNU9pQnBibXhwYm1VdFlteHZZMnM3WEc0Z0lDQWdkMmhwZEdVdGMzQmhZMlU2SUc1dmQzSmhjRHRjYmlBZ0lDQnZkbVZ5Wm14dmR6b2dhR2xrWkdWdU8xeHVJQ0FnSUhSbGVIUXRiM1psY21ac2IzYzZJR1ZzYkdsd2MybHpPMXh1SUNBZ0lIUmxlSFF0WVd4cFoyNDZJR05sYm5SbGNqdGNiaUFnSUNCbWIyNTBMWE5wZW1VNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdFptOXVkQzF6YVhwbEtUdGNiaUFnSUNCamRYSnpiM0k2SUhCdmFXNTBaWEk3WEc1Y2JpQWdJQ0JvWldsbmFIUTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRhVzVzYVc1bExXaGxhV2RvZENrN1hHNGdJQ0FnWW05eVpHVnlPaUF3Y0hnZ2MyOXNhV1E3WEc0Z0lDQWdiR2x1WlMxb1pXbG5hSFE2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMV2hsYVdkb2RDazdYRzRnSUNBZ1ltOTRMWE5vWVdSdmR6b2dibTl1WlR0Y2JseHVJQ0FnSUdOdmJHOXlPaUIyWVhJb0xTMXFjQzExYVMxbWIyNTBMV052Ykc5eU1TazdYRzRnSUNBZ1ltRmphMmR5YjNWdVpDMWpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGJHRjViM1YwTFdOdmJHOXlNaWs3WEc0Z0lDQWdZbTl5WkdWeUxXTnZiRzl5T2lCMllYSW9MUzFxY0MxaWIzSmtaWEl0WTI5c2IzSXlLVHRjYmlBZ0lDQmliM0prWlhJNklHNXZibVU3WEc1OVhHNWNiaTVxZFhCNWRHVnlMV0oxZEhSdmJpQnBMbVpoSUh0Y2JpQWdJQ0J0WVhKbmFXNHRjbWxuYUhRNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXNhVzVsTFcxaGNtZHBiaWs3WEc0Z0lDQWdjRzlwYm5SbGNpMWxkbVZ1ZEhNNklHNXZibVU3WEc1OVhHNWNiaTVxZFhCNWRHVnlMV0oxZEhSdmJqcGxiWEIwZVRwaVpXWnZjbVVnZTF4dUlDQWdJR052Ym5SbGJuUTZJRndpWEZ3eU1EQmlYQ0k3SUM4cUlIcGxjbTh0ZDJsa2RHZ2djM0JoWTJVZ0tpOWNibjFjYmx4dUxtcDFjSGwwWlhJdGQybGtaMlYwY3k1cWRYQjVkR1Z5TFdKMWRIUnZianBrYVhOaFlteGxaQ0I3WEc0Z0lDQWdiM0JoWTJsMGVUb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MWthWE5oWW14bFpDMXZjR0ZqYVhSNUtUdGNibjFjYmx4dUxtcDFjSGwwWlhJdFluVjBkRzl1SUdrdVptRXVZMlZ1ZEdWeUlIdGNiaUFnSUNCdFlYSm5hVzR0Y21sbmFIUTZJREE3WEc1OVhHNWNiaTVxZFhCNWRHVnlMV0oxZEhSdmJqcG9iM1psY2pwbGJtRmliR1ZrTENBdWFuVndlWFJsY2kxaWRYUjBiMjQ2Wm05amRYTTZaVzVoWW14bFpDQjdYRzRnSUNBZ0x5b2dUVVFnVEdsMFpTQXlaSEFnYzJoaFpHOTNJQ292WEc0Z0lDQWdZbTk0TFhOb1lXUnZkem9nTUNBeWNIZ2dNbkI0SURBZ2NtZGlZU2d3TENBd0xDQXdMQ0IyWVhJb0xTMXRaQzF6YUdGa2IzY3RhMlY1TFhCbGJuVnRZbkpoTFc5d1lXTnBkSGtwS1N4Y2JpQWdJQ0FnSUNBZ0lDQWdJQ0FnSUNBd0lETndlQ0F4Y0hnZ0xUSndlQ0J5WjJKaEtEQXNJREFzSURBc0lIWmhjaWd0TFcxa0xYTm9ZV1J2ZHkxclpYa3RkVzFpY21FdGIzQmhZMmwwZVNrcExGeHVJQ0FnSUNBZ0lDQWdJQ0FnSUNBZ0lEQWdNWEI0SURWd2VDQXdJSEpuWW1Fb01Dd2dNQ3dnTUN3Z2RtRnlLQzB0YldRdGMyaGhaRzkzTFdGdFltbGxiblF0YzJoaFpHOTNMVzl3WVdOcGRIa3BLVHRjYm4xY2JseHVMbXAxY0hsMFpYSXRZblYwZEc5dU9tRmpkR2wyWlN3Z0xtcDFjSGwwWlhJdFluVjBkRzl1TG0xdlpDMWhZM1JwZG1VZ2UxeHVJQ0FnSUM4cUlFMUVJRXhwZEdVZ05HUndJSE5vWVdSdmR5QXFMMXh1SUNBZ0lHSnZlQzF6YUdGa2IzYzZJREFnTkhCNElEVndlQ0F3SUhKblltRW9NQ3dnTUN3Z01Dd2dkbUZ5S0MwdGJXUXRjMmhoWkc5M0xXdGxlUzF3Wlc1MWJXSnlZUzF2Y0dGamFYUjVLU2tzWEc0Z0lDQWdJQ0FnSUNBZ0lDQWdJQ0FnTUNBeGNIZ2dNVEJ3ZUNBd0lISm5ZbUVvTUN3Z01Dd2dNQ3dnZG1GeUtDMHRiV1F0YzJoaFpHOTNMV0Z0WW1sbGJuUXRjMmhoWkc5M0xXOXdZV05wZEhrcEtTeGNiaUFnSUNBZ0lDQWdJQ0FnSUNBZ0lDQXdJREp3ZUNBMGNIZ2dMVEZ3ZUNCeVoySmhLREFzSURBc0lEQXNJSFpoY2lndExXMWtMWE5vWVdSdmR5MXJaWGt0ZFcxaWNtRXRiM0JoWTJsMGVTa3BPMXh1SUNBZ0lHTnZiRzl5T2lCMllYSW9MUzFxY0MxMWFTMW1iMjUwTFdOdmJHOXlNU2s3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaQzFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRiR0Y1YjNWMExXTnZiRzl5TXlrN1hHNTlYRzVjYmk1cWRYQjVkR1Z5TFdKMWRIUnZianBtYjJOMWN6cGxibUZpYkdWa0lIdGNiaUFnSUNCdmRYUnNhVzVsT2lBeGNIZ2djMjlzYVdRZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJuQjFkQzFtYjJOMWN5MWliM0prWlhJdFkyOXNiM0lwTzF4dWZWeHVYRzR2S2lCQ2RYUjBiMjRnWENKUWNtbHRZWEo1WENJZ1UzUjViR2x1WnlBcUwxeHVYRzR1YW5Wd2VYUmxjaTFpZFhSMGIyNHViVzlrTFhCeWFXMWhjbmtnZTF4dUlDQWdJR052Ykc5eU9pQjJZWElvTFMxcWNDMXBiblpsY25ObExYVnBMV1p2Ym5RdFkyOXNiM0l4S1R0Y2JpQWdJQ0JpWVdOclozSnZkVzVrTFdOdmJHOXlPaUIyWVhJb0xTMXFjQzFpY21GdVpDMWpiMnh2Y2pFcE8xeHVmVnh1WEc0dWFuVndlWFJsY2kxaWRYUjBiMjR1Ylc5a0xYQnlhVzFoY25rdWJXOWtMV0ZqZEdsMlpTQjdYRzRnSUNBZ1kyOXNiM0k2SUhaaGNpZ3RMV3B3TFdsdWRtVnljMlV0ZFdrdFptOXVkQzFqYjJ4dmNqQXBPMXh1SUNBZ0lHSmhZMnRuY205MWJtUXRZMjlzYjNJNklIWmhjaWd0TFdwd0xXSnlZVzVrTFdOdmJHOXlNQ2s3WEc1OVhHNWNiaTVxZFhCNWRHVnlMV0oxZEhSdmJpNXRiMlF0Y0hKcGJXRnllVHBoWTNScGRtVWdlMXh1SUNBZ0lHTnZiRzl5T2lCMllYSW9MUzFxY0MxcGJuWmxjbk5sTFhWcExXWnZiblF0WTI5c2IzSXdLVHRjYmlBZ0lDQmlZV05yWjNKdmRXNWtMV052Ykc5eU9pQjJZWElvTFMxcWNDMWljbUZ1WkMxamIyeHZjakFwTzF4dWZWeHVYRzR2S2lCQ2RYUjBiMjRnWENKVGRXTmpaWE56WENJZ1UzUjViR2x1WnlBcUwxeHVYRzR1YW5Wd2VYUmxjaTFpZFhSMGIyNHViVzlrTFhOMVkyTmxjM01nZTF4dUlDQWdJR052Ykc5eU9pQjJZWElvTFMxcWNDMXBiblpsY25ObExYVnBMV1p2Ym5RdFkyOXNiM0l4S1R0Y2JpQWdJQ0JpWVdOclozSnZkVzVrTFdOdmJHOXlPaUIyWVhJb0xTMXFjQzF6ZFdOalpYTnpMV052Ykc5eU1TazdYRzU5WEc1Y2JpNXFkWEI1ZEdWeUxXSjFkSFJ2Ymk1dGIyUXRjM1ZqWTJWemN5NXRiMlF0WVdOMGFYWmxJSHRjYmlBZ0lDQmpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGFXNTJaWEp6WlMxMWFTMW1iMjUwTFdOdmJHOXlNQ2s3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaQzFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRjM1ZqWTJWemN5MWpiMnh2Y2pBcE8xeHVJSDFjYmx4dUxtcDFjSGwwWlhJdFluVjBkRzl1TG0xdlpDMXpkV05qWlhOek9tRmpkR2wyWlNCN1hHNGdJQ0FnWTI5c2IzSTZJSFpoY2lndExXcHdMV2x1ZG1WeWMyVXRkV2t0Wm05dWRDMWpiMnh2Y2pBcE8xeHVJQ0FnSUdKaFkydG5jbTkxYm1RdFkyOXNiM0k2SUhaaGNpZ3RMV3B3TFhOMVkyTmxjM010WTI5c2IzSXdLVHRjYmlCOVhHNWNiaUF2S2lCQ2RYUjBiMjRnWENKSmJtWnZYQ0lnVTNSNWJHbHVaeUFxTDF4dVhHNHVhblZ3ZVhSbGNpMWlkWFIwYjI0dWJXOWtMV2x1Wm04Z2UxeHVJQ0FnSUdOdmJHOXlPaUIyWVhJb0xTMXFjQzFwYm5abGNuTmxMWFZwTFdadmJuUXRZMjlzYjNJeEtUdGNiaUFnSUNCaVlXTnJaM0p2ZFc1a0xXTnZiRzl5T2lCMllYSW9MUzFxY0MxcGJtWnZMV052Ykc5eU1TazdYRzU5WEc1Y2JpNXFkWEI1ZEdWeUxXSjFkSFJ2Ymk1dGIyUXRhVzVtYnk1dGIyUXRZV04wYVhabElIdGNiaUFnSUNCamIyeHZjam9nZG1GeUtDMHRhbkF0YVc1MlpYSnpaUzExYVMxbWIyNTBMV052Ykc5eU1DazdYRzRnSUNBZ1ltRmphMmR5YjNWdVpDMWpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGFXNW1ieTFqYjJ4dmNqQXBPMXh1ZlZ4dVhHNHVhblZ3ZVhSbGNpMWlkWFIwYjI0dWJXOWtMV2x1Wm04NllXTjBhWFpsSUh0Y2JpQWdJQ0JqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRhVzUyWlhKelpTMTFhUzFtYjI1MExXTnZiRzl5TUNrN1hHNGdJQ0FnWW1GamEyZHliM1Z1WkMxamIyeHZjam9nZG1GeUtDMHRhbkF0YVc1bWJ5MWpiMnh2Y2pBcE8xeHVmVnh1WEc0dktpQkNkWFIwYjI0Z1hDSlhZWEp1YVc1blhDSWdVM1I1YkdsdVp5QXFMMXh1WEc0dWFuVndlWFJsY2kxaWRYUjBiMjR1Ylc5a0xYZGhjbTVwYm1jZ2UxeHVJQ0FnSUdOdmJHOXlPaUIyWVhJb0xTMXFjQzFwYm5abGNuTmxMWFZwTFdadmJuUXRZMjlzYjNJeEtUdGNiaUFnSUNCaVlXTnJaM0p2ZFc1a0xXTnZiRzl5T2lCMllYSW9MUzFxY0MxM1lYSnVMV052Ykc5eU1TazdYRzU5WEc1Y2JpNXFkWEI1ZEdWeUxXSjFkSFJ2Ymk1dGIyUXRkMkZ5Ym1sdVp5NXRiMlF0WVdOMGFYWmxJSHRjYmlBZ0lDQmpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGFXNTJaWEp6WlMxMWFTMW1iMjUwTFdOdmJHOXlNQ2s3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaQzFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRkMkZ5YmkxamIyeHZjakFwTzF4dWZWeHVYRzR1YW5Wd2VYUmxjaTFpZFhSMGIyNHViVzlrTFhkaGNtNXBibWM2WVdOMGFYWmxJSHRjYmlBZ0lDQmpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGFXNTJaWEp6WlMxMWFTMW1iMjUwTFdOdmJHOXlNQ2s3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaQzFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRkMkZ5YmkxamIyeHZjakFwTzF4dWZWeHVYRzR2S2lCQ2RYUjBiMjRnWENKRVlXNW5aWEpjSWlCVGRIbHNhVzVuSUNvdlhHNWNiaTVxZFhCNWRHVnlMV0oxZEhSdmJpNXRiMlF0WkdGdVoyVnlJSHRjYmlBZ0lDQmpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGFXNTJaWEp6WlMxMWFTMW1iMjUwTFdOdmJHOXlNU2s3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaQzFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRaWEp5YjNJdFkyOXNiM0l4S1R0Y2JuMWNibHh1TG1wMWNIbDBaWEl0WW5WMGRHOXVMbTF2WkMxa1lXNW5aWEl1Ylc5a0xXRmpkR2wyWlNCN1hHNGdJQ0FnWTI5c2IzSTZJSFpoY2lndExXcHdMV2x1ZG1WeWMyVXRkV2t0Wm05dWRDMWpiMnh2Y2pBcE8xeHVJQ0FnSUdKaFkydG5jbTkxYm1RdFkyOXNiM0k2SUhaaGNpZ3RMV3B3TFdWeWNtOXlMV052Ykc5eU1DazdYRzU5WEc1Y2JpNXFkWEI1ZEdWeUxXSjFkSFJ2Ymk1dGIyUXRaR0Z1WjJWeU9tRmpkR2wyWlNCN1hHNGdJQ0FnWTI5c2IzSTZJSFpoY2lndExXcHdMV2x1ZG1WeWMyVXRkV2t0Wm05dWRDMWpiMnh2Y2pBcE8xeHVJQ0FnSUdKaFkydG5jbTkxYm1RdFkyOXNiM0k2SUhaaGNpZ3RMV3B3TFdWeWNtOXlMV052Ykc5eU1DazdYRzU5WEc1Y2JpOHFJRmRwWkdkbGRDQkNkWFIwYjI0cUwxeHVYRzR1ZDJsa1oyVjBMV0oxZEhSdmJpd2dMbmRwWkdkbGRDMTBiMmRuYkdVdFluVjBkRzl1SUh0Y2JpQWdJQ0IzYVdSMGFEb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGQybGtkR2d0YzJodmNuUXBPMXh1ZlZ4dVhHNHZLaUJYYVdSblpYUWdUR0ZpWld3Z1UzUjViR2x1WnlBcUwxeHVYRzR2S2lCUGRtVnljbWxrWlNCQ2IyOTBjM1J5WVhBZ2JHRmlaV3dnWTNOeklDb3ZYRzR1YW5Wd2VYUmxjaTEzYVdSblpYUnpJR3hoWW1Wc0lIdGNiaUFnSUNCdFlYSm5hVzR0WW05MGRHOXRPaUJwYm1sMGFXRnNPMXh1ZlZ4dVhHNHVkMmxrWjJWMExXeGhZbVZzTFdKaGMybGpJSHRjYmlBZ0lDQXZLaUJDWVhOcFl5Qk1ZV0psYkNBcUwxeHVJQ0FnSUdOdmJHOXlPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV3hoWW1Wc0xXTnZiRzl5S1R0Y2JpQWdJQ0JtYjI1MExYTnBlbVU2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10Wm05dWRDMXphWHBsS1R0Y2JpQWdJQ0J2ZG1WeVpteHZkem9nYUdsa1pHVnVPMXh1SUNBZ0lIUmxlSFF0YjNabGNtWnNiM2M2SUdWc2JHbHdjMmx6TzF4dUlDQWdJSGRvYVhSbExYTndZV05sT2lCdWIzZHlZWEE3WEc0Z0lDQWdiR2x1WlMxb1pXbG5hSFE2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMV2hsYVdkb2RDazdYRzU5WEc1Y2JpNTNhV1JuWlhRdGJHRmlaV3dnZTF4dUlDQWdJQzhxSUV4aFltVnNJQ292WEc0Z0lDQWdZMjlzYjNJNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGJHRmlaV3d0WTI5c2IzSXBPMXh1SUNBZ0lHWnZiblF0YzJsNlpUb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MW1iMjUwTFhOcGVtVXBPMXh1SUNBZ0lHOTJaWEptYkc5M09pQm9hV1JrWlc0N1hHNGdJQ0FnZEdWNGRDMXZkbVZ5Wm14dmR6b2daV3hzYVhCemFYTTdYRzRnSUNBZ2QyaHBkR1V0YzNCaFkyVTZJRzV2ZDNKaGNEdGNiaUFnSUNCc2FXNWxMV2hsYVdkb2REb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGFHVnBaMmgwS1R0Y2JuMWNibHh1TG5kcFpHZGxkQzFwYm14cGJtVXRhR0p2ZUNBdWQybGtaMlYwTFd4aFltVnNJSHRjYmlBZ0lDQXZLaUJJYjNKcGVtOXVkR0ZzSUZkcFpHZGxkQ0JNWVdKbGJDQXFMMXh1SUNBZ0lHTnZiRzl5T2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFd4aFltVnNMV052Ykc5eUtUdGNiaUFnSUNCMFpYaDBMV0ZzYVdkdU9pQnlhV2RvZER0Y2JpQWdJQ0J0WVhKbmFXNHRjbWxuYUhRNklHTmhiR01vSUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMVzFoY21kcGJpa2dLaUF5SUNrN1hHNGdJQ0FnZDJsa2RHZzZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRhVzVzYVc1bExXeGhZbVZzTFhkcFpIUm9LVHRjYmlBZ0lDQm1iR1Y0TFhOb2NtbHVhem9nTUR0Y2JuMWNibHh1TG5kcFpHZGxkQzFwYm14cGJtVXRkbUp2ZUNBdWQybGtaMlYwTFd4aFltVnNJSHRjYmlBZ0lDQXZLaUJXWlhKMGFXTmhiQ0JYYVdSblpYUWdUR0ZpWld3Z0tpOWNiaUFnSUNCamIyeHZjam9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFzWVdKbGJDMWpiMnh2Y2lrN1hHNGdJQ0FnZEdWNGRDMWhiR2xuYmpvZ1kyVnVkR1Z5TzF4dUlDQWdJR3hwYm1VdGFHVnBaMmgwT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzFvWldsbmFIUXBPMXh1ZlZ4dVhHNHZLaUJYYVdSblpYUWdVbVZoWkc5MWRDQlRkSGxzYVc1bklDb3ZYRzVjYmk1M2FXUm5aWFF0Y21WaFpHOTFkQ0I3WEc0Z0lDQWdZMjlzYjNJNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGNtVmhaRzkxZEMxamIyeHZjaWs3WEc0Z0lDQWdabTl1ZEMxemFYcGxPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV1p2Ym5RdGMybDZaU2s3WEc0Z0lDQWdhR1ZwWjJoME9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHViR2x1WlMxb1pXbG5hSFFwTzF4dUlDQWdJR3hwYm1VdGFHVnBaMmgwT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzFvWldsbmFIUXBPMXh1SUNBZ0lHOTJaWEptYkc5M09pQm9hV1JrWlc0N1hHNGdJQ0FnZDJocGRHVXRjM0JoWTJVNklHNXZkM0poY0R0Y2JpQWdJQ0IwWlhoMExXRnNhV2R1T2lCalpXNTBaWEk3WEc1OVhHNWNiaTUzYVdSblpYUXRjbVZoWkc5MWRDNXZkbVZ5Wm14dmR5QjdYRzRnSUNBZ0x5b2dUM1psY21ac2IzZHBibWNnVW1WaFpHOTFkQ0FxTDF4dVhHNGdJQ0FnTHlvZ1JuSnZiU0JOWVhSbGNtbGhiQ0JFWlhOcFoyNGdUR2wwWlZ4dUlDQWdJQ0FnSUNCemFHRmtiM2N0YTJWNUxYVnRZbkpoTFc5d1lXTnBkSGs2SURBdU1qdGNiaUFnSUNBZ0lDQWdjMmhoWkc5M0xXdGxlUzF3Wlc1MWJXSnlZUzF2Y0dGamFYUjVPaUF3TGpFME8xeHVJQ0FnSUNBZ0lDQnphR0ZrYjNjdFlXMWlhV1Z1ZEMxemFHRmtiM2N0YjNCaFkybDBlVG9nTUM0eE1qdGNiaUFnSUNBZ0tpOWNiaUFnSUNBdGQyVmlhMmwwTFdKdmVDMXphR0ZrYjNjNklEQWdNbkI0SURKd2VDQXdJSEpuWW1Fb01Dd2dNQ3dnTUN3Z01DNHlLU3hjYmlBZ0lDQWdJQ0FnSUNBZ0lDQWdJQ0FnSUNBZ0lDQWdJREFnTTNCNElERndlQ0F0TW5CNElISm5ZbUVvTUN3Z01Dd2dNQ3dnTUM0eE5Da3NYRzRnSUNBZ0lDQWdJQ0FnSUNBZ0lDQWdJQ0FnSUNBZ0lDQXdJREZ3ZUNBMWNIZ2dNQ0J5WjJKaEtEQXNJREFzSURBc0lEQXVNVElwTzF4dVhHNGdJQ0FnTFcxdmVpMWliM2d0YzJoaFpHOTNPaUF3SURKd2VDQXljSGdnTUNCeVoySmhLREFzSURBc0lEQXNJREF1TWlrc1hHNGdJQ0FnSUNBZ0lDQWdJQ0FnSUNBZ0lDQWdJQ0F3SUROd2VDQXhjSGdnTFRKd2VDQnlaMkpoS0RBc0lEQXNJREFzSURBdU1UUXBMRnh1SUNBZ0lDQWdJQ0FnSUNBZ0lDQWdJQ0FnSUNBZ01DQXhjSGdnTlhCNElEQWdjbWRpWVNnd0xDQXdMQ0F3TENBd0xqRXlLVHRjYmx4dUlDQWdJR0p2ZUMxemFHRmtiM2M2SURBZ01uQjRJREp3ZUNBd0lISm5ZbUVvTUN3Z01Dd2dNQ3dnTUM0eUtTeGNiaUFnSUNBZ0lDQWdJQ0FnSUNBZ0lDQXdJRE53ZUNBeGNIZ2dMVEp3ZUNCeVoySmhLREFzSURBc0lEQXNJREF1TVRRcExGeHVJQ0FnSUNBZ0lDQWdJQ0FnSUNBZ0lEQWdNWEI0SURWd2VDQXdJSEpuWW1Fb01Dd2dNQ3dnTUN3Z01DNHhNaWs3WEc1OVhHNWNiaTUzYVdSblpYUXRhVzVzYVc1bExXaGliM2dnTG5kcFpHZGxkQzF5WldGa2IzVjBJSHRjYmlBZ0lDQXZLaUJJYjNKcGVtOXVkR0ZzSUZKbFlXUnZkWFFnS2k5Y2JpQWdJQ0IwWlhoMExXRnNhV2R1T2lCalpXNTBaWEk3WEc0Z0lDQWdiV0Y0TFhkcFpIUm9PaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMTNhV1IwYUMxemFHOXlkQ2s3WEc0Z0lDQWdiV2x1TFhkcFpIUm9PaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMTNhV1IwYUMxMGFXNTVLVHRjYmlBZ0lDQnRZWEpuYVc0dGJHVm1kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRiV0Z5WjJsdUtUdGNibjFjYmx4dUxuZHBaR2RsZEMxcGJteHBibVV0ZG1KdmVDQXVkMmxrWjJWMExYSmxZV1J2ZFhRZ2UxeHVJQ0FnSUM4cUlGWmxjblJwWTJGc0lGSmxZV1J2ZFhRZ0tpOWNiaUFnSUNCdFlYSm5hVzR0ZEc5d09pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHViR2x1WlMxdFlYSm5hVzRwTzF4dUlDQWdJQzhxSUdGeklIZHBaR1VnWVhNZ2RHaGxJSGRwWkdkbGRDQXFMMXh1SUNBZ0lIZHBaSFJvT2lCcGJtaGxjbWwwTzF4dWZWeHVYRzR2S2lCWGFXUm5aWFFnUTJobFkydGliM2dnVTNSNWJHbHVaeUFxTDF4dVhHNHVkMmxrWjJWMExXTm9aV05yWW05NElIdGNiaUFnSUNCM2FXUjBhRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRkMmxrZEdncE8xeHVJQ0FnSUdobGFXZG9kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRhR1ZwWjJoMEtUdGNiaUFnSUNCc2FXNWxMV2hsYVdkb2REb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGFHVnBaMmgwS1R0Y2JuMWNibHh1TG5kcFpHZGxkQzFqYUdWamEySnZlQ0JwYm5CMWRGdDBlWEJsUFZ3aVkyaGxZMnRpYjNoY0lsMGdlMXh1SUNBZ0lHMWhjbWRwYmpvZ01IQjRJR05oYkdNb0lIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXNhVzVsTFcxaGNtZHBiaWtnS2lBeUlDa2dNSEI0SURCd2VEdGNiaUFnSUNCc2FXNWxMV2hsYVdkb2REb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGFHVnBaMmgwS1R0Y2JpQWdJQ0JtYjI1MExYTnBlbVU2SUd4aGNtZGxPMXh1SUNBZ0lHWnNaWGd0WjNKdmR6b2dNVHRjYmlBZ0lDQm1iR1Y0TFhOb2NtbHVhem9nTUR0Y2JpQWdJQ0JoYkdsbmJpMXpaV3htT2lCalpXNTBaWEk3WEc1OVhHNWNiaThxSUZkcFpHZGxkQ0JXWVd4cFpDQlRkSGxzYVc1bklDb3ZYRzVjYmk1M2FXUm5aWFF0ZG1Gc2FXUWdlMXh1SUNBZ0lHaGxhV2RvZERvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0YUdWcFoyaDBLVHRjYmlBZ0lDQnNhVzVsTFdobGFXZG9kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRhR1ZwWjJoMEtUdGNiaUFnSUNCM2FXUjBhRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRkMmxrZEdndGMyaHZjblFwTzF4dUlDQWdJR1p2Ym5RdGMybDZaVG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFtYjI1MExYTnBlbVVwTzF4dWZWeHVYRzR1ZDJsa1oyVjBMWFpoYkdsa0lHazZZbVZtYjNKbElIdGNiaUFnSUNCc2FXNWxMV2hsYVdkb2REb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGFHVnBaMmgwS1R0Y2JpQWdJQ0J0WVhKbmFXNHRjbWxuYUhRNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXNhVzVsTFcxaGNtZHBiaWs3WEc0Z0lDQWdiV0Z5WjJsdUxXeGxablE2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMVzFoY21kcGJpazdYRzVjYmlBZ0lDQXZLaUJtY205dElIUm9aU0JtWVNCamJHRnpjeUJwYmlCR2IyNTBRWGRsYzI5dFpUb2dhSFIwY0hNNkx5OW5hWFJvZFdJdVkyOXRMMFp2Y25SQmQyVnpiMjFsTDBadmJuUXRRWGRsYzI5dFpTOWliRzlpTHpRNU1UQXdZemRqTTJFM1lqVTRaRFV3WW1GaE56RmxabVZtTVRGaFpqUXhZVFkyWWpBelpETXZZM056TDJadmJuUXRZWGRsYzI5dFpTNWpjM01qVERFMElDb3ZYRzRnSUNBZ1pHbHpjR3hoZVRvZ2FXNXNhVzVsTFdKc2IyTnJPMXh1SUNBZ0lHWnZiblE2SUc1dmNtMWhiQ0J1YjNKdFlXd2dibTl5YldGc0lERTBjSGd2TVNCR2IyNTBRWGRsYzI5dFpUdGNiaUFnSUNCbWIyNTBMWE5wZW1VNklHbHVhR1Z5YVhRN1hHNGdJQ0FnZEdWNGRDMXlaVzVrWlhKcGJtYzZJR0YxZEc4N1hHNGdJQ0FnTFhkbFltdHBkQzFtYjI1MExYTnRiMjkwYUdsdVp6b2dZVzUwYVdGc2FXRnpaV1E3WEc0Z0lDQWdMVzF2ZWkxdmMzZ3RabTl1ZEMxemJXOXZkR2hwYm1jNklHZHlZWGx6WTJGc1pUdGNibjFjYmx4dUxuZHBaR2RsZEMxMllXeHBaQzV0YjJRdGRtRnNhV1FnYVRwaVpXWnZjbVVnZTF4dUlDQWdJR052Ym5SbGJuUTZJRndpWEZ4bU1EQmpYQ0k3WEc0Z0lDQWdZMjlzYjNJNklHZHlaV1Z1TzF4dWZWeHVYRzR1ZDJsa1oyVjBMWFpoYkdsa0xtMXZaQzFwYm5aaGJHbGtJR2s2WW1WbWIzSmxJSHRjYmlBZ0lDQmpiMjUwWlc1ME9pQmNJbHhjWmpBd1pGd2lPMXh1SUNBZ0lHTnZiRzl5T2lCeVpXUTdYRzU5WEc1Y2JpNTNhV1JuWlhRdGRtRnNhV1F1Ylc5a0xYWmhiR2xrSUM1M2FXUm5aWFF0ZG1Gc2FXUXRjbVZoWkc5MWRDQjdYRzRnSUNBZ1pHbHpjR3hoZVRvZ2JtOXVaVHRjYm4xY2JseHVMeW9nVjJsa1oyVjBJRlJsZUhRZ1lXNWtJRlJsZUhSQmNtVmhJRk4wZVdsdVp5QXFMMXh1WEc0dWQybGtaMlYwTFhSbGVIUmhjbVZoTENBdWQybGtaMlYwTFhSbGVIUWdlMXh1SUNBZ0lIZHBaSFJvT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzEzYVdSMGFDazdYRzU5WEc1Y2JpNTNhV1JuWlhRdGRHVjRkQ0JwYm5CMWRGdDBlWEJsUFZ3aWRHVjRkRndpWFN3Z0xuZHBaR2RsZEMxMFpYaDBJR2x1Y0hWMFczUjVjR1U5WENKdWRXMWlaWEpjSWwxN1hHNGdJQ0FnYUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMW9aV2xuYUhRcE8xeHVJQ0FnSUd4cGJtVXRhR1ZwWjJoME9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHViR2x1WlMxb1pXbG5hSFFwTzF4dWZWeHVYRzR1ZDJsa1oyVjBMWFJsZUhRZ2FXNXdkWFJiZEhsd1pUMWNJblJsZUhSY0lsMDZaR2x6WVdKc1pXUXNJQzUzYVdSblpYUXRkR1Y0ZENCcGJuQjFkRnQwZVhCbFBWd2liblZ0WW1WeVhDSmRPbVJwYzJGaWJHVmtMQ0F1ZDJsa1oyVjBMWFJsZUhSaGNtVmhJSFJsZUhSaGNtVmhPbVJwYzJGaWJHVmtJSHRjYmlBZ0lDQnZjR0ZqYVhSNU9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXUnBjMkZpYkdWa0xXOXdZV05wZEhrcE8xeHVmVnh1WEc0dWQybGtaMlYwTFhSbGVIUWdhVzV3ZFhSYmRIbHdaVDFjSW5SbGVIUmNJbDBzSUM1M2FXUm5aWFF0ZEdWNGRDQnBibkIxZEZ0MGVYQmxQVndpYm5WdFltVnlYQ0pkTENBdWQybGtaMlYwTFhSbGVIUmhjbVZoSUhSbGVIUmhjbVZoSUh0Y2JpQWdJQ0JpYjNndGMybDZhVzVuT2lCaWIzSmtaWEl0WW05NE8xeHVJQ0FnSUdKdmNtUmxjam9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMWliM0prWlhJdGQybGtkR2dwSUhOdmJHbGtJSFpoY2lndExXcHdMWGRwWkdkbGRITXRhVzV3ZFhRdFltOXlaR1Z5TFdOdmJHOXlLVHRjYmlBZ0lDQmlZV05yWjNKdmRXNWtMV052Ykc5eU9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHVjSFYwTFdKaFkydG5jbTkxYm1RdFkyOXNiM0lwTzF4dUlDQWdJR052Ykc5eU9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHVjSFYwTFdOdmJHOXlLVHRjYmlBZ0lDQm1iMjUwTFhOcGVtVTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRabTl1ZEMxemFYcGxLVHRjYmlBZ0lDQndZV1JrYVc1bk9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHVjSFYwTFhCaFpHUnBibWNwSUdOaGJHTW9JSFpoY2lndExXcHdMWGRwWkdkbGRITXRhVzV3ZFhRdGNHRmtaR2x1WnlrZ0tpQWdNaUFwTzF4dUlDQWdJR1pzWlhndFozSnZkem9nTVR0Y2JpQWdJQ0J0YVc0dGQybGtkR2c2SURBN0lDOHFJRlJvYVhNZ2JXRnJaWE1nYVhRZ2NHOXpjMmxpYkdVZ1ptOXlJSFJvWlNCbWJHVjRZbTk0SUhSdklITm9jbWx1YXlCMGFHbHpJR2x1Y0hWMElDb3ZYRzRnSUNBZ1pteGxlQzF6YUhKcGJtczZJREU3WEc0Z0lDQWdiM1YwYkdsdVpUb2dibTl1WlNBaGFXMXdiM0owWVc1ME8xeHVmVnh1WEc0dWQybGtaMlYwTFhSbGVIUmhjbVZoSUhSbGVIUmhjbVZoSUh0Y2JpQWdJQ0JvWldsbmFIUTZJR2x1YUdWeWFYUTdYRzRnSUNBZ2QybGtkR2c2SUdsdWFHVnlhWFE3WEc1OVhHNWNiaTUzYVdSblpYUXRkR1Y0ZENCcGJuQjFkRHBtYjJOMWN5d2dMbmRwWkdkbGRDMTBaWGgwWVhKbFlTQjBaWGgwWVhKbFlUcG1iMk4xY3lCN1hHNGdJQ0FnWW05eVpHVnlMV052Ykc5eU9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHVjSFYwTFdadlkzVnpMV0p2Y21SbGNpMWpiMnh2Y2lrN1hHNTlYRzVjYmk4cUlGZHBaR2RsZENCVGJHbGtaWElnS2k5Y2JseHVMbmRwWkdkbGRDMXpiR2xrWlhJZ0xuVnBMWE5zYVdSbGNpQjdYRzRnSUNBZ0x5b2dVMnhwWkdWeUlGUnlZV05ySUNvdlhHNGdJQ0FnWW05eVpHVnlPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMWE5zYVdSbGNpMWliM0prWlhJdGQybGtkR2dwSUhOdmJHbGtJSFpoY2lndExXcHdMV3hoZVc5MWRDMWpiMnh2Y2pNcE8xeHVJQ0FnSUdKaFkydG5jbTkxYm1RNklIWmhjaWd0TFdwd0xXeGhlVzkxZEMxamIyeHZjak1wTzF4dUlDQWdJR0p2ZUMxemFYcHBibWM2SUdKdmNtUmxjaTFpYjNnN1hHNGdJQ0FnY0c5emFYUnBiMjQ2SUhKbGJHRjBhWFpsTzF4dUlDQWdJR0p2Y21SbGNpMXlZV1JwZFhNNklEQndlRHRjYm4xY2JseHVMbmRwWkdkbGRDMXpiR2xrWlhJZ0xuVnBMWE5zYVdSbGNpQXVkV2t0YzJ4cFpHVnlMV2hoYm1Sc1pTQjdYRzRnSUNBZ0x5b2dVMnhwWkdWeUlFaGhibVJzWlNBcUwxeHVJQ0FnSUc5MWRHeHBibVU2SUc1dmJtVWdJV2x0Y0c5eWRHRnVkRHNnTHlvZ1ptOWpkWE5sWkNCemJHbGtaWElnYUdGdVpHeGxjeUJoY21VZ1kyOXNiM0psWkNBdElITmxaU0JpWld4dmR5QXFMMXh1SUNBZ0lIQnZjMmwwYVc5dU9pQmhZbk52YkhWMFpUdGNiaUFnSUNCaVlXTnJaM0p2ZFc1a0xXTnZiRzl5T2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFhOc2FXUmxjaTFvWVc1a2JHVXRZbUZqYTJkeWIzVnVaQzFqYjJ4dmNpazdYRzRnSUNBZ1ltOXlaR1Z5T2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFhOc2FXUmxjaTFpYjNKa1pYSXRkMmxrZEdncElITnZiR2xrSUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YzJ4cFpHVnlMV2hoYm1Sc1pTMWliM0prWlhJdFkyOXNiM0lwTzF4dUlDQWdJR0p2ZUMxemFYcHBibWM2SUdKdmNtUmxjaTFpYjNnN1hHNGdJQ0FnZWkxcGJtUmxlRG9nTVR0Y2JpQWdJQ0JpWVdOclozSnZkVzVrTFdsdFlXZGxPaUJ1YjI1bE95QXZLaUJQZG1WeWNtbGtaU0JxY1hWbGNua3RkV2tnS2k5Y2JuMWNibHh1THlvZ1QzWmxjbkpwWkdVZ2FuRjFaWEo1TFhWcElDb3ZYRzR1ZDJsa1oyVjBMWE5zYVdSbGNpQXVkV2t0YzJ4cFpHVnlJQzUxYVMxemJHbGtaWEl0YUdGdVpHeGxPbWh2ZG1WeUxDQXVkMmxrWjJWMExYTnNhV1JsY2lBdWRXa3RjMnhwWkdWeUlDNTFhUzF6Ykdsa1pYSXRhR0Z1Wkd4bE9tWnZZM1Z6SUh0Y2JpQWdJQ0JpWVdOclozSnZkVzVrTFdOdmJHOXlPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMWE5zYVdSbGNpMWhZM1JwZG1VdGFHRnVaR3hsTFdOdmJHOXlLVHRjYmlBZ0lDQmliM0prWlhJNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGMyeHBaR1Z5TFdKdmNtUmxjaTEzYVdSMGFDa2djMjlzYVdRZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxemJHbGtaWEl0WVdOMGFYWmxMV2hoYm1Sc1pTMWpiMnh2Y2lrN1hHNTlYRzVjYmk1M2FXUm5aWFF0YzJ4cFpHVnlJQzUxYVMxemJHbGtaWElnTG5WcExYTnNhV1JsY2kxb1lXNWtiR1U2WVdOMGFYWmxJSHRjYmlBZ0lDQmlZV05yWjNKdmRXNWtMV052Ykc5eU9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxYTnNhV1JsY2kxaFkzUnBkbVV0YUdGdVpHeGxMV052Ykc5eUtUdGNiaUFnSUNCaWIzSmtaWEl0WTI5c2IzSTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRjMnhwWkdWeUxXRmpkR2wyWlMxb1lXNWtiR1V0WTI5c2IzSXBPMXh1SUNBZ0lIb3RhVzVrWlhnNklESTdYRzRnSUNBZ2RISmhibk5tYjNKdE9pQnpZMkZzWlNneExqSXBPMXh1ZlZ4dVhHNHVkMmxrWjJWMExYTnNhV1JsY2lBZ0xuVnBMWE5zYVdSbGNpQXVkV2t0YzJ4cFpHVnlMWEpoYm1kbElIdGNiaUFnSUNBdktpQkpiblJsY25aaGJDQmlaWFIzWldWdUlIUm9aU0IwZDI4Z2MzQmxZMmxtYVdWa0lIWmhiSFZsSUc5bUlHRWdaRzkxWW14bElITnNhV1JsY2lBcUwxeHVJQ0FnSUhCdmMybDBhVzl1T2lCaFluTnZiSFYwWlR0Y2JpQWdJQ0JpWVdOclozSnZkVzVrT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFhOc2FXUmxjaTFoWTNScGRtVXRhR0Z1Wkd4bExXTnZiRzl5S1R0Y2JpQWdJQ0I2TFdsdVpHVjRPaUF3TzF4dWZWeHVYRzR2S2lCVGFHRndaWE1nYjJZZ1UyeHBaR1Z5SUVoaGJtUnNaWE1nS2k5Y2JseHVMbmRwWkdkbGRDMW9jMnhwWkdWeUlDNTFhUzF6Ykdsa1pYSWdMblZwTFhOc2FXUmxjaTFvWVc1a2JHVWdlMXh1SUNBZ0lIZHBaSFJvT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFhOc2FXUmxjaTFvWVc1a2JHVXRjMmw2WlNrN1hHNGdJQ0FnYUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMWE5zYVdSbGNpMW9ZVzVrYkdVdGMybDZaU2s3WEc0Z0lDQWdiV0Z5WjJsdUxYUnZjRG9nWTJGc1l5Z29kbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXpiR2xrWlhJdGRISmhZMnN0ZEdocFkydHVaWE56S1NBdElIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGMyeHBaR1Z5TFdoaGJtUnNaUzF6YVhwbEtTa2dMeUF5SUMwZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxemJHbGtaWEl0WW05eVpHVnlMWGRwWkhSb0tTazdYRzRnSUNBZ2JXRnlaMmx1TFd4bFpuUTZJR05oYkdNb2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxemJHbGtaWEl0YUdGdVpHeGxMWE5wZW1VcElDOGdMVElnS3lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFhOc2FXUmxjaTFpYjNKa1pYSXRkMmxrZEdncEtUdGNiaUFnSUNCaWIzSmtaWEl0Y21Ga2FYVnpPaUExTUNVN1hHNGdJQ0FnZEc5d09pQXdPMXh1ZlZ4dVhHNHVkMmxrWjJWMExYWnpiR2xrWlhJZ0xuVnBMWE5zYVdSbGNpQXVkV2t0YzJ4cFpHVnlMV2hoYm1Sc1pTQjdYRzRnSUNBZ2QybGtkR2c2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YzJ4cFpHVnlMV2hoYm1Sc1pTMXphWHBsS1R0Y2JpQWdJQ0JvWldsbmFIUTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRjMnhwWkdWeUxXaGhibVJzWlMxemFYcGxLVHRjYmlBZ0lDQnRZWEpuYVc0dFltOTBkRzl0T2lCallXeGpLSFpoY2lndExXcHdMWGRwWkdkbGRITXRjMnhwWkdWeUxXaGhibVJzWlMxemFYcGxLU0F2SUMweUlDc2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXpiR2xrWlhJdFltOXlaR1Z5TFhkcFpIUm9LU2s3WEc0Z0lDQWdiV0Z5WjJsdUxXeGxablE2SUdOaGJHTW9LSFpoY2lndExXcHdMWGRwWkdkbGRITXRjMnhwWkdWeUxYUnlZV05yTFhSb2FXTnJibVZ6Y3lrZ0xTQjJZWElvTFMxcWNDMTNhV1JuWlhSekxYTnNhV1JsY2kxb1lXNWtiR1V0YzJsNlpTa3BJQzhnTWlBdElIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGMyeHBaR1Z5TFdKdmNtUmxjaTEzYVdSMGFDa3BPMXh1SUNBZ0lHSnZjbVJsY2kxeVlXUnBkWE02SURVd0pUdGNiaUFnSUNCc1pXWjBPaUF3TzF4dWZWeHVYRzR1ZDJsa1oyVjBMV2h6Ykdsa1pYSWdMblZwTFhOc2FXUmxjaUF1ZFdrdGMyeHBaR1Z5TFhKaGJtZGxJSHRjYmlBZ0lDQm9aV2xuYUhRNklHTmhiR01vSUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YzJ4cFpHVnlMWFJ5WVdOckxYUm9hV05yYm1WemN5a2dLaUF5SUNrN1hHNGdJQ0FnYldGeVoybHVMWFJ2Y0RvZ1kyRnNZeWdvZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTF6Ykdsa1pYSXRkSEpoWTJzdGRHaHBZMnR1WlhOektTQXRJSFpoY2lndExXcHdMWGRwWkdkbGRITXRjMnhwWkdWeUxYUnlZV05yTFhSb2FXTnJibVZ6Y3lrZ0tpQXlJQ2tnTHlBeUlDMGdkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXpiR2xrWlhJdFltOXlaR1Z5TFhkcFpIUm9LU2s3WEc1OVhHNWNiaTUzYVdSblpYUXRkbk5zYVdSbGNpQXVkV2t0YzJ4cFpHVnlJQzUxYVMxemJHbGtaWEl0Y21GdVoyVWdlMXh1SUNBZ0lIZHBaSFJvT2lCallXeGpLQ0IyWVhJb0xTMXFjQzEzYVdSblpYUnpMWE5zYVdSbGNpMTBjbUZqYXkxMGFHbGphMjVsYzNNcElDb2dNaUFwTzF4dUlDQWdJRzFoY21kcGJpMXNaV1owT2lCallXeGpLQ2gyWVhJb0xTMXFjQzEzYVdSblpYUnpMWE5zYVdSbGNpMTBjbUZqYXkxMGFHbGphMjVsYzNNcElDMGdkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXpiR2xrWlhJdGRISmhZMnN0ZEdocFkydHVaWE56S1NBcUlESWdLU0F2SURJZ0xTQjJZWElvTFMxcWNDMTNhV1JuWlhSekxYTnNhV1JsY2kxaWIzSmtaWEl0ZDJsa2RHZ3BLVHRjYm4xY2JseHVMeW9nU0c5eWFYcHZiblJoYkNCVGJHbGtaWElnS2k5Y2JseHVMbmRwWkdkbGRDMW9jMnhwWkdWeUlIdGNiaUFnSUNCM2FXUjBhRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRkMmxrZEdncE8xeHVJQ0FnSUdobGFXZG9kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRhR1ZwWjJoMEtUdGNiaUFnSUNCc2FXNWxMV2hsYVdkb2REb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGFHVnBaMmgwS1R0Y2JseHVJQ0FnSUM4cUlFOTJaWEp5YVdSbElIUm9aU0JoYkdsbmJpMXBkR1Z0Y3lCaVlYTmxiR2x1WlM0Z1ZHaHBjeUIzWVhrc0lIUm9aU0JrWlhOamNtbHdkR2x2YmlCaGJtUWdjbVZoWkc5MWRGeHVJQ0FnSUhOMGFXeHNJSE5sWlcwZ2RHOGdZV3hwWjI0Z2RHaGxhWElnWW1GelpXeHBibVVnY0hKdmNHVnliSGtzSUdGdVpDQjNaU0JrYjI0bmRDQm9ZWFpsSUhSdklHaGhkbVZjYmlBZ0lDQmhiR2xuYmkxelpXeG1PaUJ6ZEhKbGRHTm9JR2x1SUhSb1pTQXVjMnhwWkdWeUxXTnZiblJoYVc1bGNpNGdLaTljYmlBZ0lDQmhiR2xuYmkxcGRHVnRjem9nWTJWdWRHVnlPMXh1ZlZ4dVhHNHVkMmxrWjJWMGN5MXpiR2xrWlhJZ0xuTnNhV1JsY2kxamIyNTBZV2x1WlhJZ2UxeHVJQ0FnSUc5MlpYSm1iRzkzT2lCMmFYTnBZbXhsTzF4dWZWeHVYRzR1ZDJsa1oyVjBMV2h6Ykdsa1pYSWdMbk5zYVdSbGNpMWpiMjUwWVdsdVpYSWdlMXh1SUNBZ0lHaGxhV2RvZERvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0YUdWcFoyaDBLVHRjYmlBZ0lDQnRZWEpuYVc0dGJHVm1kRG9nWTJGc1l5aDJZWElvTFMxcWNDMTNhV1JuWlhSekxYTnNhV1JsY2kxb1lXNWtiR1V0YzJsNlpTa2dMeUF5SUMwZ01pQXFJSFpoY2lndExXcHdMWGRwWkdkbGRITXRjMnhwWkdWeUxXSnZjbVJsY2kxM2FXUjBhQ2twTzF4dUlDQWdJRzFoY21kcGJpMXlhV2RvZERvZ1kyRnNZeWgyWVhJb0xTMXFjQzEzYVdSblpYUnpMWE5zYVdSbGNpMW9ZVzVrYkdVdGMybDZaU2tnTHlBeUlDMGdNaUFxSUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YzJ4cFpHVnlMV0p2Y21SbGNpMTNhV1IwYUNrcE8xeHVJQ0FnSUdac1pYZzZJREVnTVNCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzEzYVdSMGFDMXphRzl5ZENrN1hHNTlYRzVjYmk1M2FXUm5aWFF0YUhOc2FXUmxjaUF1ZFdrdGMyeHBaR1Z5SUh0Y2JpQWdJQ0F2S2lCSmJtNWxjaXdnYVc1MmFYTnBZbXhsSUhOc2FXUmxJR1JwZGlBcUwxeHVJQ0FnSUdobGFXZG9kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTF6Ykdsa1pYSXRkSEpoWTJzdGRHaHBZMnR1WlhOektUdGNiaUFnSUNCdFlYSm5hVzR0ZEc5d09pQmpZV3hqS0NoMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzFvWldsbmFIUXBJQzBnZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTF6Ykdsa1pYSXRkSEpoWTJzdGRHaHBZMnR1WlhOektTa2dMeUF5S1R0Y2JpQWdJQ0IzYVdSMGFEb2dNVEF3SlR0Y2JuMWNibHh1THlvZ1ZtVnlkR2xqWVd3Z1UyeHBaR1Z5SUNvdlhHNWNiaTUzYVdSblpYUXRkbUp2ZUNBdWQybGtaMlYwTFd4aFltVnNJSHRjYmlBZ0lDQm9aV2xuYUhRNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXNhVzVsTFdobGFXZG9kQ2s3WEc0Z0lDQWdiR2x1WlMxb1pXbG5hSFE2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMV2hsYVdkb2RDazdYRzU5WEc1Y2JpNTNhV1JuWlhRdGRuTnNhV1JsY2lCN1hHNGdJQ0FnTHlvZ1ZtVnlkR2xqWVd3Z1UyeHBaR1Z5SUNvdlhHNGdJQ0FnYUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMWFpsY25ScFkyRnNMV2hsYVdkb2RDazdYRzRnSUNBZ2QybGtkR2c2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMWGRwWkhSb0xYUnBibmtwTzF4dWZWeHVYRzR1ZDJsa1oyVjBMWFp6Ykdsa1pYSWdMbk5zYVdSbGNpMWpiMjUwWVdsdVpYSWdlMXh1SUNBZ0lHWnNaWGc2SURFZ01TQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHViR2x1WlMxM2FXUjBhQzF6YUc5eWRDazdYRzRnSUNBZ2JXRnlaMmx1TFd4bFpuUTZJR0YxZEc4N1hHNGdJQ0FnYldGeVoybHVMWEpwWjJoME9pQmhkWFJ2TzF4dUlDQWdJRzFoY21kcGJpMWliM1IwYjIwNklHTmhiR01vZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTF6Ykdsa1pYSXRhR0Z1Wkd4bExYTnBlbVVwSUM4Z01pQXRJRElnS2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFhOc2FXUmxjaTFpYjNKa1pYSXRkMmxrZEdncEtUdGNiaUFnSUNCdFlYSm5hVzR0ZEc5d09pQmpZV3hqS0haaGNpZ3RMV3B3TFhkcFpHZGxkSE10YzJ4cFpHVnlMV2hoYm1Sc1pTMXphWHBsS1NBdklESWdMU0F5SUNvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxemJHbGtaWEl0WW05eVpHVnlMWGRwWkhSb0tTazdYRzRnSUNBZ1pHbHpjR3hoZVRvZ1pteGxlRHRjYmlBZ0lDQm1iR1Y0TFdScGNtVmpkR2x2YmpvZ1kyOXNkVzF1TzF4dWZWeHVYRzR1ZDJsa1oyVjBMWFp6Ykdsa1pYSWdMblZwTFhOc2FXUmxjaTEyWlhKMGFXTmhiQ0I3WEc0Z0lDQWdMeW9nU1c1dVpYSXNJR2x1ZG1semFXSnNaU0J6Ykdsa1pTQmthWFlnS2k5Y2JpQWdJQ0IzYVdSMGFEb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXpiR2xrWlhJdGRISmhZMnN0ZEdocFkydHVaWE56S1R0Y2JpQWdJQ0JtYkdWNExXZHliM2M2SURFN1hHNGdJQ0FnYldGeVoybHVMV3hsWm5RNklHRjFkRzg3WEc0Z0lDQWdiV0Z5WjJsdUxYSnBaMmgwT2lCaGRYUnZPMXh1ZlZ4dVhHNHZLaUJYYVdSblpYUWdVSEp2WjNKbGMzTWdVM1I1YkdsdVp5QXFMMXh1WEc0dWNISnZaM0psYzNNdFltRnlJSHRjYmlBZ0lDQXRkMlZpYTJsMExYUnlZVzV6YVhScGIyNDZJRzV2Ym1VN1hHNGdJQ0FnTFcxdmVpMTBjbUZ1YzJsMGFXOXVPaUJ1YjI1bE8xeHVJQ0FnSUMxdGN5MTBjbUZ1YzJsMGFXOXVPaUJ1YjI1bE8xeHVJQ0FnSUMxdkxYUnlZVzV6YVhScGIyNDZJRzV2Ym1VN1hHNGdJQ0FnZEhKaGJuTnBkR2x2YmpvZ2JtOXVaVHRjYm4xY2JseHVMbkJ5YjJkeVpYTnpMV0poY2lCN1hHNGdJQ0FnYUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMW9aV2xuYUhRcE8xeHVmVnh1WEc0dWNISnZaM0psYzNNdFltRnlJSHRjYmlBZ0lDQmlZV05yWjNKdmRXNWtMV052Ykc5eU9pQjJZWElvTFMxcWNDMWljbUZ1WkMxamIyeHZjakVwTzF4dWZWeHVYRzR1Y0hKdlozSmxjM010WW1GeUxYTjFZMk5sYzNNZ2UxeHVJQ0FnSUdKaFkydG5jbTkxYm1RdFkyOXNiM0k2SUhaaGNpZ3RMV3B3TFhOMVkyTmxjM010WTI5c2IzSXhLVHRjYm4xY2JseHVMbkJ5YjJkeVpYTnpMV0poY2kxcGJtWnZJSHRjYmlBZ0lDQmlZV05yWjNKdmRXNWtMV052Ykc5eU9pQjJZWElvTFMxcWNDMXBibVp2TFdOdmJHOXlNU2s3WEc1OVhHNWNiaTV3Y205bmNtVnpjeTFpWVhJdGQyRnlibWx1WnlCN1hHNGdJQ0FnWW1GamEyZHliM1Z1WkMxamIyeHZjam9nZG1GeUtDMHRhbkF0ZDJGeWJpMWpiMnh2Y2pFcE8xeHVmVnh1WEc0dWNISnZaM0psYzNNdFltRnlMV1JoYm1kbGNpQjdYRzRnSUNBZ1ltRmphMmR5YjNWdVpDMWpiMnh2Y2pvZ2RtRnlLQzB0YW5BdFpYSnliM0l0WTI5c2IzSXhLVHRjYm4xY2JseHVMbkJ5YjJkeVpYTnpJSHRjYmlBZ0lDQmlZV05yWjNKdmRXNWtMV052Ykc5eU9pQjJZWElvTFMxcWNDMXNZWGx2ZFhRdFkyOXNiM0l5S1R0Y2JpQWdJQ0JpYjNKa1pYSTZJRzV2Ym1VN1hHNGdJQ0FnWW05NExYTm9ZV1J2ZHpvZ2JtOXVaVHRjYm4xY2JseHVMeW9nU0c5eWFYTnZiblJoYkNCUWNtOW5jbVZ6Y3lBcUwxeHVYRzR1ZDJsa1oyVjBMV2h3Y205bmNtVnpjeUI3WEc0Z0lDQWdMeW9nVUhKdlozSmxjM01nUW1GeUlDb3ZYRzRnSUNBZ2FHVnBaMmgwT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzFvWldsbmFIUXBPMXh1SUNBZ0lHeHBibVV0YUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMW9aV2xuYUhRcE8xeHVJQ0FnSUhkcFpIUm9PaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMTNhV1IwYUNrN1hHNGdJQ0FnWVd4cFoyNHRhWFJsYlhNNklHTmxiblJsY2p0Y2JseHVmVnh1WEc0dWQybGtaMlYwTFdod2NtOW5jbVZ6Y3lBdWNISnZaM0psYzNNZ2UxeHVJQ0FnSUdac1pYZ3RaM0p2ZHpvZ01UdGNiaUFnSUNCdFlYSm5hVzR0ZEc5d09pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHVjSFYwTFhCaFpHUnBibWNwTzF4dUlDQWdJRzFoY21kcGJpMWliM1IwYjIwNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXdkWFF0Y0dGa1pHbHVaeWs3WEc0Z0lDQWdZV3hwWjI0dGMyVnNaam9nYzNSeVpYUmphRHRjYmlBZ0lDQXZLaUJQZG1WeWNtbGtaU0JpYjI5MGMzUnlZWEFnYzNSNWJHVWdLaTljYmlBZ0lDQm9aV2xuYUhRNklHbHVhWFJwWVd3N1hHNTlYRzVjYmk4cUlGWmxjblJwWTJGc0lGQnliMmR5WlhOeklDb3ZYRzVjYmk1M2FXUm5aWFF0ZG5CeWIyZHlaWE56SUh0Y2JpQWdJQ0JvWldsbmFIUTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRkbVZ5ZEdsallXd3RhR1ZwWjJoMEtUdGNiaUFnSUNCM2FXUjBhRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRkMmxrZEdndGRHbHVlU2s3WEc1OVhHNWNiaTUzYVdSblpYUXRkbkJ5YjJkeVpYTnpJQzV3Y205bmNtVnpjeUI3WEc0Z0lDQWdabXhsZUMxbmNtOTNPaUF4TzF4dUlDQWdJSGRwWkhSb09pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxYQnliMmR5WlhOekxYUm9hV05yYm1WemN5azdYRzRnSUNBZ2JXRnlaMmx1TFd4bFpuUTZJR0YxZEc4N1hHNGdJQ0FnYldGeVoybHVMWEpwWjJoME9pQmhkWFJ2TzF4dUlDQWdJRzFoY21kcGJpMWliM1IwYjIwNklEQTdYRzU5WEc1Y2JpOHFJRk5sYkdWamRDQlhhV1JuWlhRZ1UzUjViR2x1WnlBcUwxeHVYRzR1ZDJsa1oyVjBMV1J5YjNCa2IzZHVJSHRjYmlBZ0lDQm9aV2xuYUhRNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXNhVzVsTFdobGFXZG9kQ2s3WEc0Z0lDQWdkMmxrZEdnNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXNhVzVsTFhkcFpIUm9LVHRjYmlBZ0lDQnNhVzVsTFdobGFXZG9kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRhR1ZwWjJoMEtUdGNibjFjYmx4dUxuZHBaR2RsZEMxa2NtOXdaRzkzYmlBK0lITmxiR1ZqZENCN1hHNGdJQ0FnY0dGa1pHbHVaeTF5YVdkb2REb2dNakJ3ZUR0Y2JpQWdJQ0JpYjNKa1pYSTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRhVzV3ZFhRdFltOXlaR1Z5TFhkcFpIUm9LU0J6YjJ4cFpDQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHVjSFYwTFdKdmNtUmxjaTFqYjJ4dmNpazdYRzRnSUNBZ1ltOXlaR1Z5TFhKaFpHbDFjem9nTUR0Y2JpQWdJQ0JvWldsbmFIUTZJR2x1YUdWeWFYUTdYRzRnSUNBZ1pteGxlRG9nTVNBeElIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXNhVzVsTFhkcFpIUm9MWE5vYjNKMEtUdGNiaUFnSUNCdGFXNHRkMmxrZEdnNklEQTdJQzhxSUZSb2FYTWdiV0ZyWlhNZ2FYUWdjRzl6YzJsaWJHVWdabTl5SUhSb1pTQm1iR1Y0WW05NElIUnZJSE5vY21sdWF5QjBhR2x6SUdsdWNIVjBJQ292WEc0Z0lDQWdZbTk0TFhOcGVtbHVaem9nWW05eVpHVnlMV0p2ZUR0Y2JpQWdJQ0J2ZFhSc2FXNWxPaUJ1YjI1bElDRnBiWEJ2Y25SaGJuUTdYRzRnSUNBZ1ltOTRMWE5vWVdSdmR6b2dibTl1WlR0Y2JpQWdJQ0JpWVdOclozSnZkVzVrTFdOdmJHOXlPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1Y0hWMExXSmhZMnRuY205MWJtUXRZMjlzYjNJcE8xeHVJQ0FnSUdOdmJHOXlPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1Y0hWMExXTnZiRzl5S1R0Y2JpQWdJQ0JtYjI1MExYTnBlbVU2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10Wm05dWRDMXphWHBsS1R0Y2JpQWdJQ0IyWlhKMGFXTmhiQzFoYkdsbmJqb2dkRzl3TzF4dUlDQWdJSEJoWkdScGJtY3RiR1ZtZERvZ1kyRnNZeWdnZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMXdZV1JrYVc1bktTQXFJRElwTzF4dVhIUmhjSEJsWVhKaGJtTmxPaUJ1YjI1bE8xeHVYSFF0ZDJWaWEybDBMV0Z3Y0dWaGNtRnVZMlU2SUc1dmJtVTdYRzVjZEMxdGIzb3RZWEJ3WldGeVlXNWpaVG9nYm05dVpUdGNiaUFnSUNCaVlXTnJaM0p2ZFc1a0xYSmxjR1ZoZERvZ2JtOHRjbVZ3WldGME8xeHVYSFJpWVdOclozSnZkVzVrTFhOcGVtVTZJREl3Y0hnN1hHNWNkR0poWTJ0bmNtOTFibVF0Y0c5emFYUnBiMjQ2SUhKcFoyaDBJR05sYm5SbGNqdGNiaUFnSUNCaVlXTnJaM0p2ZFc1a0xXbHRZV2RsT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdSeWIzQmtiM2R1TFdGeWNtOTNLVHRjYm4xY2JpNTNhV1JuWlhRdFpISnZjR1J2ZDI0Z1BpQnpaV3hsWTNRNlptOWpkWE1nZTF4dUlDQWdJR0p2Y21SbGNpMWpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJuQjFkQzFtYjJOMWN5MWliM0prWlhJdFkyOXNiM0lwTzF4dWZWeHVYRzR1ZDJsa1oyVjBMV1J5YjNCa2IzZHVJRDRnYzJWc1pXTjBPbVJwYzJGaWJHVmtJSHRjYmlBZ0lDQnZjR0ZqYVhSNU9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXUnBjMkZpYkdWa0xXOXdZV05wZEhrcE8xeHVmVnh1WEc0dktpQlVieUJrYVhOaFlteGxJSFJvWlNCa2IzUjBaV1FnWW05eVpHVnlJR2x1SUVacGNtVm1iM2dnWVhKdmRXNWtJSE5sYkdWamRDQmpiMjUwY205c2N5NWNiaUFnSUZObFpTQm9kSFJ3T2k4dmMzUmhZMnR2ZG1WeVpteHZkeTVqYjIwdllTOHhPRGcxTXpBd01pQXFMMXh1TG5kcFpHZGxkQzFrY205d1pHOTNiaUErSUhObGJHVmpkRG90Ylc5NkxXWnZZM1Z6Y21sdVp5QjdYRzRnSUNBZ1kyOXNiM0k2SUhSeVlXNXpjR0Z5Wlc1ME8xeHVJQ0FnSUhSbGVIUXRjMmhoWkc5M09pQXdJREFnTUNBak1EQXdPMXh1ZlZ4dVhHNHZLaUJUWld4bFkzUWdZVzVrSUZObGJHVmpkRTExYkhScGNHeGxJQ292WEc1Y2JpNTNhV1JuWlhRdGMyVnNaV04wSUh0Y2JpQWdJQ0IzYVdSMGFEb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGQybGtkR2dwTzF4dUlDQWdJR3hwYm1VdGFHVnBaMmgwT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzFvWldsbmFIUXBPMXh1WEc0Z0lDQWdMeW9nUW1WallYVnpaU0JHYVhKbFptOTRJR1JsWm1sdVpYTWdkR2hsSUdKaGMyVnNhVzVsSUc5bUlHRWdjMlZzWldOMElHRnpJSFJvWlNCaWIzUjBiMjBnYjJZZ2RHaGxYRzRnSUNBZ1kyOXVkSEp2YkN3Z2QyVWdZV3hwWjI0Z2RHaGxJR1Z1ZEdseVpTQmpiMjUwY205c0lIUnZJSFJvWlNCMGIzQWdZVzVrSUdGa1pDQndZV1JrYVc1bklIUnZJSFJvWlZ4dUlDQWdJSE5sYkdWamRDQjBieUJuWlhRZ1lXNGdZWEJ3Y205NGFXMWhkR1VnWm1seWMzUWdiR2x1WlNCaVlYTmxiR2x1WlNCaGJHbG5ibTFsYm5RdUlDb3ZYRzRnSUNBZ1lXeHBaMjR0YVhSbGJYTTZJR1pzWlhndGMzUmhjblE3WEc1OVhHNWNiaTUzYVdSblpYUXRjMlZzWldOMElENGdjMlZzWldOMElIdGNiaUFnSUNCaWIzSmtaWEk2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1d2RYUXRZbTl5WkdWeUxYZHBaSFJvS1NCemIyeHBaQ0IyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1Y0hWMExXSnZjbVJsY2kxamIyeHZjaWs3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaQzFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibkIxZEMxaVlXTnJaM0p2ZFc1a0xXTnZiRzl5S1R0Y2JpQWdJQ0JqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibkIxZEMxamIyeHZjaWs3WEc0Z0lDQWdabTl1ZEMxemFYcGxPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV1p2Ym5RdGMybDZaU2s3WEc0Z0lDQWdabXhsZURvZ01TQXhJSFpoY2lndExXcHdMWGRwWkdkbGRITXRhVzVzYVc1bExYZHBaSFJvTFhOb2IzSjBLVHRjYmlBZ0lDQnZkWFJzYVc1bE9pQnViMjVsSUNGcGJYQnZjblJoYm5RN1hHNGdJQ0FnYjNabGNtWnNiM2M2SUdGMWRHODdYRzRnSUNBZ2FHVnBaMmgwT2lCcGJtaGxjbWwwTzF4dVhHNGdJQ0FnTHlvZ1FtVmpZWFZ6WlNCR2FYSmxabTk0SUdSbFptbHVaWE1nZEdobElHSmhjMlZzYVc1bElHOW1JR0VnYzJWc1pXTjBJR0Z6SUhSb1pTQmliM1IwYjIwZ2IyWWdkR2hsWEc0Z0lDQWdZMjl1ZEhKdmJDd2dkMlVnWVd4cFoyNGdkR2hsSUdWdWRHbHlaU0JqYjI1MGNtOXNJSFJ2SUhSb1pTQjBiM0FnWVc1a0lHRmtaQ0J3WVdSa2FXNW5JSFJ2SUhSb1pWeHVJQ0FnSUhObGJHVmpkQ0IwYnlCblpYUWdZVzRnWVhCd2NtOTRhVzFoZEdVZ1ptbHljM1FnYkdsdVpTQmlZWE5sYkdsdVpTQmhiR2xuYm0xbGJuUXVJQ292WEc0Z0lDQWdjR0ZrWkdsdVp5MTBiM0E2SURWd2VEdGNibjFjYmx4dUxuZHBaR2RsZEMxelpXeGxZM1FnUGlCelpXeGxZM1E2Wm05amRYTWdlMXh1SUNBZ0lHSnZjbVJsY2kxamIyeHZjam9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMW1iMk4xY3kxaWIzSmtaWEl0WTI5c2IzSXBPMXh1ZlZ4dVhHNHVkMmxuWlhRdGMyVnNaV04wSUQ0Z2MyVnNaV04wSUQ0Z2IzQjBhVzl1SUh0Y2JpQWdJQ0J3WVdSa2FXNW5MV3hsWm5RNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXdkWFF0Y0dGa1pHbHVaeWs3WEc0Z0lDQWdiR2x1WlMxb1pXbG5hSFE2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMV2hsYVdkb2RDazdYRzRnSUNBZ0x5b2diR2x1WlMxb1pXbG5hSFFnWkc5bGMyNG5kQ0IzYjNKcklHOXVJSE52YldVZ1luSnZkM05sY25NZ1ptOXlJSE5sYkdWamRDQnZjSFJwYjI1eklDb3ZYRzRnSUNBZ2NHRmtaR2x1WnkxMGIzQTZJR05oYkdNb2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0YUdWcFoyaDBLUzEyWVhJb0xTMXFjQzEzYVdSblpYUnpMV1p2Ym5RdGMybDZaU2t2TWlrN1hHNGdJQ0FnY0dGa1pHbHVaeTFpYjNSMGIyMDZJR05oYkdNb2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0YUdWcFoyaDBLUzEyWVhJb0xTMXFjQzEzYVdSblpYUnpMV1p2Ym5RdGMybDZaU2t2TWlrN1hHNTlYRzVjYmx4dVhHNHZLaUJVYjJkbmJHVWdRblYwZEc5dWN5QlRkSGxzYVc1bklDb3ZYRzVjYmk1M2FXUm5aWFF0ZEc5bloyeGxMV0oxZEhSdmJuTWdlMXh1SUNBZ0lHeHBibVV0YUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMW9aV2xuYUhRcE8xeHVmVnh1WEc0dWQybGtaMlYwTFhSdloyZHNaUzFpZFhSMGIyNXpJQzUzYVdSblpYUXRkRzluWjJ4bExXSjFkSFJ2YmlCN1hHNGdJQ0FnYldGeVoybHVMV3hsWm5RNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGJXRnlaMmx1S1R0Y2JpQWdJQ0J0WVhKbmFXNHRjbWxuYUhRNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGJXRnlaMmx1S1R0Y2JuMWNibHh1TG5kcFpHZGxkQzEwYjJkbmJHVXRZblYwZEc5dWN5QXVhblZ3ZVhSbGNpMWlkWFIwYjI0NlpHbHpZV0pzWldRZ2UxeHVJQ0FnSUc5d1lXTnBkSGs2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10WkdsellXSnNaV1F0YjNCaFkybDBlU2s3WEc1OVhHNWNiaThxSUZKaFpHbHZJRUoxZEhSdmJuTWdVM1I1YkdsdVp5QXFMMXh1WEc0dWQybGtaMlYwTFhKaFpHbHZJSHRjYmlBZ0lDQjNhV1IwYURvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0ZDJsa2RHZ3BPMXh1SUNBZ0lHeHBibVV0YUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMW9aV2xuYUhRcE8xeHVmVnh1WEc0dWQybGtaMlYwTFhKaFpHbHZMV0p2ZUNCN1hHNGdJQ0FnWkdsemNHeGhlVG9nWm14bGVEdGNiaUFnSUNCbWJHVjRMV1JwY21WamRHbHZiam9nWTI5c2RXMXVPMXh1SUNBZ0lHRnNhV2R1TFdsMFpXMXpPaUJ6ZEhKbGRHTm9PMXh1SUNBZ0lHSnZlQzF6YVhwcGJtYzZJR0p2Y21SbGNpMWliM2c3WEc0Z0lDQWdabXhsZUMxbmNtOTNPaUF4TzF4dUlDQWdJRzFoY21kcGJpMWliM1IwYjIwNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGNtRmthVzh0YVhSbGJTMW9aV2xuYUhRdFlXUnFkWE4wYldWdWRDazdYRzU5WEc1Y2JpNTNhV1JuWlhRdGNtRmthVzh0WW05NElHeGhZbVZzSUh0Y2JpQWdJQ0JvWldsbmFIUTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRjbUZrYVc4dGFYUmxiUzFvWldsbmFIUXBPMXh1SUNBZ0lHeHBibVV0YUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMWEpoWkdsdkxXbDBaVzB0YUdWcFoyaDBLVHRjYmlBZ0lDQm1iMjUwTFhOcGVtVTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRabTl1ZEMxemFYcGxLVHRjYm4xY2JseHVMbmRwWkdkbGRDMXlZV1JwYnkxaWIzZ2dhVzV3ZFhRZ2UxeHVJQ0FnSUdobGFXZG9kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTF5WVdScGJ5MXBkR1Z0TFdobGFXZG9kQ2s3WEc0Z0lDQWdiR2x1WlMxb1pXbG5hSFE2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10Y21Ga2FXOHRhWFJsYlMxb1pXbG5hSFFwTzF4dUlDQWdJRzFoY21kcGJqb2dNQ0JqWVd4aktDQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHVjSFYwTFhCaFpHUnBibWNwSUNvZ01pQXBJREFnTVhCNE8xeHVJQ0FnSUdac2IyRjBPaUJzWldaME8xeHVmVnh1WEc0dktpQkRiMnh2Y2lCUWFXTnJaWElnVTNSNWJHbHVaeUFxTDF4dVhHNHVkMmxrWjJWMExXTnZiRzl5Y0dsamEyVnlJSHRjYmlBZ0lDQjNhV1IwYURvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0ZDJsa2RHZ3BPMXh1SUNBZ0lHaGxhV2RvZERvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0YUdWcFoyaDBLVHRjYmlBZ0lDQnNhVzVsTFdobGFXZG9kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRhR1ZwWjJoMEtUdGNibjFjYmx4dUxuZHBaR2RsZEMxamIyeHZjbkJwWTJ0bGNpQStJQzUzYVdSblpYUXRZMjlzYjNKd2FXTnJaWEl0YVc1d2RYUWdlMXh1SUNBZ0lHWnNaWGd0WjNKdmR6b2dNVHRjYmlBZ0lDQm1iR1Y0TFhOb2NtbHVhem9nTVR0Y2JpQWdJQ0J0YVc0dGQybGtkR2c2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMWGRwWkhSb0xYUnBibmtwTzF4dWZWeHVYRzR1ZDJsa1oyVjBMV052Ykc5eWNHbGphMlZ5SUdsdWNIVjBXM1I1Y0dVOVhDSmpiMnh2Y2x3aVhTQjdYRzRnSUNBZ2QybGtkR2c2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1c2FXNWxMV2hsYVdkb2RDazdYRzRnSUNBZ2FHVnBaMmgwT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWJHbHVaUzFvWldsbmFIUXBPMXh1SUNBZ0lIQmhaR1JwYm1jNklEQWdNbkI0T3lBdktpQnRZV3RsSUhSb1pTQmpiMnh2Y2lCemNYVmhjbVVnWVdOMGRXRnNiSGtnYzNGMVlYSmxJRzl1SUVOb2NtOXRaU0J2YmlCUFV5QllJQ292WEc0Z0lDQWdZbUZqYTJkeWIzVnVaRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMWlZV05yWjNKdmRXNWtMV052Ykc5eUtUdGNiaUFnSUNCamIyeHZjam9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMWpiMnh2Y2lrN1hHNGdJQ0FnWW05eVpHVnlPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1Y0hWMExXSnZjbVJsY2kxM2FXUjBhQ2tnYzI5c2FXUWdkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibkIxZEMxaWIzSmtaWEl0WTI5c2IzSXBPMXh1SUNBZ0lHSnZjbVJsY2kxc1pXWjBPaUJ1YjI1bE8xeHVJQ0FnSUdac1pYZ3RaM0p2ZHpvZ01EdGNiaUFnSUNCbWJHVjRMWE5vY21sdWF6b2dNRHRjYmlBZ0lDQmliM2d0YzJsNmFXNW5PaUJpYjNKa1pYSXRZbTk0TzF4dUlDQWdJR0ZzYVdkdUxYTmxiR1k2SUhOMGNtVjBZMmc3WEc0Z0lDQWdiM1YwYkdsdVpUb2dibTl1WlNBaGFXMXdiM0owWVc1ME8xeHVmVnh1WEc0dWQybGtaMlYwTFdOdmJHOXljR2xqYTJWeUxtTnZibU5wYzJVZ2FXNXdkWFJiZEhsd1pUMWNJbU52Ykc5eVhDSmRJSHRjYmlBZ0lDQmliM0prWlhJdGJHVm1kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMWliM0prWlhJdGQybGtkR2dwSUhOdmJHbGtJSFpoY2lndExXcHdMWGRwWkdkbGRITXRhVzV3ZFhRdFltOXlaR1Z5TFdOdmJHOXlLVHRjYm4xY2JseHVMbmRwWkdkbGRDMWpiMnh2Y25CcFkydGxjaUJwYm5CMWRGdDBlWEJsUFZ3aVkyOXNiM0pjSWwwNlptOWpkWE1zSUM1M2FXUm5aWFF0WTI5c2IzSndhV05yWlhJZ2FXNXdkWFJiZEhsd1pUMWNJblJsZUhSY0lsMDZabTlqZFhNZ2UxeHVJQ0FnSUdKdmNtUmxjaTFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibkIxZEMxbWIyTjFjeTFpYjNKa1pYSXRZMjlzYjNJcE8xeHVmVnh1WEc0dWQybGtaMlYwTFdOdmJHOXljR2xqYTJWeUlHbHVjSFYwVzNSNWNHVTlYQ0owWlhoMFhDSmRJSHRjYmlBZ0lDQm1iR1Y0TFdkeWIzYzZJREU3WEc0Z0lDQWdiM1YwYkdsdVpUb2dibTl1WlNBaGFXMXdiM0owWVc1ME8xeHVJQ0FnSUdobGFXZG9kRG9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm14cGJtVXRhR1ZwWjJoMEtUdGNiaUFnSUNCc2FXNWxMV2hsYVdkb2REb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGFHVnBaMmgwS1R0Y2JpQWdJQ0JpWVdOclozSnZkVzVrT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWNIVjBMV0poWTJ0bmNtOTFibVF0WTI5c2IzSXBPMXh1SUNBZ0lHTnZiRzl5T2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWNIVjBMV052Ykc5eUtUdGNiaUFnSUNCaWIzSmtaWEk2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10YVc1d2RYUXRZbTl5WkdWeUxYZHBaSFJvS1NCemIyeHBaQ0IyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1Y0hWMExXSnZjbVJsY2kxamIyeHZjaWs3WEc0Z0lDQWdabTl1ZEMxemFYcGxPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV1p2Ym5RdGMybDZaU2s3WEc0Z0lDQWdjR0ZrWkdsdVp6b2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibkIxZEMxd1lXUmthVzVuS1NCallXeGpLQ0IyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1Y0hWMExYQmhaR1JwYm1jcElDb2dJRElnS1R0Y2JpQWdJQ0J0YVc0dGQybGtkR2c2SURBN0lDOHFJRlJvYVhNZ2JXRnJaWE1nYVhRZ2NHOXpjMmxpYkdVZ1ptOXlJSFJvWlNCbWJHVjRZbTk0SUhSdklITm9jbWx1YXlCMGFHbHpJR2x1Y0hWMElDb3ZYRzRnSUNBZ1pteGxlQzF6YUhKcGJtczZJREU3WEc0Z0lDQWdZbTk0TFhOcGVtbHVaem9nWW05eVpHVnlMV0p2ZUR0Y2JuMWNibHh1TG5kcFpHZGxkQzFqYjJ4dmNuQnBZMnRsY2lCcGJuQjFkRnQwZVhCbFBWd2lkR1Y0ZEZ3aVhUcGthWE5oWW14bFpDQjdYRzRnSUNBZ2IzQmhZMmwwZVRvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxa2FYTmhZbXhsWkMxdmNHRmphWFI1S1R0Y2JuMWNibHh1THlvZ1JHRjBaU0JRYVdOclpYSWdVM1I1YkdsdVp5QXFMMXh1WEc0dWQybGtaMlYwTFdSaGRHVndhV05yWlhJZ2UxeHVJQ0FnSUhkcFpIUm9PaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMTNhV1IwYUNrN1hHNGdJQ0FnYUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2x1YkdsdVpTMW9aV2xuYUhRcE8xeHVJQ0FnSUd4cGJtVXRhR1ZwWjJoME9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHViR2x1WlMxb1pXbG5hSFFwTzF4dWZWeHVYRzR1ZDJsa1oyVjBMV1JoZEdWd2FXTnJaWElnYVc1d2RYUmJkSGx3WlQxY0ltUmhkR1ZjSWwwZ2UxeHVJQ0FnSUdac1pYZ3RaM0p2ZHpvZ01UdGNiaUFnSUNCbWJHVjRMWE5vY21sdWF6b2dNVHRjYmlBZ0lDQnRhVzR0ZDJsa2RHZzZJREE3SUM4cUlGUm9hWE1nYldGclpYTWdhWFFnY0c5emMybGliR1VnWm05eUlIUm9aU0JtYkdWNFltOTRJSFJ2SUhOb2NtbHVheUIwYUdseklHbHVjSFYwSUNvdlhHNGdJQ0FnYjNWMGJHbHVaVG9nYm05dVpTQWhhVzF3YjNKMFlXNTBPMXh1SUNBZ0lHaGxhV2RvZERvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxcGJteHBibVV0YUdWcFoyaDBLVHRjYmlBZ0lDQmliM0prWlhJNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFXNXdkWFF0WW05eVpHVnlMWGRwWkhSb0tTQnpiMnhwWkNCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWNIVjBMV0p2Y21SbGNpMWpiMnh2Y2lrN1hHNGdJQ0FnWW1GamEyZHliM1Z1WkMxamIyeHZjam9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMWlZV05yWjNKdmRXNWtMV052Ykc5eUtUdGNiaUFnSUNCamIyeHZjam9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMWpiMnh2Y2lrN1hHNGdJQ0FnWm05dWRDMXphWHBsT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdadmJuUXRjMmw2WlNrN1hHNGdJQ0FnY0dGa1pHbHVaem9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMXdZV1JrYVc1bktTQmpZV3hqS0NCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdsdWNIVjBMWEJoWkdScGJtY3BJQ29nSURJZ0tUdGNiaUFnSUNCaWIzZ3RjMmw2YVc1bk9pQmliM0prWlhJdFltOTRPMXh1ZlZ4dVhHNHVkMmxrWjJWMExXUmhkR1Z3YVdOclpYSWdhVzV3ZFhSYmRIbHdaVDFjSW1SaGRHVmNJbDA2Wm05amRYTWdlMXh1SUNBZ0lHSnZjbVJsY2kxamIyeHZjam9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFwYm5CMWRDMW1iMk4xY3kxaWIzSmtaWEl0WTI5c2IzSXBPMXh1ZlZ4dVhHNHVkMmxrWjJWMExXUmhkR1Z3YVdOclpYSWdhVzV3ZFhSYmRIbHdaVDFjSW1SaGRHVmNJbDA2YVc1MllXeHBaQ0I3WEc0Z0lDQWdZbTl5WkdWeUxXTnZiRzl5T2lCMllYSW9MUzFxY0MxM1lYSnVMV052Ykc5eU1TazdYRzU5WEc1Y2JpNTNhV1JuWlhRdFpHRjBaWEJwWTJ0bGNpQnBibkIxZEZ0MGVYQmxQVndpWkdGMFpWd2lYVHBrYVhOaFlteGxaQ0I3WEc0Z0lDQWdiM0JoWTJsMGVUb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MWthWE5oWW14bFpDMXZjR0ZqYVhSNUtUdGNibjFjYmx4dUx5b2dVR3hoZVNCWGFXUm5aWFFnS2k5Y2JseHVMbmRwWkdkbGRDMXdiR0Y1SUh0Y2JpQWdJQ0IzYVdSMGFEb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MXBibXhwYm1VdGQybGtkR2d0YzJodmNuUXBPMXh1SUNBZ0lHUnBjM0JzWVhrNklHWnNaWGc3WEc0Z0lDQWdZV3hwWjI0dGFYUmxiWE02SUhOMGNtVjBZMmc3WEc1OVhHNWNiaTUzYVdSblpYUXRjR3hoZVNBdWFuVndlWFJsY2kxaWRYUjBiMjRnZTF4dUlDQWdJR1pzWlhndFozSnZkem9nTVR0Y2JpQWdJQ0JvWldsbmFIUTZJR0YxZEc4N1hHNTlYRzVjYmk1M2FXUm5aWFF0Y0d4aGVTQXVhblZ3ZVhSbGNpMWlkWFIwYjI0NlpHbHpZV0pzWldRZ2UxeHVJQ0FnSUc5d1lXTnBkSGs2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10WkdsellXSnNaV1F0YjNCaFkybDBlU2s3WEc1OVhHNWNiaThxSUZSaFlpQlhhV1JuWlhRZ0tpOWNibHh1TG1wMWNIbDBaWEl0ZDJsa1oyVjBjeTUzYVdSblpYUXRkR0ZpSUh0Y2JpQWdJQ0JrYVhOd2JHRjVPaUJtYkdWNE8xeHVJQ0FnSUdac1pYZ3RaR2x5WldOMGFXOXVPaUJqYjJ4MWJXNDdYRzU5WEc1Y2JpNXFkWEI1ZEdWeUxYZHBaR2RsZEhNdWQybGtaMlYwTFhSaFlpQStJQzV3TFZSaFlrSmhjaUI3WEc0Z0lDQWdMeW9nVG1WalpYTnpZWEo1SUhOdklIUm9ZWFFnWVNCMFlXSWdZMkZ1SUdKbElITm9hV1owWldRZ1pHOTNiaUIwYnlCdmRtVnliR0Y1SUhSb1pTQmliM0prWlhJZ2IyWWdkR2hsSUdKdmVDQmlaV3h2ZHk0Z0tpOWNiaUFnSUNCdmRtVnlabXh2ZHkxNE9pQjJhWE5wWW14bE8xeHVJQ0FnSUc5MlpYSm1iRzkzTFhrNklIWnBjMmxpYkdVN1hHNTlYRzVjYmk1cWRYQjVkR1Z5TFhkcFpHZGxkSE11ZDJsa1oyVjBMWFJoWWlBK0lDNXdMVlJoWWtKaGNpQStJQzV3TFZSaFlrSmhjaTFqYjI1MFpXNTBJSHRjYmlBZ0lDQXZLaUJOWVd0bElITjFjbVVnZEdoaGRDQjBhR1VnZEdGaUlHZHliM2R6SUdaeWIyMGdZbTkwZEc5dElIVndJQ292WEc0Z0lDQWdZV3hwWjI0dGFYUmxiWE02SUdac1pYZ3RaVzVrTzF4dUlDQWdJRzFwYmkxM2FXUjBhRG9nTUR0Y2JpQWdJQ0J0YVc0dGFHVnBaMmgwT2lBd08xeHVmVnh1WEc0dWFuVndlWFJsY2kxM2FXUm5aWFJ6TG5kcFpHZGxkQzEwWVdJZ1BpQXVkMmxrWjJWMExYUmhZaTFqYjI1MFpXNTBjeUI3WEc0Z0lDQWdkMmxrZEdnNklERXdNQ1U3WEc0Z0lDQWdZbTk0TFhOcGVtbHVaem9nWW05eVpHVnlMV0p2ZUR0Y2JpQWdJQ0J0WVhKbmFXNDZJREE3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaRG9nZG1GeUtDMHRhbkF0YkdGNWIzVjBMV052Ykc5eU1TazdYRzRnSUNBZ1kyOXNiM0k2SUhaaGNpZ3RMV3B3TFhWcExXWnZiblF0WTI5c2IzSXhLVHRjYmlBZ0lDQmliM0prWlhJNklIWmhjaWd0TFdwd0xXSnZjbVJsY2kxM2FXUjBhQ2tnYzI5c2FXUWdkbUZ5S0MwdGFuQXRZbTl5WkdWeUxXTnZiRzl5TVNrN1hHNGdJQ0FnY0dGa1pHbHVaem9nZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFqYjI1MFlXbHVaWEl0Y0dGa1pHbHVaeWs3WEc0Z0lDQWdabXhsZUMxbmNtOTNPaUF4TzF4dUlDQWdJRzkyWlhKbWJHOTNPaUJoZFhSdk8xeHVmVnh1WEc0dWFuVndlWFJsY2kxM2FXUm5aWFJ6TG5kcFpHZGxkQzEwWVdJZ1BpQXVjQzFVWVdKQ1lYSWdlMXh1SUNBZ0lHWnZiblE2SUhaaGNpZ3RMV3B3TFhkcFpHZGxkSE10Wm05dWRDMXphWHBsS1NCSVpXeDJaWFJwWTJFc0lFRnlhV0ZzTENCellXNXpMWE5sY21sbU8xeHVJQ0FnSUcxcGJpMW9aV2xuYUhRNklHTmhiR01vZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFvYjNKcGVtOXVkR0ZzTFhSaFlpMW9aV2xuYUhRcElDc2dkbUZ5S0MwdGFuQXRZbTl5WkdWeUxYZHBaSFJvS1NrN1hHNTlYRzVjYmk1cWRYQjVkR1Z5TFhkcFpHZGxkSE11ZDJsa1oyVjBMWFJoWWlBK0lDNXdMVlJoWWtKaGNpQXVjQzFVWVdKQ1lYSXRkR0ZpSUh0Y2JpQWdJQ0JtYkdWNE9pQXdJREVnZG1GeUtDMHRhbkF0ZDJsa1oyVjBjeTFvYjNKcGVtOXVkR0ZzTFhSaFlpMTNhV1IwYUNrN1hHNGdJQ0FnYldsdUxYZHBaSFJvT2lBek5YQjRPMXh1SUNBZ0lHMXBiaTFvWldsbmFIUTZJR05oYkdNb2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxb2IzSnBlbTl1ZEdGc0xYUmhZaTFvWldsbmFIUXBJQ3NnZG1GeUtDMHRhbkF0WW05eVpHVnlMWGRwWkhSb0tTazdYRzRnSUNBZ2JHbHVaUzFvWldsbmFIUTZJSFpoY2lndExXcHdMWGRwWkdkbGRITXRhRzl5YVhwdmJuUmhiQzEwWVdJdGFHVnBaMmgwS1R0Y2JpQWdJQ0J0WVhKbmFXNHRiR1ZtZERvZ1kyRnNZeWd0TVNBcUlIWmhjaWd0TFdwd0xXSnZjbVJsY2kxM2FXUjBhQ2twTzF4dUlDQWdJSEJoWkdScGJtYzZJREJ3ZUNBeE1IQjRPMXh1SUNBZ0lHSmhZMnRuY205MWJtUTZJSFpoY2lndExXcHdMV3hoZVc5MWRDMWpiMnh2Y2pJcE8xeHVJQ0FnSUdOdmJHOXlPaUIyWVhJb0xTMXFjQzExYVMxbWIyNTBMV052Ykc5eU1pazdYRzRnSUNBZ1ltOXlaR1Z5T2lCMllYSW9MUzFxY0MxaWIzSmtaWEl0ZDJsa2RHZ3BJSE52Ykdsa0lIWmhjaWd0TFdwd0xXSnZjbVJsY2kxamIyeHZjakVwTzF4dUlDQWdJR0p2Y21SbGNpMWliM1IwYjIwNklHNXZibVU3WEc0Z0lDQWdjRzl6YVhScGIyNDZJSEpsYkdGMGFYWmxPMXh1ZlZ4dVhHNHVhblZ3ZVhSbGNpMTNhV1JuWlhSekxuZHBaR2RsZEMxMFlXSWdQaUF1Y0MxVVlXSkNZWElnTG5BdFZHRmlRbUZ5TFhSaFlpNXdMVzF2WkMxamRYSnlaVzUwSUh0Y2JpQWdJQ0JqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRkV2t0Wm05dWRDMWpiMnh2Y2pBcE8xeHVJQ0FnSUM4cUlGZGxJSGRoYm5RZ2RHaGxJR0poWTJ0bmNtOTFibVFnZEc4Z2JXRjBZMmdnZEdobElIUmhZaUJqYjI1MFpXNTBJR0poWTJ0bmNtOTFibVFnS2k5Y2JpQWdJQ0JpWVdOclozSnZkVzVrT2lCMllYSW9MUzFxY0Mxc1lYbHZkWFF0WTI5c2IzSXhLVHRjYmlBZ0lDQnRhVzR0YUdWcFoyaDBPaUJqWVd4aktIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdGFHOXlhWHB2Ym5SaGJDMTBZV0l0YUdWcFoyaDBLU0FySURJZ0tpQjJZWElvTFMxcWNDMWliM0prWlhJdGQybGtkR2dwS1R0Y2JpQWdJQ0IwY21GdWMyWnZjbTA2SUhSeVlXNXpiR0YwWlZrb2RtRnlLQzB0YW5BdFltOXlaR1Z5TFhkcFpIUm9LU2s3WEc0Z0lDQWdiM1psY21ac2IzYzZJSFpwYzJsaWJHVTdYRzU5WEc1Y2JpNXFkWEI1ZEdWeUxYZHBaR2RsZEhNdWQybGtaMlYwTFhSaFlpQStJQzV3TFZSaFlrSmhjaUF1Y0MxVVlXSkNZWEl0ZEdGaUxuQXRiVzlrTFdOMWNuSmxiblE2WW1WbWIzSmxJSHRjYmlBZ0lDQndiM05wZEdsdmJqb2dZV0p6YjJ4MWRHVTdYRzRnSUNBZ2RHOXdPaUJqWVd4aktDMHhJQ29nZG1GeUtDMHRhbkF0WW05eVpHVnlMWGRwWkhSb0tTazdYRzRnSUNBZ2JHVm1kRG9nWTJGc1l5Z3RNU0FxSUhaaGNpZ3RMV3B3TFdKdmNtUmxjaTEzYVdSMGFDa3BPMXh1SUNBZ0lHTnZiblJsYm5RNklDY25PMXh1SUNBZ0lHaGxhV2RvZERvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxb2IzSnBlbTl1ZEdGc0xYUmhZaTEwYjNBdFltOXlaR1Z5S1R0Y2JpQWdJQ0IzYVdSMGFEb2dZMkZzWXlneE1EQWxJQ3NnTWlBcUlIWmhjaWd0TFdwd0xXSnZjbVJsY2kxM2FXUjBhQ2twTzF4dUlDQWdJR0poWTJ0bmNtOTFibVE2SUhaaGNpZ3RMV3B3TFdKeVlXNWtMV052Ykc5eU1TazdYRzU5WEc1Y2JpNXFkWEI1ZEdWeUxYZHBaR2RsZEhNdWQybGtaMlYwTFhSaFlpQStJQzV3TFZSaFlrSmhjaUF1Y0MxVVlXSkNZWEl0ZEdGaU9tWnBjbk4wTFdOb2FXeGtJSHRjYmlBZ0lDQnRZWEpuYVc0dGJHVm1kRG9nTUR0Y2JuMWNibHh1TG1wMWNIbDBaWEl0ZDJsa1oyVjBjeTUzYVdSblpYUXRkR0ZpSUQ0Z0xuQXRWR0ZpUW1GeUlDNXdMVlJoWWtKaGNpMTBZV0k2YUc5MlpYSTZibTkwS0M1d0xXMXZaQzFqZFhKeVpXNTBLU0I3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaRG9nZG1GeUtDMHRhbkF0YkdGNWIzVjBMV052Ykc5eU1TazdYRzRnSUNBZ1kyOXNiM0k2SUhaaGNpZ3RMV3B3TFhWcExXWnZiblF0WTI5c2IzSXhLVHRjYm4xY2JseHVMbXAxY0hsMFpYSXRkMmxrWjJWMGN5NTNhV1JuWlhRdGRHRmlJRDRnTG5BdFZHRmlRbUZ5SUM1d0xXMXZaQzFqYkc5ellXSnNaU0ErSUM1d0xWUmhZa0poY2kxMFlXSkRiRzl6WlVsamIyNGdlMXh1SUNBZ0lHMWhjbWRwYmkxc1pXWjBPaUEwY0hnN1hHNTlYRzVjYmk1cWRYQjVkR1Z5TFhkcFpHZGxkSE11ZDJsa1oyVjBMWFJoWWlBK0lDNXdMVlJoWWtKaGNpQXVjQzF0YjJRdFkyeHZjMkZpYkdVZ1BpQXVjQzFVWVdKQ1lYSXRkR0ZpUTJ4dmMyVkpZMjl1T21KbFptOXlaU0I3WEc0Z0lDQWdabTl1ZEMxbVlXMXBiSGs2SUVadmJuUkJkMlZ6YjIxbE8xeHVJQ0FnSUdOdmJuUmxiblE2SUNkY1hHWXdNR1FuT3lBdktpQmpiRzl6WlNBcUwxeHVmVnh1WEc0dWFuVndlWFJsY2kxM2FXUm5aWFJ6TG5kcFpHZGxkQzEwWVdJZ1BpQXVjQzFVWVdKQ1lYSWdMbkF0VkdGaVFtRnlMWFJoWWtsamIyNHNYRzR1YW5Wd2VYUmxjaTEzYVdSblpYUnpMbmRwWkdkbGRDMTBZV0lnUGlBdWNDMVVZV0pDWVhJZ0xuQXRWR0ZpUW1GeUxYUmhZa3hoWW1Wc0xGeHVMbXAxY0hsMFpYSXRkMmxrWjJWMGN5NTNhV1JuWlhRdGRHRmlJRDRnTG5BdFZHRmlRbUZ5SUM1d0xWUmhZa0poY2kxMFlXSkRiRzl6WlVsamIyNGdlMXh1SUNBZ0lHeHBibVV0YUdWcFoyaDBPaUIyWVhJb0xTMXFjQzEzYVdSblpYUnpMV2h2Y21sNmIyNTBZV3d0ZEdGaUxXaGxhV2RvZENrN1hHNTlYRzVjYmk4cUlFRmpZMjl5WkdsdmJpQlhhV1JuWlhRZ0tpOWNibHh1TG5BdFEyOXNiR0Z3YzJVZ2UxeHVJQ0FnSUdScGMzQnNZWGs2SUdac1pYZzdYRzRnSUNBZ1pteGxlQzFrYVhKbFkzUnBiMjQ2SUdOdmJIVnRianRjYmlBZ0lDQmhiR2xuYmkxcGRHVnRjem9nYzNSeVpYUmphRHRjYm4xY2JseHVMbkF0UTI5c2JHRndjMlV0YUdWaFpHVnlJSHRjYmlBZ0lDQndZV1JrYVc1bk9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHVjSFYwTFhCaFpHUnBibWNwTzF4dUlDQWdJR04xY25OdmNqb2djRzlwYm5SbGNqdGNiaUFnSUNCamIyeHZjam9nZG1GeUtDMHRhbkF0ZFdrdFptOXVkQzFqYjJ4dmNqSXBPMXh1SUNBZ0lHSmhZMnRuY205MWJtUXRZMjlzYjNJNklIWmhjaWd0TFdwd0xXeGhlVzkxZEMxamIyeHZjaklwTzF4dUlDQWdJR0p2Y21SbGNqb2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MWliM0prWlhJdGQybGtkR2dwSUhOdmJHbGtJSFpoY2lndExXcHdMV0p2Y21SbGNpMWpiMnh2Y2pFcE8xeHVJQ0FnSUhCaFpHUnBibWM2SUdOaGJHTW9kbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MWpiMjUwWVdsdVpYSXRjR0ZrWkdsdVp5a2dLaUF5SUM4Z015a2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MWpiMjUwWVdsdVpYSXRjR0ZrWkdsdVp5azdYRzRnSUNBZ1ptOXVkQzEzWldsbmFIUTZJR0p2YkdRN1hHNTlYRzVjYmk1d0xVTnZiR3hoY0hObExXaGxZV1JsY2pwb2IzWmxjaUI3WEc0Z0lDQWdZbUZqYTJkeWIzVnVaQzFqYjJ4dmNqb2dkbUZ5S0MwdGFuQXRiR0Y1YjNWMExXTnZiRzl5TVNrN1hHNGdJQ0FnWTI5c2IzSTZJSFpoY2lndExXcHdMWFZwTFdadmJuUXRZMjlzYjNJeEtUdGNibjFjYmx4dUxuQXRRMjlzYkdGd2MyVXRiM0JsYmlBK0lDNXdMVU52Ykd4aGNITmxMV2hsWVdSbGNpQjdYRzRnSUNBZ1ltRmphMmR5YjNWdVpDMWpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGJHRjViM1YwTFdOdmJHOXlNU2s3WEc0Z0lDQWdZMjlzYjNJNklIWmhjaWd0TFdwd0xYVnBMV1p2Ym5RdFkyOXNiM0l3S1R0Y2JpQWdJQ0JqZFhKemIzSTZJR1JsWm1GMWJIUTdYRzRnSUNBZ1ltOXlaR1Z5TFdKdmRIUnZiVG9nYm05dVpUdGNibjFjYmx4dUxuQXRRMjlzYkdGd2MyVWdMbkF0UTI5c2JHRndjMlV0YUdWaFpHVnlPanBpWldadmNtVWdlMXh1SUNBZ0lHTnZiblJsYm5RNklDZGNYR1l3WkdGY1hEQXdRVEFuT3lBZ0x5b2dZMkZ5WlhRdGNtbG5hSFFzSUc1dmJpMWljbVZoYTJsdVp5QnpjR0ZqWlNBcUwxeHVJQ0FnSUdScGMzQnNZWGs2SUdsdWJHbHVaUzFpYkc5amF6dGNiaUFnSUNCbWIyNTBPaUJ1YjNKdFlXd2dibTl5YldGc0lHNXZjbTFoYkNBeE5IQjRMekVnUm05dWRFRjNaWE52YldVN1hHNGdJQ0FnWm05dWRDMXphWHBsT2lCcGJtaGxjbWwwTzF4dUlDQWdJSFJsZUhRdGNtVnVaR1Z5YVc1bk9pQmhkWFJ2TzF4dUlDQWdJQzEzWldKcmFYUXRabTl1ZEMxemJXOXZkR2hwYm1jNklHRnVkR2xoYkdsaGMyVmtPMXh1SUNBZ0lDMXRiM290YjNONExXWnZiblF0YzIxdmIzUm9hVzVuT2lCbmNtRjVjMk5oYkdVN1hHNTlYRzVjYmk1d0xVTnZiR3hoY0hObExXOXdaVzRnUGlBdWNDMURiMnhzWVhCelpTMW9aV0ZrWlhJNk9tSmxabTl5WlNCN1hHNGdJQ0FnWTI5dWRHVnVkRG9nSjF4Y1pqQmtOMXhjTURCQk1DYzdJQzhxSUdOaGNtVjBMV1J2ZDI0c0lHNXZiaTFpY21WaGEybHVaeUJ6Y0dGalpTQXFMMXh1ZlZ4dVhHNHVjQzFEYjJ4c1lYQnpaUzFqYjI1MFpXNTBjeUI3WEc0Z0lDQWdjR0ZrWkdsdVp6b2dkbUZ5S0MwdGFuQXRkMmxrWjJWMGN5MWpiMjUwWVdsdVpYSXRjR0ZrWkdsdVp5azdYRzRnSUNBZ1ltRmphMmR5YjNWdVpDMWpiMnh2Y2pvZ2RtRnlLQzB0YW5BdGJHRjViM1YwTFdOdmJHOXlNU2s3WEc0Z0lDQWdZMjlzYjNJNklIWmhjaWd0TFdwd0xYVnBMV1p2Ym5RdFkyOXNiM0l4S1R0Y2JpQWdJQ0JpYjNKa1pYSXRiR1ZtZERvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxaWIzSmtaWEl0ZDJsa2RHZ3BJSE52Ykdsa0lIWmhjaWd0TFdwd0xXSnZjbVJsY2kxamIyeHZjakVwTzF4dUlDQWdJR0p2Y21SbGNpMXlhV2RvZERvZ2RtRnlLQzB0YW5BdGQybGtaMlYwY3kxaWIzSmtaWEl0ZDJsa2RHZ3BJSE52Ykdsa0lIWmhjaWd0TFdwd0xXSnZjbVJsY2kxamIyeHZjakVwTzF4dUlDQWdJR0p2Y21SbGNpMWliM1IwYjIwNklIWmhjaWd0TFdwd0xYZHBaR2RsZEhNdFltOXlaR1Z5TFhkcFpIUm9LU0J6YjJ4cFpDQjJZWElvTFMxcWNDMWliM0prWlhJdFkyOXNiM0l4S1R0Y2JpQWdJQ0J2ZG1WeVpteHZkem9nWVhWMGJ6dGNibjFjYmx4dUxuQXRRV05qYjNKa2FXOXVJSHRjYmlBZ0lDQmthWE53YkdGNU9pQm1iR1Y0TzF4dUlDQWdJR1pzWlhndFpHbHlaV04wYVc5dU9pQmpiMngxYlc0N1hHNGdJQ0FnWVd4cFoyNHRhWFJsYlhNNklITjBjbVYwWTJnN1hHNTlYRzVjYmk1d0xVRmpZMjl5WkdsdmJpQXVjQzFEYjJ4c1lYQnpaU0I3WEc0Z0lDQWdiV0Z5WjJsdUxXSnZkSFJ2YlRvZ01EdGNibjFjYmx4dUxuQXRRV05qYjNKa2FXOXVJQzV3TFVOdmJHeGhjSE5sSUNzZ0xuQXRRMjlzYkdGd2MyVWdlMXh1SUNBZ0lHMWhjbWRwYmkxMGIzQTZJRFJ3ZUR0Y2JuMWNibHh1WEc1Y2JpOHFJRWhVVFV3Z2QybGtaMlYwSUNvdlhHNWNiaTUzYVdSblpYUXRhSFJ0YkN3Z0xuZHBaR2RsZEMxb2RHMXNiV0YwYUNCN1hHNGdJQ0FnWm05dWRDMXphWHBsT2lCMllYSW9MUzFxY0MxM2FXUm5aWFJ6TFdadmJuUXRjMmw2WlNrN1hHNTlYRzVjYmk1M2FXUm5aWFF0YUhSdGJDQStJQzUzYVdSblpYUXRhSFJ0YkMxamIyNTBaVzUwTENBdWQybGtaMlYwTFdoMGJXeHRZWFJvSUQ0Z0xuZHBaR2RsZEMxb2RHMXNMV052Ym5SbGJuUWdlMXh1SUNBZ0lDOHFJRVpwYkd3Z2IzVjBJSFJvWlNCaGNtVmhJR2x1SUhSb1pTQklWRTFNSUhkcFpHZGxkQ0FxTDF4dUlDQWdJR0ZzYVdkdUxYTmxiR1k2SUhOMGNtVjBZMmc3WEc0Z0lDQWdabXhsZUMxbmNtOTNPaUF4TzF4dUlDQWdJR1pzWlhndGMyaHlhVzVyT2lBeE8xeHVJQ0FnSUM4cUlFMWhhMlZ6SUhOMWNtVWdkR2hsSUdKaGMyVnNhVzVsSUdseklITjBhV3hzSUdGc2FXZHVaV1FnZDJsMGFDQnZkR2hsY2lCbGJHVnRaVzUwY3lBcUwxeHVJQ0FnSUd4cGJtVXRhR1ZwWjJoME9pQjJZWElvTFMxcWNDMTNhV1JuWlhSekxXbHViR2x1WlMxb1pXbG5hSFFwTzF4dUlDQWdJQzhxSUUxaGEyVWdhWFFnY0c5emMybGliR1VnZEc4Z2FHRjJaU0JoWW5OdmJIVjBaV3g1TFhCdmMybDBhVzl1WldRZ1pXeGxiV1Z1ZEhNZ2FXNGdkR2hsSUdoMGJXd2dLaTljYmlBZ0lDQndiM05wZEdsdmJqb2djbVZzWVhScGRtVTdYRzU5WEc0aUxDSXZLaUJVYUdseklHWnBiR1VnYUdGeklHTnZaR1VnWkdWeWFYWmxaQ0JtY205dElGQm9iM053YUc5eVNsTWdRMU5USUdacGJHVnpMQ0JoY3lCdWIzUmxaQ0JpWld4dmR5NGdWR2hsSUd4cFkyVnVjMlVnWm05eUlIUm9hWE1nVUdodmMzQm9iM0pLVXlCamIyUmxJR2x6T2x4dVhHNURiM0I1Y21sbmFIUWdLR01wSURJd01UUXRNakF4Tnl3Z1VHaHZjM0JvYjNKS1V5QkRiMjUwY21saWRYUnZjbk5jYmtGc2JDQnlhV2RvZEhNZ2NtVnpaWEoyWldRdVhHNWNibEpsWkdsemRISnBZblYwYVc5dUlHRnVaQ0IxYzJVZ2FXNGdjMjkxY21ObElHRnVaQ0JpYVc1aGNua2dabTl5YlhNc0lIZHBkR2dnYjNJZ2QybDBhRzkxZEZ4dWJXOWthV1pwWTJGMGFXOXVMQ0JoY21VZ2NHVnliV2wwZEdWa0lIQnliM1pwWkdWa0lIUm9ZWFFnZEdobElHWnZiR3h2ZDJsdVp5QmpiMjVrYVhScGIyNXpJR0Z5WlNCdFpYUTZYRzVjYmlvZ1VtVmthWE4wY21saWRYUnBiMjV6SUc5bUlITnZkWEpqWlNCamIyUmxJRzExYzNRZ2NtVjBZV2x1SUhSb1pTQmhZbTkyWlNCamIzQjVjbWxuYUhRZ2JtOTBhV05sTENCMGFHbHpYRzRnSUd4cGMzUWdiMllnWTI5dVpHbDBhVzl1Y3lCaGJtUWdkR2hsSUdadmJHeHZkMmx1WnlCa2FYTmpiR0ZwYldWeUxseHVYRzRxSUZKbFpHbHpkSEpwWW5WMGFXOXVjeUJwYmlCaWFXNWhjbmtnWm05eWJTQnRkWE4wSUhKbGNISnZaSFZqWlNCMGFHVWdZV0p2ZG1VZ1kyOXdlWEpwWjJoMElHNXZkR2xqWlN4Y2JpQWdkR2hwY3lCc2FYTjBJRzltSUdOdmJtUnBkR2x2Ym5NZ1lXNWtJSFJvWlNCbWIyeHNiM2RwYm1jZ1pHbHpZMnhoYVcxbGNpQnBiaUIwYUdVZ1pHOWpkVzFsYm5SaGRHbHZibHh1SUNCaGJtUXZiM0lnYjNSb1pYSWdiV0YwWlhKcFlXeHpJSEJ5YjNacFpHVmtJSGRwZEdnZ2RHaGxJR1JwYzNSeWFXSjFkR2x2Ymk1Y2JseHVLaUJPWldsMGFHVnlJSFJvWlNCdVlXMWxJRzltSUhSb1pTQmpiM0I1Y21sbmFIUWdhRzlzWkdWeUlHNXZjaUIwYUdVZ2JtRnRaWE1nYjJZZ2FYUnpYRzRnSUdOdmJuUnlhV0oxZEc5eWN5QnRZWGtnWW1VZ2RYTmxaQ0IwYnlCbGJtUnZjbk5sSUc5eUlIQnliMjF2ZEdVZ2NISnZaSFZqZEhNZ1pHVnlhWFpsWkNCbWNtOXRYRzRnSUhSb2FYTWdjMjltZEhkaGNtVWdkMmwwYUc5MWRDQnpjR1ZqYVdacFl5QndjbWx2Y2lCM2NtbDBkR1Z1SUhCbGNtMXBjM05wYjI0dVhHNWNibFJJU1ZNZ1UwOUdWRmRCVWtVZ1NWTWdVRkpQVmtsRVJVUWdRbGtnVkVoRklFTlBVRmxTU1VkSVZDQklUMHhFUlZKVElFRk9SQ0JEVDA1VVVrbENWVlJQVWxNZ1hDSkJVeUJKVTF3aVhHNUJUa1FnUVU1WklFVllVRkpGVTFNZ1QxSWdTVTFRVEVsRlJDQlhRVkpTUVU1VVNVVlRMQ0JKVGtOTVZVUkpUa2NzSUVKVlZDQk9UMVFnVEVsTlNWUkZSQ0JVVHl3Z1ZFaEZYRzVKVFZCTVNVVkVJRmRCVWxKQlRsUkpSVk1nVDBZZ1RVVlNRMGhCVGxSQlFrbE1TVlJaSUVGT1JDQkdTVlJPUlZOVElFWlBVaUJCSUZCQlVsUkpRMVZNUVZJZ1VGVlNVRTlUUlNCQlVrVmNia1JKVTBOTVFVbE5SVVF1SUVsT0lFNVBJRVZXUlU1VUlGTklRVXhNSUZSSVJTQkRUMUJaVWtsSFNGUWdTRTlNUkVWU0lFOVNJRU5QVGxSU1NVSlZWRTlTVXlCQ1JTQk1TVUZDVEVWY2JrWlBVaUJCVGxrZ1JFbFNSVU5VTENCSlRrUkpVa1ZEVkN3Z1NVNURTVVJGVGxSQlRDd2dVMUJGUTBsQlRDd2dSVmhGVFZCTVFWSlpMQ0JQVWlCRFQwNVRSVkZWUlU1VVNVRk1YRzVFUVUxQlIwVlRJQ2hKVGtOTVZVUkpUa2NzSUVKVlZDQk9UMVFnVEVsTlNWUkZSQ0JVVHl3Z1VGSlBRMVZTUlUxRlRsUWdUMFlnVTFWQ1UxUkpWRlZVUlNCSFQwOUVVeUJQVWx4dVUwVlNWa2xEUlZNN0lFeFBVMU1nVDBZZ1ZWTkZMQ0JFUVZSQkxDQlBVaUJRVWs5R1NWUlRPeUJQVWlCQ1ZWTkpUa1ZUVXlCSlRsUkZVbEpWVUZSSlQwNHBJRWhQVjBWV1JWSmNia05CVlZORlJDQkJUa1FnVDA0Z1FVNVpJRlJJUlU5U1dTQlBSaUJNU1VGQ1NVeEpWRmtzSUZkSVJWUklSVklnU1U0Z1EwOU9WRkpCUTFRc0lGTlVVa2xEVkNCTVNVRkNTVXhKVkZrc1hHNVBVaUJVVDFKVUlDaEpUa05NVlVSSlRrY2dUa1ZIVEVsSFJVNURSU0JQVWlCUFZFaEZVbGRKVTBVcElFRlNTVk5KVGtjZ1NVNGdRVTVaSUZkQldTQlBWVlFnVDBZZ1ZFaEZJRlZUUlZ4dVQwWWdWRWhKVXlCVFQwWlVWMEZTUlN3Z1JWWkZUaUJKUmlCQlJGWkpVMFZFSUU5R0lGUklSU0JRVDFOVFNVSkpURWxVV1NCUFJpQlRWVU5JSUVSQlRVRkhSUzVjYmx4dUtpOWNibHh1THlwY2JpQXFJRlJvWlNCbWIyeHNiM2RwYm1jZ2MyVmpkR2x2YmlCcGN5QmtaWEpwZG1Wa0lHWnliMjBnYUhSMGNITTZMeTluYVhSb2RXSXVZMjl0TDNCb2IzTndhRzl5YW5NdmNHaHZjM0JvYjNJdllteHZZaTh5TTJJNVpEQTNOV1ZpWXpWaU56TmhZakUwT0dJMlpXSm1Zekl3WVdZNU4yWTROVGN4TkdNMEwzQmhZMnRoWjJWekwzZHBaR2RsZEhNdmMzUjViR1V2ZEdGaVltRnlMbU56Y3lCY2JpQXFJRmRsSjNabElITmpiM0JsWkNCMGFHVWdjblZzWlhNZ2MyOGdkR2hoZENCMGFHVjVJR0Z5WlNCamIyNXphWE4wWlc1MElIZHBkR2dnWlhoaFkzUnNlU0J2ZFhJZ1kyOWtaUzVjYmlBcUwxeHVYRzR1YW5Wd2VYUmxjaTEzYVdSblpYUnpMbmRwWkdkbGRDMTBZV0lnUGlBdWNDMVVZV0pDWVhJZ2UxeHVJQ0JrYVhOd2JHRjVPaUJtYkdWNE8xeHVJQ0F0ZDJWaWEybDBMWFZ6WlhJdGMyVnNaV04wT2lCdWIyNWxPMXh1SUNBdGJXOTZMWFZ6WlhJdGMyVnNaV04wT2lCdWIyNWxPMXh1SUNBdGJYTXRkWE5sY2kxelpXeGxZM1E2SUc1dmJtVTdYRzRnSUhWelpYSXRjMlZzWldOME9pQnViMjVsTzF4dWZWeHVYRzVjYmk1cWRYQjVkR1Z5TFhkcFpHZGxkSE11ZDJsa1oyVjBMWFJoWWlBK0lDNXdMVlJoWWtKaGNsdGtZWFJoTFc5eWFXVnVkR0YwYVc5dVBTZG9iM0pwZW05dWRHRnNKMTBnZTF4dUlDQm1iR1Y0TFdScGNtVmpkR2x2YmpvZ2NtOTNPMXh1ZlZ4dVhHNWNiaTVxZFhCNWRHVnlMWGRwWkdkbGRITXVkMmxrWjJWMExYUmhZaUErSUM1d0xWUmhZa0poY2x0a1lYUmhMVzl5YVdWdWRHRjBhVzl1UFNkMlpYSjBhV05oYkNkZElIdGNiaUFnWm14bGVDMWthWEpsWTNScGIyNDZJR052YkhWdGJqdGNibjFjYmx4dVhHNHVhblZ3ZVhSbGNpMTNhV1JuWlhSekxuZHBaR2RsZEMxMFlXSWdQaUF1Y0MxVVlXSkNZWElnUGlBdWNDMVVZV0pDWVhJdFkyOXVkR1Z1ZENCN1hHNGdJRzFoY21kcGJqb2dNRHRjYmlBZ2NHRmtaR2x1WnpvZ01EdGNiaUFnWkdsemNHeGhlVG9nWm14bGVEdGNiaUFnWm14bGVEb2dNU0F4SUdGMWRHODdYRzRnSUd4cGMzUXRjM1I1YkdVdGRIbHdaVG9nYm05dVpUdGNibjFjYmx4dVhHNHVhblZ3ZVhSbGNpMTNhV1JuWlhSekxuZHBaR2RsZEMxMFlXSWdQaUF1Y0MxVVlXSkNZWEpiWkdGMFlTMXZjbWxsYm5SaGRHbHZiajBuYUc5eWFYcHZiblJoYkNkZElENGdMbkF0VkdGaVFtRnlMV052Ym5SbGJuUWdlMXh1SUNCbWJHVjRMV1JwY21WamRHbHZiam9nY205M08xeHVmVnh1WEc1Y2JpNXFkWEI1ZEdWeUxYZHBaR2RsZEhNdWQybGtaMlYwTFhSaFlpQStJQzV3TFZSaFlrSmhjbHRrWVhSaExXOXlhV1Z1ZEdGMGFXOXVQU2QyWlhKMGFXTmhiQ2RkSUQ0Z0xuQXRWR0ZpUW1GeUxXTnZiblJsYm5RZ2UxeHVJQ0JtYkdWNExXUnBjbVZqZEdsdmJqb2dZMjlzZFcxdU8xeHVmVnh1WEc1Y2JpNXFkWEI1ZEdWeUxYZHBaR2RsZEhNdWQybGtaMlYwTFhSaFlpQStJQzV3TFZSaFlrSmhjaUF1Y0MxVVlXSkNZWEl0ZEdGaUlIdGNiaUFnWkdsemNHeGhlVG9nWm14bGVEdGNiaUFnWm14bGVDMWthWEpsWTNScGIyNDZJSEp2ZHp0Y2JpQWdZbTk0TFhOcGVtbHVaem9nWW05eVpHVnlMV0p2ZUR0Y2JpQWdiM1psY21ac2IzYzZJR2hwWkdSbGJqdGNibjFjYmx4dVhHNHVhblZ3ZVhSbGNpMTNhV1JuWlhSekxuZHBaR2RsZEMxMFlXSWdQaUF1Y0MxVVlXSkNZWElnTG5BdFZHRmlRbUZ5TFhSaFlrbGpiMjRzWEc0dWFuVndlWFJsY2kxM2FXUm5aWFJ6TG5kcFpHZGxkQzEwWVdJZ1BpQXVjQzFVWVdKQ1lYSWdMbkF0VkdGaVFtRnlMWFJoWWtOc2IzTmxTV052YmlCN1hHNGdJR1pzWlhnNklEQWdNQ0JoZFhSdk8xeHVmVnh1WEc1Y2JpNXFkWEI1ZEdWeUxYZHBaR2RsZEhNdWQybGtaMlYwTFhSaFlpQStJQzV3TFZSaFlrSmhjaUF1Y0MxVVlXSkNZWEl0ZEdGaVRHRmlaV3dnZTF4dUlDQm1iR1Y0T2lBeElERWdZWFYwYnp0Y2JpQWdiM1psY21ac2IzYzZJR2hwWkdSbGJqdGNiaUFnZDJocGRHVXRjM0JoWTJVNklHNXZkM0poY0R0Y2JuMWNibHh1WEc0dWFuVndlWFJsY2kxM2FXUm5aWFJ6TG5kcFpHZGxkQzEwWVdJZ1BpQXVjQzFVWVdKQ1lYSWdMbkF0VkdGaVFtRnlMWFJoWWk1d0xXMXZaQzFvYVdSa1pXNGdlMXh1SUNCa2FYTndiR0Y1T2lCdWIyNWxJQ0ZwYlhCdmNuUmhiblE3WEc1OVhHNWNibHh1TG1wMWNIbDBaWEl0ZDJsa1oyVjBjeTUzYVdSblpYUXRkR0ZpSUQ0Z0xuQXRWR0ZpUW1GeUxuQXRiVzlrTFdSeVlXZG5hVzVuSUM1d0xWUmhZa0poY2kxMFlXSWdlMXh1SUNCd2IzTnBkR2x2YmpvZ2NtVnNZWFJwZG1VN1hHNTlYRzVjYmx4dUxtcDFjSGwwWlhJdGQybGtaMlYwY3k1M2FXUm5aWFF0ZEdGaUlENGdMbkF0VkdGaVFtRnlMbkF0Ylc5a0xXUnlZV2RuYVc1blcyUmhkR0V0YjNKcFpXNTBZWFJwYjI0OUoyaHZjbWw2YjI1MFlXd25YU0F1Y0MxVVlXSkNZWEl0ZEdGaUlIdGNiaUFnYkdWbWREb2dNRHRjYmlBZ2RISmhibk5wZEdsdmJqb2diR1ZtZENBeE5UQnRjeUJsWVhObE8xeHVmVnh1WEc1Y2JpNXFkWEI1ZEdWeUxYZHBaR2RsZEhNdWQybGtaMlYwTFhSaFlpQStJQzV3TFZSaFlrSmhjaTV3TFcxdlpDMWtjbUZuWjJsdVoxdGtZWFJoTFc5eWFXVnVkR0YwYVc5dVBTZDJaWEowYVdOaGJDZGRJQzV3TFZSaFlrSmhjaTEwWVdJZ2UxeHVJQ0IwYjNBNklEQTdYRzRnSUhSeVlXNXphWFJwYjI0NklIUnZjQ0F4TlRCdGN5QmxZWE5sTzF4dWZWeHVYRzVjYmk1cWRYQjVkR1Z5TFhkcFpHZGxkSE11ZDJsa1oyVjBMWFJoWWlBK0lDNXdMVlJoWWtKaGNpNXdMVzF2WkMxa2NtRm5aMmx1WnlBdWNDMVVZV0pDWVhJdGRHRmlMbkF0Ylc5a0xXUnlZV2RuYVc1bklIdGNiaUFnZEhKaGJuTnBkR2x2YmpvZ2JtOXVaVHRjYm4xY2JseHVMeW9nUlc1a0lIUmhZbUpoY2k1amMzTWdLaTljYmlKZGZRPT0gKi8=", "headers": [["content-type", "text/css"]], "ok": true, "status": 200, "status_text": ""}}}, "colab_type": "code", "id": "YPeepTKKdvGY", "outputId": "f6f7f166-d6c5-49f5-a081-f2fa1f45f105"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "991c333011554cc198ea11b26ad5d322", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=5), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torch.optim as optim\n", "\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "optimizer = optim.Adam(filter(lambda p: p.requires_grad, model.parameters()), lr=0.001)\n", "loss = nn.CrossEntropyLoss()\n", "\n", "import torchbearer\n", "from torchbearer import Trial\n", "\n", "# If you have tensorboardX installed then write to tensorboard, else don't\n", "import sys\n", "if 'tensorboardX' in sys.modules:\n", "  import tensorboardX\n", "  from torchbearer.callbacks import TensorBoard\n", "  callbacks = [TensorBoard(write_batch_metrics=True)]\n", "else:\n", "  callbacks = []\n", "\n", "callbacks.append(make_grid)\n", "\n", "trial = Trial(model, optimizer, loss, metrics=['acc', 'loss'], callbacks=callbacks).to(device)\n", "trial.with_generators(train_generator=traingen, val_generator=valgen, test_generator=testgen)\n", "history = trial.run(epochs=5, verbose=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now that our model is trained, let's evaluate it, triggering our callback from earlier. Here, we again use one of torchbearers state_keys to tell it to evaluate on the (unseen) test data."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e5c7018f2a644cdd84815bb799abd092", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(e)', max=79), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"text/plain": ["{'test_acc': 0.6567999720573425, 'test_loss': 0.9761508107185364}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["trial.evaluate(data_key=torchbearer.TEST_DATA)"]}, {"cell_type": "markdown", "metadata": {"colab": {}, "colab_type": "code", "id": "r6RI63hkfWmj"}, "source": ["We can also get access to the training history of the trial (returned by the run method), if we wanted to do any post analysis. The history is a list with one entry for each epoch. Each entry contains a tuple (num_train_steps, num_val_steps) and the metric dictionary from that epoch."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[((352, 40), {'running_acc': 0.5356249809265137, 'running_loss': 1.311218023300171, 'acc': 0.44946667551994324, 'loss': 1.5274814367294312, 'val_acc': 0.5389999747276306, 'val_loss': 1.2900903224945068}), ((352, 40), {'running_acc': 0.5998437404632568, 'running_loss': 1.136667251586914, 'acc': 0.5794888734817505, 'loss': 1.1804065704345703, 'val_acc': 0.6043999791145325, 'val_loss': 1.1251872777938843}), ((352, 40), {'running_acc': 0.63671875, 'running_loss': 1.0439308881759644, 'acc': 0.630311131477356, 'loss': 1.0476974248886108, 'val_acc': 0.6241999864578247, 'val_loss': 1.0394434928894043}), ((352, 40), {'running_acc': 0.6657812595367432, 'running_loss': 0.9543967247009277, 'acc': 0.6608889102935791, 'loss': 0.9619852900505066, 'val_acc': 0.6581999659538269, 'val_loss': 0.9835954904556274}), ((352, 40), {'running_acc': 0.6818749904632568, 'running_loss': 0.9066686034202576, 'acc': 0.6882666945457458, 'loss': 0.8895869851112366, 'val_acc': 0.6611999869346619, 'val_loss': 0.9643400311470032, 'test_acc': 0.6567999720573425, 'test_loss': 0.9761508107185364})]\n"]}], "source": ["print(history)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"name": "Quickstart.ipynb", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 1}