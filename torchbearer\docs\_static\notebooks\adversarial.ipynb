{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "iaACKcYpTKZz"}, "source": ["# Adversarial Example Generation\n", "\n", "In real world applications of machine learning models an important consideration is robustness. If the model performs well only under limited conditions then it may not be suitable for use in a real world, often noisy environment. One way of determining the robustness of a model is to consider which adversarial attacks can defeat it. In this notebook we will follow the [adversarial example generation](https://pytorch.org/tutorials/beginner/fgsm_tutorial.html) from the PyTorch website and implement it in torchbearer. \n", "\n", "Before continuing it would be a good idea to look over that example and familarise yourself with the content since we won't be convering the motivation and background in as much detail, prefering to look at the implementational details. \n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.3.2\n"]}], "source": ["#%%\n", "try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "    # If problems arise, try\n", "    # pip install git+https://github.com/pytorchbearer/torchbearer\n", "    # import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Model and Data\n", "\n", "First we load the same MNIST model that was used in the PyTorch example or train a model from scratch if the file isn't available. Pretrained weights can be found [here](https://drive.google.com/drive/folders/1fn83DF14tWmit0RTKWRhPq5uVXt73e0h?usp=sharing). We then load the MNIST dataset test set. We also included some commented out code to train a new model if you prefer. "]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {}, "colab_type": "code", "id": "t-eH5WeuU7v6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["File: data/lenet_mnist_model.pth was not found, training a model from scratch instead\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b0180d4b20964e5e915ec19d28c9fdd8", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/5(t)', max=469), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0b66d62c451644358194270cc624fabf", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='1/5(t)', max=469), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "62744c13a9a445248c36b683635a5591", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='2/5(t)', max=469), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "619d0441933048dc840986e50cfcb77c", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='3/5(t)', max=469), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b2eae388b71440229867d4386195e9ac", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='4/5(t)', max=469), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torchbearer import Trial\n", "\n", "class Net(nn.Module):\n", "    def __init__(self):\n", "        super(Net, self).__init__()\n", "        self.conv1 = nn.Conv2d(1, 10, kernel_size=5)\n", "        self.conv2 = nn.Conv2d(10, 20, kernel_size=5)\n", "        self.conv2_drop = nn.Dropout2d()\n", "        self.fc1 = nn.Linear(320, 50)\n", "        self.fc2 = nn.Linear(50, 10)\n", "\n", "    def forward(self, x):\n", "        x = F.relu(F.max_pool2d(self.conv1(x), 2))\n", "        x = F.relu(F.max_pool2d(self.conv2_drop(self.conv2(x)), 2))\n", "        x = x.view(-1, 320)\n", "        x = F.relu(self.fc1(x))\n", "        x = F.dropout(x, training=self.training)\n", "        x = self.fc2(x)\n", "        return F.log_softmax(x, dim=1)\n", "\n", "\n", "import torchvision.datasets as datasets\n", "import torchvision.transforms as transforms\n", "\n", "# MNIST Test dataset and dataloader declaration\n", "test_loader = torch.utils.data.DataLoader(\n", "    datasets.MNIST('data', train=False, download=True, transform=transforms.Compose([\n", "            transforms.To<PERSON><PERSON><PERSON>(),\n", "            ])),\n", "        batch_size=1, shuffle=True)\n", "\n", "# Use cuda if possible\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# Load the pretrained model\n", "model = Net()\n", "pretrained_model = \"data/lenet_mnist_model.pth\"\n", "\n", "try:\n", "    state = torch.load(pretrained_model, map_location='cpu')\n", "    model.load_state_dict(state)\n", "except FileNotFoundError:\n", "    print('File: ' + pretrained_model + ' was not found, training a model from scratch instead')\n", "\n", "    train_loader = torch.utils.data.DataLoader(\n", "        datasets.MNIST('data', train=True, download=True, transform=transforms.Compose([\n", "                transforms.To<PERSON><PERSON><PERSON>(),\n", "                ])),\n", "            batch_size=128, shuffle=True)\n", "    from torch.optim import Adam\n", "    trial = Trial(model, optimizer=<PERSON>(model.parameters(), 3e-4), criterion=nn.CrossEntropyLoss(), metrics=['acc'])\n", "    _ = trial.with_train_generator(train_loader).to(device).run(5)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "s0T1AfbCV73U"}, "source": ["The Attack\n", "========================================\n", "\n", "The attack used in the PyTorch example is the Fast Gradient Sign Method (FGSM) attack by Goodfellow _et. al._ in [Explaining and Harnessing Adversarial Examples](https://arxiv.org/abs/1412.6572). This attack takes advantage of access to the model gradients by simply performing gradient ascent on the input to maximise the classification loss. \n", "\n", "In implementation, the attack is defined by a funciton of the original image, its gradient and an epsilon which is a learning rate for the attack. "]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {}, "colab_type": "code", "id": "mtyia_TuXdYo"}, "outputs": [], "source": ["def fgsm_attack(image, epsilon, data_grad):\n", "    # Collect the element-wise sign of the data gradient\n", "    sign_data_grad = data_grad.sign()\n", "    # Create the perturbed image by adjusting each pixel of the input image\n", "    perturbed_image = image + epsilon*sign_data_grad\n", "    # Adding clipping to maintain [0,1] range\n", "    perturbed_image = torch.clamp(perturbed_image, 0, 1)\n", "    # Return the perturbed image\n", "    return perturbed_image"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "JbBJWsQwYPnF"}, "source": ["In torchbearer, we would usually implement something like this in a callback, so that we can create a Trial with an MNIST classifier and take advantage of the training loop and metrics built in. \n", "\n", "So lets think of how we need to perform the attack.\n", "- We need to make sure the input image has gradient which we can set **on_sample**\n", "- Optionally, we can skip images which are already misclassified - which can be done **on_forward**, once we know the predictions\n", "- We need to run the attack after generating the gradients, which can be done **on_backward**\n", "- We would like to do some visualisation, which can be done **on_step_training**, but we will implement this in a separate callback for simplicity. \n", "\n", "No we have a callback structure, we shall implement it, referring to the [testing function](https://pytorch.org/tutorials/beginner/fgsm_tutorial.html#testing-function) in the original PyTorch example. "]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "oOHLhCExZ2ul"}, "outputs": [], "source": ["from torchbearer import Callback, state_key\n", "\n", "VIS_IMAGE = state_key('vis_image')\n", "PERTURBED_IMAGE = state_key('perturbed_image')\n", "\n", "class FGSA(Callback):\n", "    def __init__(self, epsilon):\n", "        self.epsilon = epsilon\n", "        self.skip = False\n", "\n", "    def on_sample(self, state):\n", "        state[torchbearer.X].requires_grad = True\n", "\n", "    def on_forward(self, state):\n", "        pred, true = state[torchbearer.Y_PRED], state[torchbearer.Y_TRUE]\n", "        pred = pred.max(1, keepdim=True)[1]\n", "        if pred != true:\n", "          # Skip already misclassified example\n", "          self.skip = True\n", "\n", "    def on_backward(self, state):\n", "        if not self.skip:\n", "          image = state[torchbearer.X]\n", "          image_grad = image.grad.data\n", "          perturbed_image = fgsm_attack(image, self.epsilon, image_grad)\n", "          state[PERTURBED_IMAGE] = torch.cat((image, perturbed_image))\n", "          # We replace the prediction so that we can use accuracy metrics easily\n", "          state[torchbearer.Y_PRED] = state[torchbearer.MODEL](perturbed_image)\n", "\n", "    def on_step_training(self, state):\n", "        # Make sure to reset the skip flag for each image\n", "        state[torchbearer.MODEL].zero_grad()\n", "        self.skip = False\n", "    "]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "uwba06KDYGGG"}, "source": ["Running and Testing the Attack\n", "==========================================\n", "\n", "Before doing any visualisation, lets run the attack and just look at how well the model classifies. We create a trial with a cross entropy loss, our attack as a callback (lets choose a large epsilon so we see a large effect) and accuracy as the only metric. If our attack works we should see a very low accuracy. We'll first run 500 steps without an attack and then 500 steps with an attack and compare this accuracy. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {}, "colab_type": "code", "id": "ycMXboNkvEiF"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "046c9eee435b40ec982418a551449b69", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(t)', max=500), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bc8d3eb875a1471ab05551163c64edff", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(t)', max=500), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["trial = Trial(model, criterion=nn.CrossEntropyLoss(), callbacks=[], metrics=['acc']).with_train_generator(test_loader, steps=500).to(device).run(1)\n", "\n", "trial = Trial(model, criterion=nn.CrossEntropyLoss(), callbacks=[FGSA(0.5)], metrics=['acc']).with_train_generator(test_loader, steps=500).to(device).run(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For our pretrained model we get an accuracy of over 90%. After attacking, we see an accuracy of below 10%. Our attack was successful. So what do our adversarial images look like? Lets add a visualisation callback that retrieves the perturbed image that we saved in state earlier. Recall that we added a pair of images under that state key, the original and the perturbed. The outputs below will show a number of these pairs. "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8c8659dfd89b42e3a908b5d444568190", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(t)', max=500), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "91f1cbb3f45040f9ac118dcde9d36531", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(t)', max=500), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3d211fd609f94445a5f00a507e6d1ee5", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(t)', max=500), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f8eed52d61fd428ca9a438a2f99e7200", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, description='0/1(t)', max=500), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["from torchbearer.callbacks import once, on_step_training\n", "from torchbearer.callbacks.imaging import MakeGrid\n", "%matplotlib inline\n", "\n", "vis = lambda x: MakeGrid(PERTURBED_IMAGE).to_pyplot().to_file('perturbed_' + str(x) + '.png').on_train()\n", "trial = Trial(model, criterion=nn.CrossEntropyLoss(), callbacks=[FGSA(0.0), vis(0.0)], metrics=['acc']).with_train_generator(test_loader, steps=500).to(device).run(1)\n", "trial = Trial(model, criterion=nn.CrossEntropyLoss(), callbacks=[FGSA(0.1), vis(0.1)], metrics=['acc']).with_train_generator(test_loader, steps=500).to(device).run(1)\n", "trial = Trial(model, criterion=nn.CrossEntropyLoss(), callbacks=[FGSA(0.2), vis(0.2)], metrics=['acc']).with_train_generator(test_loader, steps=500).to(device).run(1)\n", "trial = Trial(model, criterion=nn.CrossEntropyLoss(), callbacks=[FGSA(0.3), vis(0.3)], metrics=['acc']).with_train_generator(test_loader, steps=500).to(device).run(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The PyTorch example goes on to draw a graph of accuracy for various epsilons. We shall quickly show how we can achieve a similar thing with torchbearer. All we need to do is create a new trial on each loop iteration and grab the accuracies out of the history. We use the same plotting code as the PyTorch example.  "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 360x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "epsilons = [0, .05, .1, .15, .2, .25, .3]\n", "accuracies = []\n", "\n", "for eps in epsilons:\n", "    history = Trial(model, criterion=nn.CrossEntropyLoss(), callbacks=[FGSA(eps)], metrics=['acc'], verbose=0).with_train_generator(test_loader).to(device).run(1)\n", "    accuracies.append(history[0]['acc'])\n", "    \n", "plt.figure(figsize=(5,5))\n", "plt.plot(epsilons, accuracies, \"*-\")\n", "plt.yticks(np.arange(0, 1.1, step=0.1))\n", "plt.xticks(np.arange(0, .35, step=0.05))\n", "plt.title(\"Accuracy vs Epsilon\")\n", "plt.xlabel(\"Epsilon\")\n", "plt.ylabel(\"Accuracy\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "GPU", "colab": {"name": "Untitled1.ipynb", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.8"}}, "nbformat": 4, "nbformat_minor": 1}