{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "NYJWmpEfR9lJ"}, "source": ["# Linear Support Vector Machine (SVM)\n", "\n", "We've seen how to frame a problem as a differentiable program in the optimising functions example. \n", "Now we can take a look a more usable example; a linear Support Vector Machine (SVM). Note that the model and loss used\n", "in this guide are based on the code found [here](https://github.com/kazuto1011/svm-pytorch).\n", "\n", "**Note**: The easiest way to use this tutorial is as a colab notebook, which allows you to dive in with no setup. We recommend you enable a free GPU with\n", "\n", "> **Runtime**   →   **Change runtime type**   →   **Hardware Accelerator: GPU**\n", "\n", "## Install Torchbearer\n", "\n", "First we install torchbearer if needed. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.3.2\n"]}], "source": ["try:\n", "    import torchbearer\n", "except:\n", "    !pip install -q torchbearer\n", "    import torchbearer\n", "    \n", "print(torchbearer.__version__)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "a31Upx80S0Wf"}, "source": ["## SVM Recap\n", "\n", "Recall that an SVM tries to find the maximum margin hyperplane which separates the data classes. For a soft margin SVM\n", "where $\\textbf{x}$ is our data, we minimize:\n", "\n", "\\begin{equation}\n", "\\left[\\frac 1 n \\sum_{i=1}^n \\max\\left(0, 1 - y_i(\\textbf{w}\\cdot \\textbf{x}_i - b)\\right) \\right] + \\lambda\\lVert \\textbf{w} \\rVert^2\n", "\\end{equation}\n", "\n", "We can formulate this as an optimization over our weights $\\textbf{w}$ and bias $b$, where we minimize the\n", "hinge loss subject to a level 2 weight decay term. The hinge loss for some model outputs\n", "$z = \\textbf{w}\\textbf{x} + b$ with targets $y$ is given by:\n", "\n", "\\begin{equation}\n", "\\ell(y,z) = \\max\\left(0, 1 - yz \\right)\n", "\\end{equation}\n", "\n", "## Defining the Model\n", "\n", "Let's put this into code. First we can define our module which will project the data through our weights and offset by\n", "a bias. Note that this is identical to the function of a linear layer."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {}, "colab_type": "code", "id": "27hRy0i8Sze4"}, "outputs": [], "source": ["import torch.nn as nn\n", "\n", "class LinearSVM(nn.Module):\n", "    \"\"\"Support Vector Machine\"\"\"\n", "\n", "    def __init__(self):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.w = nn.Parameter(torch.randn(1, 2), requires_grad=True)\n", "        self.b = nn.Parameter(torch.randn(1), requires_grad=True)\n", "\n", "    def forward(self, x):\n", "        h = x.matmul(self.w.t()) + self.b\n", "        return h"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "PyfrGG7uS59t"}, "source": ["Next, we define the hinge loss function:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {}, "colab_type": "code", "id": "a-0v2QecS6YP"}, "outputs": [], "source": ["import torch\n", "\n", "def hinge_loss(y_pred, y_true):\n", "    return torch.mean(torch.clamp(1 - y_pred.t() * y_true, min=0))"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "diJonMlwS7z4"}, "source": ["Creating Synthetic Data\n", "-----------------------------------------------\n", "\n", "Now for some data, 1024 samples should do the trick. We normalise here so that our random init is in the same space as\n", "the data:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {}, "colab_type": "code", "id": "U4U7FpoiS946"}, "outputs": [], "source": ["import numpy as np\n", "from sklearn.datasets.samples_generator import make_blobs\n", "\n", "X, Y = make_blobs(n_samples=1024, centers=2, cluster_std=1.2, random_state=1)\n", "X = (X - X.mean()) / X.std()\n", "Y[np.where(Y == 0)] = -1\n", "X, Y = torch.FloatTensor(X), torch.FloatTensor(Y)"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "OR_iJX_6TJRF"}, "source": ["Visualizing the Training\n", "----------------------------------------\n", "\n", "We now aim to create a nice visualisation, such as the one below. \n", "\n", "![svmgif](https://raw.githubusercontent.com/ecs-vlc/torchbearer/master/docs/_static/img/svm_fit.gif)\n", "\n", "The code for the visualisation (using [pyplot](https://matplotlib.org/api/pyplot_api.html)) is a bit ugly but we'll\n", "try to explain it to some degree. First, we need a mesh grid `xy` over the range of our data:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {}, "colab_type": "code", "id": "9WWuOIt5TeAA"}, "outputs": [], "source": ["delta = 0.01\n", "x = np.arange(X[:, 0].min(), X[:, 0].max(), delta)\n", "y = np.arange(X[:, 1].min(), X[:, 1].max(), delta)\n", "x, y = np.meshgrid(x, y)\n", "xy = list(map(np.ravel, [x, y]))"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "Wm9gBsuzTy7t"}, "source": ["Now things get a little strange. We start by evaluating our model over the mesh grid from earlier.\n", "\n", "For our outputs $z \\in \\textbf{Z}$, we can make some observations about the decision boundary. First, that we are\n", "outside the margin if $z \\lt -1$ or $z \\gt 1$. Conversely, we are inside the margine where $z \\gt -1$\n", "or $z \\lt 1$. \n", "\n", "The next bit is a bit of a hack to get the update of the contour plot working. If a reference to the plot is already in state we just remove the old one and add a new one, otherwise we add it and show the plot. Finally, we call mypause to trigger an update. You could just use plt.pause, however, it grabs the mouse focus each time it is called which can be annoying. Instead, mypause is taken from stackoverflow.\n", "\n", "This whole process is shown in the callback below:\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {}, "colab_type": "code", "id": "QEcC8BsoTzQ9"}, "outputs": [], "source": ["from torchbearer import callbacks\n", "\n", "%matplotlib notebook\n", "import matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "@callbacks.on_step_training\n", "@callbacks.only_if(lambda state: state[torchbearer.BATCH] % 10 == 0)\n", "def draw_margin(state):\n", "    w = state[torchbearer.MODEL].w[0].detach().to('cpu').numpy()\n", "    b = state[torchbearer.MODEL].b[0].detach().to('cpu').numpy()\n", "\n", "    z = (w.dot(xy) + b).reshape(x.shape)\n", "    z[np.where(z > 1.)] = 4\n", "    z[np.where((z > 0.) & (z <= 1.))] = 3\n", "    z[np.where((z > -1.) & (z <= 0.))] = 2\n", "    z[np.where(z <= -1.)] = 1\n", "\n", "    plt.clf()\n", "    plt.scatter(x=X[:, 0], y=X[:, 1], c=\"black\", s=10)\n", "    plt.contourf(x, y, z, cmap=plt.cm.jet, alpha=0.5)\n", "    fig.canvas.draw()"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "GAdaOug0S_Nf"}, "source": ["Subgradient Descent\n", "----------------------------------------------\n", "\n", "Since we don't know that our data is linearly separable, we would like to use a soft-margin SVM. That is, an SVM for\n", "which the data does not all have to be outside of the margin. This takes the form of a weight decay term,\n", "$\\lambda\\lVert \\textbf{w} \\rVert^2$ in the above equation. This term is called weight decay because the gradient\n", "corresponds to subtracting some amount ($2\\lambda\\textbf{w}$) from our weights at each step. With torchbearer we\n", "can use the `L2WeightDecay` callback to do this. This whole process is known as subgradient descent because we\n", "only use a mini-batch (of size 32 in our example) at each step to approximate the gradient over all of the data. This is\n", "proven to converge to the minimum for convex functions such as our SVM. At this point we are ready to create and train\n", "our model:"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {}, "colab_type": "code", "id": "gpKBohTtTHdr"}, "outputs": [{"data": {"application/javascript": ["/* Put everything inside the global mpl namespace */\n", "window.mpl = {};\n", "\n", "\n", "mpl.get_websocket_type = function() {\n", "    if (typeof(WebSocket) !== 'undefined') {\n", "        return WebSocket;\n", "    } else if (typeof(MozWebSocket) !== 'undefined') {\n", "        return MozWebSocket;\n", "    } else {\n", "        alert('Your browser does not have WebSocket support.' +\n", "              'Please try Chrome, Safari or Firefox ≥ 6. ' +\n", "              'Firefox 4 and 5 are also supported but you ' +\n", "              'have to enable WebSockets in about:config.');\n", "    };\n", "}\n", "\n", "mpl.figure = function(figure_id, websocket, ondownload, parent_element) {\n", "    this.id = figure_id;\n", "\n", "    this.ws = websocket;\n", "\n", "    this.supports_binary = (this.ws.binaryType != undefined);\n", "\n", "    if (!this.supports_binary) {\n", "        var warnings = document.getElementById(\"mpl-warnings\");\n", "        if (warnings) {\n", "            warnings.style.display = 'block';\n", "            warnings.textContent = (\n", "                \"This browser does not support binary websocket messages. \" +\n", "                    \"Performance may be slow.\");\n", "        }\n", "    }\n", "\n", "    this.imageObj = new Image();\n", "\n", "    this.context = undefined;\n", "    this.message = undefined;\n", "    this.canvas = undefined;\n", "    this.rubberband_canvas = undefined;\n", "    this.rubberband_context = undefined;\n", "    this.format_dropdown = undefined;\n", "\n", "    this.image_mode = 'full';\n", "\n", "    this.root = $('<div/>');\n", "    this._root_extra_style(this.root)\n", "    this.root.attr('style', 'display: inline-block');\n", "\n", "    $(parent_element).append(this.root);\n", "\n", "    this._init_header(this);\n", "    this._init_canvas(this);\n", "    this._init_toolbar(this);\n", "\n", "    var fig = this;\n", "\n", "    this.waiting = false;\n", "\n", "    this.ws.onopen =  function () {\n", "            fig.send_message(\"supports_binary\", {value: fig.supports_binary});\n", "            fig.send_message(\"send_image_mode\", {});\n", "            if (mpl.ratio != 1) {\n", "                fig.send_message(\"set_dpi_ratio\", {'dpi_ratio': mpl.ratio});\n", "            }\n", "            fig.send_message(\"refresh\", {});\n", "        }\n", "\n", "    this.imageObj.onload = function() {\n", "            if (fig.image_mode == 'full') {\n", "                // Full images could contain transparency (where diff images\n", "                // almost always do), so we need to clear the canvas so that\n", "                // there is no ghosting.\n", "                fig.context.clearRect(0, 0, fig.canvas.width, fig.canvas.height);\n", "            }\n", "            fig.context.drawImage(fig.imageObj, 0, 0);\n", "        };\n", "\n", "    this.imageObj.onunload = function() {\n", "        fig.ws.close();\n", "    }\n", "\n", "    this.ws.onmessage = this._make_on_message_function(this);\n", "\n", "    this.ondownload = ondownload;\n", "}\n", "\n", "mpl.figure.prototype._init_header = function() {\n", "    var titlebar = $(\n", "        '<div class=\"ui-dialog-titlebar ui-widget-header ui-corner-all ' +\n", "        'ui-helper-clearfix\"/>');\n", "    var titletext = $(\n", "        '<div class=\"ui-dialog-title\" style=\"width: 100%; ' +\n", "        'text-align: center; padding: 3px;\"/>');\n", "    titlebar.append(titletext)\n", "    this.root.append(titlebar);\n", "    this.header = titletext[0];\n", "}\n", "\n", "\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "\n", "mpl.figure.prototype._root_extra_style = function(canvas_div) {\n", "\n", "}\n", "\n", "mpl.figure.prototype._init_canvas = function() {\n", "    var fig = this;\n", "\n", "    var canvas_div = $('<div/>');\n", "\n", "    canvas_div.attr('style', 'position: relative; clear: both; outline: 0');\n", "\n", "    function canvas_keyboard_event(event) {\n", "        return fig.key_event(event, event['data']);\n", "    }\n", "\n", "    canvas_div.keydown('key_press', canvas_keyboard_event);\n", "    canvas_div.keyup('key_release', canvas_keyboard_event);\n", "    this.canvas_div = canvas_div\n", "    this._canvas_extra_style(canvas_div)\n", "    this.root.append(canvas_div);\n", "\n", "    var canvas = $('<canvas/>');\n", "    canvas.addClass('mpl-canvas');\n", "    canvas.attr('style', \"left: 0; top: 0; z-index: 0; outline: 0\")\n", "\n", "    this.canvas = canvas[0];\n", "    this.context = canvas[0].getContext(\"2d\");\n", "\n", "    var backingStore = this.context.backingStorePixelRatio ||\n", "\tthis.context.webkitBackingStorePixelRatio ||\n", "\tthis.context.mozBackingStorePixelRatio ||\n", "\tthis.context.msBackingStorePixelRatio ||\n", "\tthis.context.oBackingStorePixelRatio ||\n", "\tthis.context.backingStorePixelRatio || 1;\n", "\n", "    mpl.ratio = (window.devicePixelRatio || 1) / backingStore;\n", "\n", "    var rubberband = $('<canvas/>');\n", "    rubberband.attr('style', \"position: absolute; left: 0; top: 0; z-index: 1;\")\n", "\n", "    var pass_mouse_events = true;\n", "\n", "    canvas_div.resizable({\n", "        start: function(event, ui) {\n", "            pass_mouse_events = false;\n", "        },\n", "        resize: function(event, ui) {\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "        stop: function(event, ui) {\n", "            pass_mouse_events = true;\n", "            fig.request_resize(ui.size.width, ui.size.height);\n", "        },\n", "    });\n", "\n", "    function mouse_event_fn(event) {\n", "        if (pass_mouse_events)\n", "            return fig.mouse_event(event, event['data']);\n", "    }\n", "\n", "    rubberband.mousedown('button_press', mouse_event_fn);\n", "    rubberband.mouseup('button_release', mouse_event_fn);\n", "    // Throttle sequential mouse events to 1 every 20ms.\n", "    rubberband.mousemove('motion_notify', mouse_event_fn);\n", "\n", "    rubberband.mouseenter('figure_enter', mouse_event_fn);\n", "    rubberband.mouseleave('figure_leave', mouse_event_fn);\n", "\n", "    canvas_div.on(\"wheel\", function (event) {\n", "        event = event.originalEvent;\n", "        event['data'] = 'scroll'\n", "        if (event.deltaY < 0) {\n", "            event.step = 1;\n", "        } else {\n", "            event.step = -1;\n", "        }\n", "        mouse_event_fn(event);\n", "    });\n", "\n", "    canvas_div.append(canvas);\n", "    canvas_div.append(rubberband);\n", "\n", "    this.rubberband = rubberband;\n", "    this.rubberband_canvas = rubberband[0];\n", "    this.rubberband_context = rubberband[0].getContext(\"2d\");\n", "    this.rubberband_context.strokeStyle = \"#000000\";\n", "\n", "    this._resize_canvas = function(width, height) {\n", "        // Keep the size of the canvas, canvas container, and rubber band\n", "        // canvas in synch.\n", "        canvas_div.css('width', width)\n", "        canvas_div.css('height', height)\n", "\n", "        canvas.attr('width', width * mpl.ratio);\n", "        canvas.attr('height', height * mpl.ratio);\n", "        canvas.attr('style', 'width: ' + width + 'px; height: ' + height + 'px;');\n", "\n", "        rubberband.attr('width', width);\n", "        rubberband.attr('height', height);\n", "    }\n", "\n", "    // Set the figure to an initial 600x600px, this will subsequently be updated\n", "    // upon first draw.\n", "    this._resize_canvas(600, 600);\n", "\n", "    // Disable right mouse context menu.\n", "    $(this.rubberband_canvas).bind(\"contextmenu\",function(e){\n", "        return false;\n", "    });\n", "\n", "    function set_focus () {\n", "        canvas.focus();\n", "        canvas_div.focus();\n", "    }\n", "\n", "    window.setTimeout(set_focus, 100);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items) {\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) {\n", "            // put a spacer in here.\n", "            continue;\n", "        }\n", "        var button = $('<button/>');\n", "        button.addClass('ui-button ui-widget ui-state-default ui-corner-all ' +\n", "                        'ui-button-icon-only');\n", "        button.attr('role', 'button');\n", "        button.attr('aria-disabled', 'false');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "\n", "        var icon_img = $('<span/>');\n", "        icon_img.addClass('ui-button-icon-primary ui-icon');\n", "        icon_img.addClass(image);\n", "        icon_img.addClass('ui-corner-all');\n", "\n", "        var tooltip_span = $('<span/>');\n", "        tooltip_span.addClass('ui-button-text');\n", "        tooltip_span.html(tooltip);\n", "\n", "        button.append(icon_img);\n", "        button.append(tooltip_span);\n", "\n", "        nav_element.append(button);\n", "    }\n", "\n", "    var fmt_picker_span = $('<span/>');\n", "\n", "    var fmt_picker = $('<select/>');\n", "    fmt_picker.addClass('mpl-toolbar-option ui-widget ui-widget-content');\n", "    fmt_picker_span.append(fmt_picker);\n", "    nav_element.append(fmt_picker_span);\n", "    this.format_dropdown = fmt_picker[0];\n", "\n", "    for (var ind in mpl.extensions) {\n", "        var fmt = mpl.extensions[ind];\n", "        var option = $(\n", "            '<option/>', {selected: fmt === mpl.default_extension}).html(fmt);\n", "        fmt_picker.append(option)\n", "    }\n", "\n", "    // Add hover states to the ui-buttons\n", "    $( \".ui-button\" ).hover(\n", "        function() { $(this).addClass(\"ui-state-hover\");},\n", "        function() { $(this).removeClass(\"ui-state-hover\");}\n", "    );\n", "\n", "    var status_bar = $('<span class=\"mpl-message\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "}\n", "\n", "mpl.figure.prototype.request_resize = function(x_pixels, y_pixels) {\n", "    // Request matplotlib to resize the figure. Matplotlib will then trigger a resize in the client,\n", "    // which will in turn request a refresh of the image.\n", "    this.send_message('resize', {'width': x_pixels, 'height': y_pixels});\n", "}\n", "\n", "mpl.figure.prototype.send_message = function(type, properties) {\n", "    properties['type'] = type;\n", "    properties['figure_id'] = this.id;\n", "    this.ws.send(JSON.stringify(properties));\n", "}\n", "\n", "mpl.figure.prototype.send_draw_message = function() {\n", "    if (!this.waiting) {\n", "        this.waiting = true;\n", "        this.ws.send(JSON.stringify({type: \"draw\", figure_id: this.id}));\n", "    }\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    var format_dropdown = fig.format_dropdown;\n", "    var format = format_dropdown.options[format_dropdown.selectedIndex].value;\n", "    fig.ondownload(fig, format);\n", "}\n", "\n", "\n", "mpl.figure.prototype.handle_resize = function(fig, msg) {\n", "    var size = msg['size'];\n", "    if (size[0] != fig.canvas.width || size[1] != fig.canvas.height) {\n", "        fig._resize_canvas(size[0], size[1]);\n", "        fig.send_message(\"refresh\", {});\n", "    };\n", "}\n", "\n", "mpl.figure.prototype.handle_rubberband = function(fig, msg) {\n", "    var x0 = msg['x0'] / mpl.ratio;\n", "    var y0 = (fig.canvas.height - msg['y0']) / mpl.ratio;\n", "    var x1 = msg['x1'] / mpl.ratio;\n", "    var y1 = (fig.canvas.height - msg['y1']) / mpl.ratio;\n", "    x0 = Math.floor(x0) + 0.5;\n", "    y0 = Math.floor(y0) + 0.5;\n", "    x1 = Math.floor(x1) + 0.5;\n", "    y1 = Math.floor(y1) + 0.5;\n", "    var min_x = Math.min(x0, x1);\n", "    var min_y = Math.min(y0, y1);\n", "    var width = Math.abs(x1 - x0);\n", "    var height = Math.abs(y1 - y0);\n", "\n", "    fig.rubberband_context.clearRect(\n", "        0, 0, fig.canvas.width, fig.canvas.height);\n", "\n", "    fig.rubberband_context.strokeRect(min_x, min_y, width, height);\n", "}\n", "\n", "mpl.figure.prototype.handle_figure_label = function(fig, msg) {\n", "    // Updates the figure title.\n", "    fig.header.textContent = msg['label'];\n", "}\n", "\n", "mpl.figure.prototype.handle_cursor = function(fig, msg) {\n", "    var cursor = msg['cursor'];\n", "    switch(cursor)\n", "    {\n", "    case 0:\n", "        cursor = 'pointer';\n", "        break;\n", "    case 1:\n", "        cursor = 'default';\n", "        break;\n", "    case 2:\n", "        cursor = 'crosshair';\n", "        break;\n", "    case 3:\n", "        cursor = 'move';\n", "        break;\n", "    }\n", "    fig.rubberband_canvas.style.cursor = cursor;\n", "}\n", "\n", "mpl.figure.prototype.handle_message = function(fig, msg) {\n", "    fig.message.textContent = msg['message'];\n", "}\n", "\n", "mpl.figure.prototype.handle_draw = function(fig, msg) {\n", "    // Request the server to send over a new figure.\n", "    fig.send_draw_message();\n", "}\n", "\n", "mpl.figure.prototype.handle_image_mode = function(fig, msg) {\n", "    fig.image_mode = msg['mode'];\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Called whenever the canvas gets updated.\n", "    this.send_message(\"ack\", {});\n", "}\n", "\n", "// A function to construct a web socket function for onmessage handling.\n", "// Called in the figure constructor.\n", "mpl.figure.prototype._make_on_message_function = function(fig) {\n", "    return function socket_on_message(evt) {\n", "        if (evt.data instanceof Blob) {\n", "            /* FIXME: We get \"Resource interpreted as Image but\n", "             * transferred with MIME type text/plain:\" errors on\n", "             * Chrome.  But how to set the MIME type?  It doesn't seem\n", "             * to be part of the websocket stream */\n", "            evt.data.type = \"image/png\";\n", "\n", "            /* Free the memory for the previous frames */\n", "            if (fig.imageObj.src) {\n", "                (window.URL || window.webkitURL).revokeObjectURL(\n", "                    fig.imageObj.src);\n", "            }\n", "\n", "            fig.imageObj.src = (window.URL || window.webkitURL).createObjectURL(\n", "                evt.data);\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "        else if (typeof evt.data === 'string' && evt.data.slice(0, 21) == \"data:image/png;base64\") {\n", "            fig.imageObj.src = evt.data;\n", "            fig.updated_canvas_event();\n", "            fig.waiting = false;\n", "            return;\n", "        }\n", "\n", "        var msg = JSON.parse(evt.data);\n", "        var msg_type = msg['type'];\n", "\n", "        // Call the  \"handle_{type}\" callback, which takes\n", "        // the figure and JSON message as its only arguments.\n", "        try {\n", "            var callback = fig[\"handle_\" + msg_type];\n", "        } catch (e) {\n", "            console.log(\"No handler for the '\" + msg_type + \"' message type: \", msg);\n", "            return;\n", "        }\n", "\n", "        if (callback) {\n", "            try {\n", "                // console.log(\"Handling '\" + msg_type + \"' message: \", msg);\n", "                callback(fig, msg);\n", "            } catch (e) {\n", "                console.log(\"Exception inside the 'handler_\" + msg_type + \"' callback:\", e, e.stack, msg);\n", "            }\n", "        }\n", "    };\n", "}\n", "\n", "// from http://stackoverflow.com/questions/1114465/getting-mouse-location-in-canvas\n", "mpl.findpos = function(e) {\n", "    //this section is from http://www.quirksmode.org/js/events_properties.html\n", "    var targ;\n", "    if (!e)\n", "        e = window.event;\n", "    if (e.target)\n", "        targ = e.target;\n", "    else if (e.srcElement)\n", "        targ = e.srcElement;\n", "    if (targ.nodeType == 3) // defeat Safari bug\n", "        targ = targ.parentNode;\n", "\n", "    // jQ<PERSON>y normalizes the pageX and pageY\n", "    // pageX,Y are the mouse positions relative to the document\n", "    // offset() returns the position of the element relative to the document\n", "    var x = e.pageX - $(targ).offset().left;\n", "    var y = e.pageY - $(targ).offset().top;\n", "\n", "    return {\"x\": x, \"y\": y};\n", "};\n", "\n", "/*\n", " * return a copy of an object with only non-object keys\n", " * we need this to avoid circular references\n", " * http://stackoverflow.com/a/24161582/3208463\n", " */\n", "function simple<PERSON><PERSON>s (original) {\n", "  return Object.keys(original).reduce(function (obj, key) {\n", "    if (typeof original[key] !== 'object')\n", "        obj[key] = original[key]\n", "    return obj;\n", "  }, {});\n", "}\n", "\n", "mpl.figure.prototype.mouse_event = function(event, name) {\n", "    var canvas_pos = mpl.findpos(event)\n", "\n", "    if (name === 'button_press')\n", "    {\n", "        this.canvas.focus();\n", "        this.canvas_div.focus();\n", "    }\n", "\n", "    var x = canvas_pos.x * mpl.ratio;\n", "    var y = canvas_pos.y * mpl.ratio;\n", "\n", "    this.send_message(name, {x: x, y: y, button: event.button,\n", "                             step: event.step,\n", "                             guiEvent: simpleKeys(event)});\n", "\n", "    /* This prevents the web browser from automatically changing to\n", "     * the text insertion cursor when the button is pressed.  We want\n", "     * to control all of the cursor setting manually through the\n", "     * 'cursor' event from matplotlib */\n", "    event.preventDefault();\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    // Handle any extra behaviour associated with a key event\n", "}\n", "\n", "mpl.figure.prototype.key_event = function(event, name) {\n", "\n", "    // Prevent repeat events\n", "    if (name == 'key_press')\n", "    {\n", "        if (event.which === this._key)\n", "            return;\n", "        else\n", "            this._key = event.which;\n", "    }\n", "    if (name == 'key_release')\n", "        this._key = null;\n", "\n", "    var value = '';\n", "    if (event.ctrlKey && event.which != 17)\n", "        value += \"ctrl+\";\n", "    if (event.altKey && event.which != 18)\n", "        value += \"alt+\";\n", "    if (event.shiftKey && event.which != 16)\n", "        value += \"shift+\";\n", "\n", "    value += 'k';\n", "    value += event.which.toString();\n", "\n", "    this._key_event_extra(event, name);\n", "\n", "    this.send_message(name, {key: value,\n", "                             guiEvent: simpleKeys(event)});\n", "    return false;\n", "}\n", "\n", "mpl.figure.prototype.toolbar_button_onclick = function(name) {\n", "    if (name == 'download') {\n", "        this.handle_save(this, null);\n", "    } else {\n", "        this.send_message(\"toolbar_button\", {name: name});\n", "    }\n", "};\n", "\n", "mpl.figure.prototype.toolbar_button_onmouseover = function(tooltip) {\n", "    this.message.textContent = tooltip;\n", "};\n", "mpl.toolbar_items = [[\"Home\", \"Reset original view\", \"fa fa-home icon-home\", \"home\"], [\"Back\", \"Back to previous view\", \"fa fa-arrow-left icon-arrow-left\", \"back\"], [\"Forward\", \"Forward to next view\", \"fa fa-arrow-right icon-arrow-right\", \"forward\"], [\"\", \"\", \"\", \"\"], [\"Pan\", \"Pan axes with left mouse, zoom with right\", \"fa fa-arrows icon-move\", \"pan\"], [\"Zoom\", \"Zoom to rectangle\", \"fa fa-square-o icon-check-empty\", \"zoom\"], [\"\", \"\", \"\", \"\"], [\"Download\", \"Download plot\", \"fa fa-floppy-o icon-save\", \"download\"]];\n", "\n", "mpl.extensions = [\"eps\", \"jpeg\", \"pdf\", \"png\", \"ps\", \"raw\", \"svg\", \"tif\"];\n", "\n", "mpl.default_extension = \"png\";var comm_websocket_adapter = function(comm) {\n", "    // Create a \"websocket\"-like object which calls the given IPython comm\n", "    // object with the appropriate methods. Currently this is a non binary\n", "    // socket, so there is still some room for performance tuning.\n", "    var ws = {};\n", "\n", "    ws.close = function() {\n", "        comm.close()\n", "    };\n", "    ws.send = function(m) {\n", "        //console.log('sending', m);\n", "        comm.send(m);\n", "    };\n", "    // Register the callback with on_msg.\n", "    comm.on_msg(function(msg) {\n", "        //console.log('receiving', msg['content']['data'], msg);\n", "        // Pass the mpl event to the overridden (by mpl) onmessage function.\n", "        ws.onmessage(msg['content']['data'])\n", "    });\n", "    return ws;\n", "}\n", "\n", "mpl.mpl_figure_comm = function(comm, msg) {\n", "    // This is the function which gets called when the mpl process\n", "    // starts-up an IPython Comm through the \"matplotlib\" channel.\n", "\n", "    var id = msg.content.data.id;\n", "    // Get hold of the div created by the display call when the Comm\n", "    // socket was opened in Python.\n", "    var element = $(\"#\" + id);\n", "    var ws_proxy = comm_websocket_adapter(comm)\n", "\n", "    function ondownload(figure, format) {\n", "        window.open(figure.imageObj.src);\n", "    }\n", "\n", "    var fig = new mpl.figure(id, ws_proxy,\n", "                           ondownload,\n", "                           element.get(0));\n", "\n", "    // Call onopen now - mpl needs it, as it is assuming we've passed it a real\n", "    // web socket which is closed, not our websocket->open comm proxy.\n", "    ws_proxy.onopen();\n", "\n", "    fig.parent_element = element.get(0);\n", "    fig.cell_info = mpl.find_output_cell(\"<div id='\" + id + \"'></div>\");\n", "    if (!fig.cell_info) {\n", "        console.error(\"Failed to find cell for figure\", id, fig);\n", "        return;\n", "    }\n", "\n", "    var output_index = fig.cell_info[2]\n", "    var cell = fig.cell_info[0];\n", "\n", "};\n", "\n", "mpl.figure.prototype.handle_close = function(fig, msg) {\n", "    var width = fig.canvas.width/mpl.ratio\n", "    fig.root.unbind('remove')\n", "\n", "    // Update the output cell to use the data from the current canvas.\n", "    fig.push_to_output();\n", "    var dataURL = fig.canvas.toDataURL();\n", "    // Re-enable the keyboard manager in IPython - without this line, in FF,\n", "    // the notebook keyboard shortcuts fail.\n", "    IPython.keyboard_manager.enable()\n", "    $(fig.parent_element).html('<img src=\"' + dataURL + '\" width=\"' + width + '\">');\n", "    fig.close_ws(fig, msg);\n", "}\n", "\n", "mpl.figure.prototype.close_ws = function(fig, msg){\n", "    fig.send_message('closing', msg);\n", "    // fig.ws.close()\n", "}\n", "\n", "mpl.figure.prototype.push_to_output = function(remove_interactive) {\n", "    // Turn the data on the canvas into data in the output cell.\n", "    var width = this.canvas.width/mpl.ratio\n", "    var dataURL = this.canvas.toDataURL();\n", "    this.cell_info[1]['text/html'] = '<img src=\"' + dataURL + '\" width=\"' + width + '\">';\n", "}\n", "\n", "mpl.figure.prototype.updated_canvas_event = function() {\n", "    // Tell IPython that the notebook contents must change.\n", "    IPython.notebook.set_dirty(true);\n", "    this.send_message(\"ack\", {});\n", "    var fig = this;\n", "    // Wait a second, then push the new image to the DOM so\n", "    // that it is saved nicely (might be nice to debounce this).\n", "    setTimeout(function () { fig.push_to_output() }, 1000);\n", "}\n", "\n", "mpl.figure.prototype._init_toolbar = function() {\n", "    var fig = this;\n", "\n", "    var nav_element = $('<div/>')\n", "    nav_element.attr('style', 'width: 100%');\n", "    this.root.append(nav_element);\n", "\n", "    // Define a callback function for later on.\n", "    function toolbar_event(event) {\n", "        return fig.toolbar_button_onclick(event['data']);\n", "    }\n", "    function toolbar_mouse_event(event) {\n", "        return fig.toolbar_button_onmouseover(event['data']);\n", "    }\n", "\n", "    for(var toolbar_ind in mpl.toolbar_items){\n", "        var name = mpl.toolbar_items[toolbar_ind][0];\n", "        var tooltip = mpl.toolbar_items[toolbar_ind][1];\n", "        var image = mpl.toolbar_items[toolbar_ind][2];\n", "        var method_name = mpl.toolbar_items[toolbar_ind][3];\n", "\n", "        if (!name) { continue; };\n", "\n", "        var button = $('<button class=\"btn btn-default\" href=\"#\" title=\"' + name + '\"><i class=\"fa ' + image + ' fa-lg\"></i></button>');\n", "        button.click(method_name, toolbar_event);\n", "        button.mouseover(tooltip, toolbar_mouse_event);\n", "        nav_element.append(button);\n", "    }\n", "\n", "    // Add the status bar.\n", "    var status_bar = $('<span class=\"mpl-message\" style=\"text-align:right; float: right;\"/>');\n", "    nav_element.append(status_bar);\n", "    this.message = status_bar[0];\n", "\n", "    // Add the close button to the window.\n", "    var buttongrp = $('<div class=\"btn-group inline pull-right\"></div>');\n", "    var button = $('<button class=\"btn btn-mini btn-primary\" href=\"#\" title=\"Stop Interaction\"><i class=\"fa fa-power-off icon-remove icon-large\"></i></button>');\n", "    button.click(function (evt) { fig.handle_close(fig, {}); } );\n", "    button.mouseover('Stop Interaction', toolbar_mouse_event);\n", "    buttongrp.append(button);\n", "    var titlebar = this.root.find($('.ui-dialog-titlebar'));\n", "    titlebar.prepend(buttongrp);\n", "}\n", "\n", "mpl.figure.prototype._root_extra_style = function(el){\n", "    var fig = this\n", "    el.on(\"remove\", function(){\n", "\tfig.close_ws(fig, {});\n", "    });\n", "}\n", "\n", "mpl.figure.prototype._canvas_extra_style = function(el){\n", "    // this is important to make the div 'focusable\n", "    el.attr('tabindex', 0)\n", "    // reach out to IPython and tell the keyboard manager to turn it's self\n", "    // off when our div gets focus\n", "\n", "    // location in version 3\n", "    if (IPython.notebook.keyboard_manager) {\n", "        IPython.notebook.keyboard_manager.register_events(el);\n", "    }\n", "    else {\n", "        // location in version 2\n", "        IPython.keyboard_manager.register_events(el);\n", "    }\n", "\n", "}\n", "\n", "mpl.figure.prototype._key_event_extra = function(event, name) {\n", "    var manager = IPython.notebook.keyboard_manager;\n", "    if (!manager)\n", "        manager = IPython.keyboard_manager;\n", "\n", "    // Check for shift+enter\n", "    if (event.shiftKey && event.which == 13) {\n", "        this.canvas_div.blur();\n", "        event.shiftKey = false;\n", "        // Send a \"J\" for go to next cell\n", "        event.which = 74;\n", "        event.keyCode = 74;\n", "        manager.command_mode();\n", "        manager.handle_keydown(event);\n", "    }\n", "}\n", "\n", "mpl.figure.prototype.handle_save = function(fig, msg) {\n", "    fig.ondownload(fig, null);\n", "}\n", "\n", "\n", "mpl.find_output_cell = function(html_output) {\n", "    // Return the cell and output element which can be found *uniquely* in the notebook.\n", "    // Note - this is a bit hacky, but it is done because the \"notebook_saving.Notebook\"\n", "    // IPython event is triggered only after the cells have been serialised, which for\n", "    // our purposes (turning an active figure into a static one), is too late.\n", "    var cells = IPython.notebook.get_cells();\n", "    var ncells = cells.length;\n", "    for (var i=0; i<ncells; i++) {\n", "        var cell = cells[i];\n", "        if (cell.cell_type === 'code'){\n", "            for (var j=0; j<cell.output_area.outputs.length; j++) {\n", "                var data = cell.output_area.outputs[j];\n", "                if (data.data) {\n", "                    // IPython >= 3 moved mimebundle to data attribute of output\n", "                    data = data.data;\n", "                }\n", "                if (data['text/html'] == html_output) {\n", "                    return [cell, data, j];\n", "                }\n", "            }\n", "        }\n", "    }\n", "}\n", "\n", "// Register the function which deals with the matplotlib target/channel.\n", "// The kernel may be null if the page has been refreshed.\n", "if (IPython.notebook.kernel != null) {\n", "    IPython.notebook.kernel.comm_manager.register_target('matplotlib', mpl.mpl_figure_comm);\n", "}\n"], "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<img src=\"data:image/png;base64,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\" width=\"498.88890210493145\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7d561f5a3fbe403e99432ea844814822", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(IntProgress(value=0, max=50), HTML(value='')))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}], "source": ["from torchbearer import Trial\n", "from torchbearer.callbacks import L2WeightDecay, ExponentialLR\n", "\n", "import torch.optim as optim\n", "\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "\n", "fig = plt.figure(figsize=(5, 5))\n", "\n", "svm = LinearSVM()\n", "model = Trial(svm, optim.SGD(svm.parameters(), 0.1), hinge_loss, ['loss'],\n", "              callbacks=[draw_margin, ExponentialLR(0.999, step_on_batch=True), L2WeightDecay(0.01, params=[svm.w])]).to(device)\n", "model.with_train_data(X, Y, batch_size=32)\n", "model.run(epochs=50, verbose=1)\n", "\n", "fig.savefig('svm.png', bbox_inches='tight')"]}, {"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "tZwqaO7pT0le"}, "source": ["Final Comments\n", "------------------------------------\n", "\n", "So, there you have it, a fun differentiable programming example with a live visualisation in under 100 lines of code\n", "with torchbearer. It's easy to see how this could become more useful, perhaps finding a way to use the kernel trick with\n", "the standard form of an SVM (essentially an RBF network). You could also attempt to write some code that saves the gif\n", "from earlier. We had some but it was beyond a hack, can you do better?"]}], "metadata": {"colab": {"name": "svm_linear.ipynb", "provenance": [], "version": "0.3.2"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 1}